version: v1.0
name: Android Beta Release
agent:
  machine:
    type: e1-standard-8
  containers:
    - name: main
      image: registry.semaphoreci.com/android:31-node

execution_time_limit:
  minutes: 10
blocks:
  - name: Publish Beta Release
    dependencies: []
    task:
      secrets:
        - name: Android Build Secrets
      env_vars:
        - name: BUILD_OUTPUTS
          value: ./
        - name: ANDROID_CREDENTIALS_LOCATION
          value: /android/secure/chipper-play.json
      prologue:
        commands:
          - checkout
          - export BRANCH_TAG=$(echo ${SEMAPHORE_GIT_PR_BRANCH:-$SEMAPHORE_GIT_BRANCH} | sed 's/[/<>|:&.]/-/g')
          - export ARTIFACT_NAME=android_${BRANCH_TAG}_$(git rev-parse --short HEAD).tar.gz
          - echo $ARTIFACT_NAME
          - apt-get update
          - git clone https://github.com/rbenv/rbenv.git ~/.rbenv
          - echo 'eval "$(~/.rbenv/bin/rbenv init - bash)"' >> ~/.bashrc
          - source ~/.bashrc
          - git clone https://github.com/rbenv/ruby-build.git ~/.rbenv/plugins/ruby-build
          - sem-version ruby 2.7.6 -f
          - bundle install
      jobs:
        - name: Closed Track Release
          commands:
            - artifact pull project $ARTIFACT_NAME
            - tar xzvf $ARTIFACT_NAME
            - bundle exec fastlane android betaRelease
