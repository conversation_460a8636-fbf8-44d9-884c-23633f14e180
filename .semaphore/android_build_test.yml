version: v1.0
name: Android Artifact Build
agent:
  machine:
    type: e1-standard-8
    os_image: ubuntu2004
  containers:
    - name: main
      image: 'registry.semaphoreci.com/android:31-node'

execution_time_limit:
  minutes: 45

blocks:
  - name: Install and Cache Dependencies
    dependencies: []
    task:
      secrets:
        - name: Github Package Access
      prologue:
        commands:
          - checkout
      jobs:
        - name: Node Modules Install and C<PERSON>
          commands:
            - export CHECKSUM=$(checksum package-lock.json)
            - cache has_key node-modules-$CHECKSUM || (npm ci && cache store node-modules-$CHECKSUM node_modules)

  - name: Build Android
    dependencies:
      - Install and Cache Dependencies
    task:
      secrets:
        - name: Github Package Access
        - name: Android Build Secrets
        - name: BugSnag Access
      env_vars:
        - name: BUILD_OUTPUTS
          value: ./android/app/build/outputs/artifacts/
        - name: ANDROID_CREDENTIALS_LOCATION
          value: /android/secure/chipper-play.json
        - name: GIT_SHA_SHORT
          value: echo ${SEMAPHORE_GIT_SHA::7}
      prologue:
        commands:
          - checkout
          - export CHECKSUM=$(checksum package-lock.json)
          # Setup Java 17
          - sudo apt-get update
          - sudo apt-get install -y openjdk-17-jdk
          - sudo update-alternatives --set java /usr/lib/jvm/java-17-openjdk-amd64/bin/java
          - sudo update-alternatives --set javac /usr/lib/jvm/java-17-openjdk-amd64/bin/javac
          - export JAVA_HOME=/usr/lib/jvm/java-17-openjdk-amd64
          # Setup Node
          - export GIT_SHA_SHORT=$(echo ${SEMAPHORE_GIT_SHA::7})
          - cache restore node-modules-$CHECKSUM
          - curl https://raw.githubusercontent.com/creationix/nvm/master/install.sh | bash
          - source ~/.profile
          - nvm install 18.18.2
          - nvm use
      jobs:
        - name: Fastlane Build
          commands:
            - apt-get update
            - git clone https://github.com/rbenv/rbenv.git ~/.rbenv
            - echo 'eval "$(~/.rbenv/bin/rbenv init - bash)"' >> ~/.bashrc
            - source ~/.bashrc
            - git clone https://github.com/rbenv/ruby-build.git ~/.rbenv/plugins/ruby-build
            - sem-version ruby 2.7.6 -f
            - bundle install
            - npm run fastlane-build-android-test
      epilogue:
        on_pass:
          commands:
            - export BRANCH_TAG=$(echo ${SEMAPHORE_GIT_PR_BRANCH:-$SEMAPHORE_GIT_BRANCH} | sed 's/[/<>|:&.]/-/g')
            - export ARTIFACT_NAME=android_${BRANCH_TAG}_$(git rev-parse --short HEAD)_test.tar.gz
            - echo $ARTIFACT_NAME
            - mkdir -p $BUILD_OUTPUTS
            - mv ./android/app/build/outputs/bundle/release/* $BUILD_OUTPUTS
            - tar czvf $ARTIFACT_NAME -C $BUILD_OUTPUTS .
            - artifact push project $ARTIFACT_NAME --force
promotions:
  - name: Android E2E Tests
    pipeline_file: android_e2e_tests.yml
    auto_promote:
      when: "result = 'passed' AND (branch = 'master' OR branch =~ '^release/')"
