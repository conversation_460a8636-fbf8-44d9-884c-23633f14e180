version: v1.0
name: iOS Beta Release

agent:
  machine:
    type: a2-standard-4
    os_image: macos-xcode16

execution_time_limit:
  minutes: 120

blocks:
  - name: Testflight Beta Release
    dependencies: []
    task:
      secrets:
        - name: iOS Github Access
      env_vars:
        - name: LANG
          value: en_US.UTF-8
        - name: LC_ALL
          value: en_US.UTF-8
      prologue:
        commands:
          - checkout
          - export BRANCH_TAG=$(echo ${SEMAPHORE_GIT_PR_BRANCH:-$SEMAPHORE_GIT_BRANCH} | sed 's/[/<>|:&.]/-/g')
          - export ARTIFACT_NAME=ios_${BRANCH_TAG}_$(git rev-parse --short HEAD).tar.gz
          - echo $ARTIFACT_NAME
          - sem-version ruby 2.7.6 -f
          - gem install bundler -v "$(grep -A 1 "BUNDLED WITH" Gemfile.lock | tail -n 1)"
          - bundle install
      jobs:
        - name: Beta Testers Publish
          commands:
            - artifact pull project $ARTIFACT_NAME
            - tar xzvf $ARTIFACT_NAME
            - bundle exec fastlane ios betaRelease
