version: v1.0
name: Android E2E Tests

agent:
  machine:
    type: e1-standard-8
  containers:
    - name: main
      image: 'registry.semaphoreci.com/android:31-node'

execution_time_limit:
  minutes: 10

blocks:
  - name: Android E2E Tests
    dependencies: []
    task:
      secrets:
        - name: Android Build Secrets
        - name: Maestro API Key
      env_vars:
        - name: BUNDLETOOL_VER
          value: 1.15.2
        - name: MAESTRO_PATH
          value: /ChipperNative/.maestro
        - name: APK_NAME
          value: test_app
      prologue:
        commands:
          - checkout
          - export BRANCH_TAG=$(echo ${SEMAPHORE_GIT_PR_BRANCH:-$SEMAPHORE_GIT_BRANCH} | sed 's/[/<>|:&.]/-/g')
          - export ARTIFACT_NAME=android_${BRANCH_TAG}_$(git rev-parse --short HEAD)_test.tar.gz
          - echo $ARTIFACT_NAME
          - wget https://github.com/google/bundletool/releases/download/$BUNDLETOOL_VER/bundletool-all-$BUNDLETOOL_VER.jar
          - chmod +x bundletool-all-$BUNDLETOOL_VER.jar
          - mkdir -p $HOME/tools
          - mv bundletool-all-$BUNDLETOOL_VER.jar $HOME/tools/bundletool.jar
          - curl -Ls "https://get.maestro.mobile.dev" | bash
          - source ~/.bashrc
          - artifact pull project $ARTIFACT_NAME
          - tar xzvf $ARTIFACT_NAME
          - java -jar $HOME/tools/bundletool.jar build-apks --bundle=app-release.aab --output=$APK_NAME.apks --mode=universal --ks=$CHIPPER_RELEASE_STORE_FILE --ks-pass=pass:$CHIPPER_RELEASE_STORE_PASSWORD --ks-key-alias=$CHIPPER_RELEASE_KEY_ALIAS --key-pass=pass:$CHIPPER_RELEASE_KEY_PASSWORD
          - unzip -p $APK_NAME.apks universal.apk > $APK_NAME.apk && rm $APK_NAME.apks && rm app-release.aab
      jobs:
        - name: Run Maestro Cloud tests
          commands:
            - node $HOME/$MAESTRO_PATH/runMaestro.mjs
