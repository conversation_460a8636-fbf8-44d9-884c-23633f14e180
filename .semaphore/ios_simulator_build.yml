version: v1.0
name: iOS Simulator Build

agent:
  machine:
    type: a2-standard-4
    os_image: macos-xcode16

execution_time_limit:
  minutes: 60

blocks:
  - name: Install and Cache Dependencies
    dependencies: []
    task:
      secrets:
        - name: Github Package Access
      prologue:
        commands:
          - checkout
      jobs:
        - name: <PERSON><PERSON> Modules Install and Cache
          commands:
            - export CHECKSUM=$(checksum package-lock.json)
            - cache has_key node-modules-$CHECKSUM || (npm ci && cache store node-modules-$CHECKSUM node_modules)
  - name: Build iOS
    dependencies:
      - Install and Cache Dependencies
    task:
      secrets:
        - name: Github Package Access
        - name: iOS Github Access
        - name: iOS Github RW Access
      env_vars:
        - name: LANG
          value: en_US.UTF-8
        - name: LC_ALL
          value: en_US.UTF-8
      prologue:
        commands:
          - checkout
          - export CHECKSUM=$(checksum package-lock.json)
          - cache restore node-modules-$CHECKSUM
          - curl https://raw.githubusercontent.com/creationix/nvm/master/install.sh | bash
          # Commenting out .profile for now as it is no longer on the agent plus we are not sure what it was doing.
          # - source ~/.profile
          - nvm install 18.18.2
          - nvm use
          - sem-version ruby 2.7.6 -f
          - git config --global user.email $GITHUB_EMAIL
          - git config --global user.name $GITHUB_USER_NAME
          - ssh-keyscan -H github.com >> ~/.ssh/known_hosts
          - chmod 600 ~/.ssh/semaphore_github
          - ssh-add ~/.ssh/semaphore_github
          - gem install bundler -v "$(grep -A 1 "BUNDLED WITH" Gemfile.lock | tail -n 1)"
      jobs:
        - name: Simulator Build
          commands:
            - bundle install
            - (cd ios && bundle exec pod install)
            - npm run ios-simulator-build
      epilogue:
        on_pass:
          commands:
            - export BRANCH_TAG=$(echo ${SEMAPHORE_GIT_PR_BRANCH:-$SEMAPHORE_GIT_BRANCH} | sed 's/[/<>|:&.]/-/g')
            - export ARTIFACT_NAME=ios_debug_simulator_${BRANCH_TAG}_$(git rev-parse --short HEAD).tar.gz
            - echo $ARTIFACT_NAME
            - tar czvf $ARTIFACT_NAME ~/Library/Developer/Xcode/DerivedData/ChipperNative-*/Build/Products/Debug-iphonesimulator/ChipperNative.app
            - artifact push project $ARTIFACT_NAME --force
