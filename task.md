# Task: Pass Currency Context from Account to Send Flow

## Overview
When a user taps on the cash out option from a specific currency account, we need to pass that currency through the navigation flow and pre-select it as the origin currency in the amount input screen.

## Detailed Implementation Steps

### 1. Update Type Definitions for Route Params

Update the type definitions in `src/features/money-movement/money-movement-utils/moneyMovementTypes.ts`:

```typescript
export type SendParamsList = {
  [routeConst.MONEY_MOVEMENT_SEND]: {
    currency?: string
  }
  [routeConst.MONEY_MOVEMENT_BANK_ACCOUNT_DETAILS]: {
    provider: PaymentMethod
    currency?: string
  }
  [routeConst.MONEY_MOVEMENT_MOBILE_MONEY_DETAILS]: {
    provider: PaymentMethod
    currency?: string
  }
  [routeConst.MONEY_MOVEMENT_CASH_PICKUP_DETAILS]: {
    provider: PaymentMethod
    currency?: string
  }
  // Other routes remain the same
}
```

### 2. Modify Account Action Handler to Pass Currency

Update `src/shared/hooks/useAccountActionHandlerV2.ts` to pass the currency when navigating to the send screen:

```typescript
const sendHandler = useCallback(() => {
  if (navigation?.canGoBack()) {
    navigation?.goBack()
  }
  
  // Navigate to send screen with currency context
  NavigationService.navigate(routeConst.MONEY_MOVEMENT_SEND, {
    currency: contextCurrency
  })
}, [navigation, contextCurrency])
```

### 3. Update SendHome Screen to Accept and Forward Currency

Modify `src/features/money-movement/money-movement-features/send/send-screens/home/<USER>

```typescript
export function SendHome({ navigation, route }: SendHomeScreenProps) {
  // Get currency from route params if available, otherwise use API data
  const { data: balanceData } = coreService.useBalanceQuery()
  const userCurrency = balanceData?.currency || ''
  const selectedCurrency = route.params?.currency || userCurrency
  
  // Pass the currency to the next screens in the flow
  const handleBankAccountPress = (provider: PaymentMethod) => {
    navigation.navigate(routeConst.MONEY_MOVEMENT_BANK_ACCOUNT_DETAILS, {
      provider,
      currency: selectedCurrency
    })
  }

  const handleMobileMoneyPress = (provider: PaymentMethod) => {
    navigation.navigate(routeConst.MONEY_MOVEMENT_MOBILE_MONEY_DETAILS, {
      provider,
      currency: selectedCurrency
    })
  }

  const handleCashPickupPress = (provider: PaymentMethod) => {
    navigation.navigate(routeConst.MONEY_MOVEMENT_CASH_PICKUP_DETAILS, {
      provider,
      currency: selectedCurrency
    })
  }
  
  // Rest of the component remains the same
}
```

### 4. Update Bank Account Details Screen

Modify `src/features/money-movement/money-movement-features/send/send-screens/bank-account/BankAccountDetailsV2.tsx`:

```typescript
const gotoAmountScreen = useCallback(
  (resolvedAccount: ResolvedAccount, extraParams?: Record<string, any>) => {
    const showPromoCode =
      extraParams?.serviceProvider?.code ===
      MoneyMovementAccountType.WESTERN_UNION
    
    // Get the currency from route params
    const selectedCurrency = route.params?.currency

    navigation.navigate(routeConst.MONEY_MOVEMENT_AMOUNT_SCREEN, {
      transactionType: TransactionType.SEND_TO_NON_CHIPPER,
      provider: finalProvider as NonNullableFields<Required<PaymentMethod>>,
      mode: TransactionMode.SEND,
      sendType: SendType.BANK,
      showPromoCode,
      // Pass the selected currency as originCurrency
      originCurrency: selectedCurrency,
      ...extraParams,
      recipient: {
        ...resolvedAccount
      }
    })
  },
  [navigation, finalProvider, route.params?.currency]
)
```

### 5. Update Mobile Money Details Screen

Modify `src/features/money-movement/money-movement-features/send/send-screens/mobile-money/MobileMoneyDetailV2.tsx` similarly:

```typescript
const gotoAmountScreen = useCallback(
  (resolvedAccount: ResolvedAccount, extraParams?: Record<string, any>) => {
    // Get the currency from route params
    const selectedCurrency = route.params?.currency

    navigation.navigate(routeConst.MONEY_MOVEMENT_AMOUNT_SCREEN, {
      transactionType: TransactionType.SEND_TO_NON_CHIPPER,
      provider: finalProvider as NonNullableFields<Required<PaymentMethod>>,
      mode: TransactionMode.SEND,
      sendType: SendType.MOBILE_MONEY,
      // Pass the selected currency as originCurrency
      originCurrency: selectedCurrency,
      ...extraParams,
      recipient: {
        ...resolvedAccount
      }
    })
  },
  [navigation, finalProvider, route.params?.currency]
)
```

### 6. Update Cash Pickup Details Screen

Modify `src/features/money-movement/money-movement-features/send/send-screens/cash-pickup/CashPickupDetails.tsx` similarly:

```typescript
const gotoAmountScreen = useCallback(
  (resolvedAccount: ResolvedAccount, extraParams?: Record<string, any>) => {
    // Get the currency from route params
    const selectedCurrency = route.params?.currency

    navigation.navigate(routeConst.MONEY_MOVEMENT_AMOUNT_SCREEN, {
      transactionType: TransactionType.SEND_TO_NON_CHIPPER,
      provider: finalProvider as NonNullableFields<Required<PaymentMethod>>,
      mode: TransactionMode.SEND,
      sendType: SendType.CASH_PICKUP,
      // Pass the selected currency as originCurrency
      originCurrency: selectedCurrency,
      ...extraParams,
      recipient: {
        ...resolvedAccount
      }
    })
  },
  [navigation, finalProvider, route.params?.currency]
)
```

### 7. Update Transaction Amount Screen

Modify `src/shared/features/transaction/transaction-screens/TransactionAmountScreen.tsx` to prioritize the currency from route params:

```typescript
// Initialize originCurrency with the value from route params if available
const [originCurrency, setOriginCurrency] = useState<string>(
  screenConfig?.originCurrency || primaryCurrency
)
```

### 8. Update Chipper Tag Send Screen (if applicable)

If your app allows sending to Chipper tags, update `src/features/money-movement/money-movement-features/send/send-screens/chipper-tag/ChipperTagSend.tsx`:

```typescript
const gotoAmountScreen = useCallback(
  (resolvedAccount: ResolvedAccount) => {
    // Get the currency from route params
    const selectedCurrency = route.params?.currency

    navigation.navigate(routeConst.MONEY_MOVEMENT_AMOUNT_SCREEN, {
      transactionType: TransactionType.SEND_TO_CHIPPER,
      mode: TransactionMode.SEND,
      sendType: SendType.CHIPPER_TAG,
      // Pass the selected currency as originCurrency
      originCurrency: selectedCurrency,
      recipient: {
        ...resolvedAccount
      }
    })
  },
  [navigation, route.params?.currency]
)
```

### 9. Update Cash Out Handler to Pass Currency

Modify the `cashOutHandler` in `src/shared/hooks/useAccountActionHandlerV2.ts` to pass the currency when navigating to the cash out screen:

```typescript
const cashOutHandler = useCallback(() => {
  if (navigation?.canGoBack()) {
    navigation?.goBack()
  }

  const isNGNUSDCashOut = isNGNUser && isUSDBalance

  if (isCashOutAvailable) {
    let screen: keyof typeof routeConst = routeConst.WITHDRAWAL_CASH_OUT

    if (isNGNUSDCashOut) {
      screen = routeConst.WITHDRAWAL_NG
    }

    if (
      contextCurrency === CONST.CURRENCIES.ZMW.currency ||
      (isRwfCashoutEnabled && isUSDBalance) ||
      (isUGXUser && isUSDBalance)
    ) {
      screen = routeConst.WITHDRAWAL_OPTIONS
    }

    return verification.routeToFeaturePresets.goToWithdrawals(screen, {
      currency: contextCurrency,
      primaryCurrency: withdrawalPrimaryCurrency
    })
  } else {
    // Alert shown when cashout unavailable
  }
}, [
  // dependencies including contextCurrency
])
```

### 10. Update Withdrawal Screens to Forward Currency

Modify the withdrawal screens to forward the currency parameter to the send screens when applicable. For example, in `src/features/money-movement/money-movement-features/withdrawal/withdrawal-screens/WithdrawalOptionsScreen.tsx`:

```typescript
const handleSendToBank = useCallback(() => {
  // Get currency from route params
  const selectedCurrency = route.params?.currency

  // Navigate to send screen with currency context
  navigation.navigate(routeConst.MONEY_MOVEMENT_SEND, {
    currency: selectedCurrency
  })
}, [navigation, route.params?.currency])
```

## Testing

1. Navigate to an account detail screen for a specific currency (e.g., USD)
2. Tap on the "Send" or "Cash Out" option
3. Follow the send flow to the amount input screen
4. Verify that the origin currency is pre-selected as the currency from the account (e.g., USD)

## Notes

- Make sure to handle the case where the currency parameter is undefined (fall back to user's primary currency)
- Consider adding validation to ensure the selected currency is valid for the chosen payment method
- Update any relevant tests to account for the new parameter passing
