import React, {
  use<PERSON><PERSON>back,
  useImperative<PERSON><PERSON>le,
  useMemo,
  useState
} from 'react'
import { Modal, Pressable } from 'react-native'

import {
  Box,
  Button,
  Icon,
  IconButton,
  IconProps,
  Image,
  ListLoader,
  Radio,
  Text,
  TextInput,
  defaultAnalyticEvents,
  normalize,
  screenHeight
} from '@chippercash/chipper-ui'
import { Flag } from '@chippercash/chipper-ui/src/components/blocks/form/shared/Flag'
import { BottomSheetScrollView } from '@gorhom/bottom-sheet'
import _ from 'lodash'

import { format } from '@shared/services'

import { TransactionItem, TransactionListItem } from './activity-list-v2'
import { BottomSheetContainer } from './modals/BottomSheetContainer'

type TextInputRef = React.ComponentRef<typeof TextInput>
export type SelectionInputRef = TextInputRef & {
  closeModal?: () => void
  resetSelection?: () => void
}

type BottomSheetRef = React.ComponentRef<typeof BottomSheetContainer>

export type SelectionInputProps<T = unknown> = Omit<
  React.ComponentProps<typeof TextInput>,
  'defaultValue' | 'onChange' | 'value'
> & {
  withBackground?: boolean
  options?: Option<T>[]
  showSelectButton?: boolean
  title?: string
  onSelect?: (option?: Option<T> | null) => void
  onChange?: (value?: T | null) => void
  value?: T
  autoDismissOnSelect?: boolean

  defaultValue?: Option<T> | null

  Footer?: React.ReactNode

  isLoadingOptions?: boolean

  onPress?: () => void

  getId?: (value: T) => string
}

export const SelectionInput = React.forwardRef(function SelectInput<
  T = unknown
>(
  {
    label,
    displayOnly,
    disabled,
    placeholder,
    options,
    showSelectButton = false,
    title,
    onChange,
    onSelect,
    onPress,
    value: propValue,
    defaultValue,
    Footer,
    errorMessage,
    autoDismissOnSelect = true,
    isLoadingOptions,
    isLoading,
    getId: getKey = (v: T) => String(v)
  }: SelectionInputProps<T>,
  ref: React.Ref<SelectionInputRef>
) {
  const textInputRef = React.useRef<TextInputRef>(null)
  const bottomSheetRef = React.useRef<BottomSheetRef>(null)

  const valueOption = useMemo(() => {
    return options?.find(
      option =>
        option.value &&
        propValue &&
        getKey(option.value as T) === getKey(propValue)
    )
  }, [options, propValue, getKey])

  const initialOption = useMemo(() => {
    return valueOption || defaultValue
  }, [defaultValue, valueOption])

  const [selectedOption_, setSelectedOption] = useState<
    Option<T> | null | undefined
  >(initialOption)

  const [show, setShow] = useState(false)

  const selectedOption = useMemo(() => {
    return valueOption || selectedOption_
  }, [valueOption, selectedOption_])

  const handleIsSelected = useCallback(
    (option: Option<T>) => {
      const optionValue = option.value
      const selectedValue = selectedOption?.value
      return (
        !_.isNil(selectedValue) &&
        getKey(selectedValue as T) === getKey(optionValue as T)
      )
    },
    [selectedOption, getKey]
  )

  const handleClose = useCallback(() => {
    bottomSheetRef.current?.close()
    setShow(false)
  }, [])

  useImperativeHandle(
    ref,
    // @ts-ignore
    () => {
      return {
        ...(textInputRef.current as TextInputRef),
        closeModal: handleClose,
        resetSelection: () => setSelectedOption(undefined)
      }
    },
    [handleClose]
  )

  const displayValue = useMemo(() => {
    return selectedOption?.label || selectedOption?.title
  }, [selectedOption])

  const handleSelect = useCallback(
    (option: Option<T>) => {
      if (displayOnly || disabled) {
        return
      }

      const newOption = handleIsSelected(option) ? null : option

      setSelectedOption(newOption)
      onChange?.((newOption?.value as T) || null)
      onSelect?.(newOption)
      autoDismissOnSelect && handleClose()
    },
    [
      disabled,
      autoDismissOnSelect,
      displayOnly,
      handleClose,
      handleIsSelected,
      onChange,
      onSelect
    ]
  )

  const isDisabled = displayOnly || disabled

  const handleDisplayTap = useCallback(() => {
    onPress?.()
    setShow(true)
  }, [onPress])

  return (
    <>
      <Box flexDirection="column" gap={'s8'}>
        <TextInput
          ref={textInputRef}
          accessible={true}
          label={label}
          value={displayValue}
          displayOnly
          disabled={isDisabled}
          placeholder={placeholder || 'Tap to select an option'}
          onDisplayTap={handleDisplayTap}
          onChange={() => null}
          trailingElement={
            <SelectionDisplayTrailingElement
              onPress={handleDisplayTap}
              withButton={showSelectButton}
              hasSelectedOption={!!selectedOption}
              isDisabled={isDisabled}
            />
          }
          leadingElement={
            selectedOption && (
              <SelectionLeadingElement option={selectedOption} />
            )
          }
          errorMessage={errorMessage}
          isLoading={isLoading}
        />
      </Box>

      <Modal
        visible={show}
        transparent
        animationType="fade"
        onRequestClose={handleClose}>
        <BottomSheetContainer
          ref={bottomSheetRef}
          maxDynamicContentSize={screenHeight * 0.9}
          enableDynamicSizing={true}
          animateOnMount={true}
          onClose={handleClose}>
          <BottomSheetScrollView
            contentContainerStyle={{ paddingBottom: normalize(35) }}>
            <Box gutterTop={'s12'}>
              {!!title && (
                <Box gutterHorizontal={'s24'}>
                  <Text type="h4SemiBold">{title}</Text>
                </Box>
              )}
              {errorMessage ? (
                <Box gutterHorizontal={'s24'}>
                  <Text type="bodyDefault" color="textRed">
                    {errorMessage}
                  </Text>
                </Box>
              ) : (
                <Box gutterHorizontal={'s24'}>
                  {!isLoadingOptions && !options?.length ? (
                    <Text color={'textSecondary'}>No results</Text>
                  ) : (
                    <SelectionBody
                      options={options}
                      onSelect={handleSelect}
                      onIsSelected={handleIsSelected}
                      isLoadingOptions={isLoadingOptions}
                    />
                  )}
                </Box>
              )}
              {Footer}
            </Box>
          </BottomSheetScrollView>
        </BottomSheetContainer>
      </Modal>
    </>
  )
})

type ImageProps = React.ComponentProps<typeof Image>

interface BaseOption<T = unknown> {
  title?: string
  label?: string
  subtitle?: string
  logo?: string | ImageProps['source']
  flag?: string
  icon?: Omit<IconProps, 'size' | 'customSize' | 'isLoading'>
  value: T
  kind?: string
  currency?: string
  carrier?: string
}

interface DefaultOption<T = unknown> extends BaseOption<T> {
  kind: 'default'
}

interface BalanceOption<T = unknown> extends BaseOption<T> {
  kind: 'balance'
  balance: number
  currency: string
}

interface LinkedAccountOption<T = unknown> extends BaseOption<T> {
  linkedAccountId: string | number
}

interface MomoOption<T = unknown> extends LinkedAccountOption<T> {
  kind: 'momo'
}

interface BankOption<T = unknown> extends LinkedAccountOption<T> {
  kind: 'bank'
}

interface TransactionItemOption<T = unknown> extends BaseOption<T> {
  kind: 'transaction'
  item: TransactionItem
}

export type Option<T = unknown> =
  | BaseOption<T>
  | DefaultOption<T>
  | BalanceOption<T>
  | MomoOption<T>
  | TransactionItemOption<T>
  | BankOption<T>

const LeadingElementContainer = ({
  children
}: {
  children?: React.ReactNode
}) => {
  return (
    <Box
      backgroundColor="bgLayerOne"
      borderRadius={'small'}
      width={normalize(32)}
      height={normalize(32)}
      justifyContent={'center'}
      alignItems="center"
      alignSelf={'center'}>
      {children}
    </Box>
  )
}

export const SelectionLeadingElement = ({ option }: { option: Option }) => {
  if (option.flag) {
    return (
      <LeadingElementContainer>
        <Box
          justifyContent="center"
          alignSelf="center"
          alignItems="center"
          borderRadius={'small'}>
          <Flag country={option.flag} typography={'h4'} />
        </Box>
      </LeadingElementContainer>
    )
  }

  if (option.icon) {
    return (
      <LeadingElementContainer>
        <Icon {...option.icon} customSize={normalize(24)} />
      </LeadingElementContainer>
    )
  }

  if (option.logo) {
    return (
      <LeadingElementContainer>
        <Image
          style={{
            width: normalize(24),
            height: normalize(24),
            borderRadius: normalize(4)
          }}
          source={
            typeof option.logo === 'string' ? { uri: option.logo } : option.logo
          }
          accessibilityIgnoresInvertColors
        />
      </LeadingElementContainer>
    )
  }

  return null
}

export function SelectionTrailingElement<T = unknown>({
  option,
  isSelected,
  onSelect
}: {
  option: Option<T>
  isSelected: boolean
  onSelect?: (option: Option<T>) => void
}) {
  const RadioElement = (
    <Box alignSelf="flex-start" marginTop={-4}>
      <Radio
        checked={isSelected}
        onPress={() => onSelect?.(option)}
        analyticEventName={defaultAnalyticEvents.NONE}
        activeIconColor="brandPrimary100"
      />
    </Box>
  )

  if ('kind' in option && option.kind === 'balance' && 'balance' in option) {
    return (
      <Box flexDirection="row" alignItems="flex-start" gap={'s4'}>
        <Text type="listMedium" spacing="s0">
          {format.fiat.getFormattedValue(option.balance, option.currency)}
        </Text>
        {RadioElement}
      </Box>
    )
  }

  return <>{RadioElement}</>
}

export function SelectionBody<T = unknown>({
  options,
  onIsSelected,
  onSelect,
  isLoadingOptions
}: {
  options?: Option<T>[]
  onIsSelected: (option: Option<T>) => boolean
  onSelect: (option: Option<T>) => void
  isLoadingOptions?: boolean
}) {
  if (isLoadingOptions) {
    return <ListLoader title="-" subTitle="-" number={3} />
  }

  return (
    <>
      {options?.map((option, idx) => {
        const isLast = options[options.length - 1] === option

        if (option.kind === 'transaction' && 'item' in option) {
          return (
            <TransactionListItem
              dateFormat="MMM DD, YYYY [at] h:mm a"
              key={idx}
              item={option.item as TransactionItem}
              onPress={() => onSelect?.(option)}
            />
          )
        }

        return (
          <Pressable key={String(idx)} onPress={() => onSelect?.(option)}>
            <Box
              flexDirection="row"
              gap={'s8'}
              gutterVertical={'s16'}
              borderBottomWidth={!isLast ? 'thin' : 'none'}
              borderColor={'border'}
              alignItems={'center'}
              justifyContent={'space-between'}>
              <Box flexDirection={'row'} alignItems={'center'} gap={'s8'}>
                <SelectionLeadingElement option={option} />
                <Box gap={'s4'}>
                  <Text
                    type={option.subtitle ? 'listMedium' : 'listDefault'}
                    color="textPrimary"
                    spacing="s0">
                    {option.label || option.title}
                  </Text>
                  {option.subtitle && (
                    <Text type={'listSubTitle'} color="textSecondary">
                      {option.subtitle}
                    </Text>
                  )}
                  <Box />
                </Box>
              </Box>
              <SelectionTrailingElement
                option={option}
                onSelect={onSelect}
                isSelected={onIsSelected(option)}
              />
            </Box>
          </Pressable>
        )
      })}
    </>
  )
}

const SelectionDisplayTrailingElement = ({
  withButton,
  hasSelectedOption,
  isDisabled,
  onPress
}: {
  withButton?: boolean
  hasSelectedOption?: boolean
  isDisabled?: boolean
  onPress: () => void
}) => {
  if (withButton) {
    return (
      <Box justifyContent="center" alignItems="center">
        <Button
          disabled={isDisabled}
          style={{ alignSelf: 'flex-start' }}
          isFlex={false}
          appearance="secondary"
          size="tiny"
          title={hasSelectedOption ? 'Change' : 'Select'}
          onPress={onPress}
          analyticEventName={defaultAnalyticEvents.NONE}
        />
      </Box>
    )
  }
  return (
    <IconButton
      onPress={onPress}
      analyticEventName={defaultAnalyticEvents.NONE}
      name="chevron-down"
      size="small"
    />
  )
}
