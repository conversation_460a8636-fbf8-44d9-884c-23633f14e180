import React, { FunctionComponent, useEffect, useState } from 'react'
import { Pressable, SafeAreaView, Share } from 'react-native'

import {
  Avatar,
  BottomSheet,
  Button,
  IconButton,
  Image,
  Stack,
  Text,
  defaultAnalyticEvents,
  normalize,
  useChipperUITheme
} from '@chippercash/chipper-ui'
import ImageCropPicker from 'react-native-image-crop-picker'
import styled from 'styled-components/native'
import { v4 as uuid } from 'uuid'

import {
  CONST,
  analytics,
  coreService,
  errorService,
  remoteStorage,
  verification
} from '@shared/services'
import { FeatureStatus } from '@shared/services/verification/verificationTypes'
import { handleImageCropPickerPermissionNotAllowed } from '@shared/utils'

import {
  NavigationService,
  ProfileHomeScreenProps,
  routeConst
} from '@navigation'

type NavigationProp = ProfileHomeScreenProps['navigation']

type UserProfileHeaderProps = {
  displayName: string
  navigation: NavigationProp
  avatarURL: string
  tag: string
  status: FeatureStatus
  firstName?: string
  lastName?: string
  enableShowPaymentLink: boolean
  userId: string
  nextLevel?: string
}

interface UserProfileSettingsModalProps {
  isActive: boolean
  onCancel: () => void
  onNewPhotoTap: () => void
  onEditNameTap: () => void
  onTagTap: () => void
  onShareTagPagePressed?: () => void
  enableShowPaymentLink: boolean
}

const UserProfileSettingsModal: FunctionComponent<
  UserProfileSettingsModalProps
> = ({
  isActive,
  onCancel,
  onNewPhotoTap,
  onEditNameTap,
  onTagTap,
  enableShowPaymentLink,
  onShareTagPagePressed
}) => {
  const theme = useChipperUITheme()

  return (
    <BottomSheet
      contentContainerStyle={{
        backgroundColor: theme.colors.bgBase
      }}
      show={isActive}
      onClose={onCancel}>
      <SafeAreaView>
        <StyledButton
          appearance="secondary"
          title="Choose New Photo"
          onPress={onNewPhotoTap}
          weightOverride="primary"
          analyticEventName={defaultAnalyticEvents.NONE}
        />
        <Divider />

        <StyledButton
          appearance="secondary"
          title="Edit Name"
          onPress={onEditNameTap}
          weightOverride="primary"
          analyticEventName={defaultAnalyticEvents.NONE}
        />
        <Divider />

        <StyledButton
          appearance="secondary"
          title="Change @ChipperTag"
          onPress={onTagTap}
          analyticEventName={defaultAnalyticEvents.NONE}
          weightOverride="primary"
        />
        <Divider />

        {enableShowPaymentLink && (
          <>
            <StyledButton
              appearance="secondary"
              title="Share your Payment Link"
              onPress={onShareTagPagePressed as () => void}
              analyticEventName={defaultAnalyticEvents.NONE}
              weightOverride="primary"
            />
            <Divider />
          </>
        )}

        <StyledButton
          appearance="text"
          title="Cancel"
          onPress={onCancel}
          weightOverride="primary"
          analyticEventName={defaultAnalyticEvents.NONE}
        />
      </SafeAreaView>
    </BottomSheet>
  )
}

export const UserProfileHeader: FunctionComponent<UserProfileHeaderProps> = ({
  navigation,
  displayName,
  avatarURL,
  tag,
  status,
  firstName,
  lastName,
  enableShowPaymentLink,
  nextLevel
}) => {
  const [showModal, setShowModal] = useState(false)
  useEffect(() => {
    navigation.setOptions({
      headerRight: () => (
        <Button
          style={{ alignItems: 'flex-end' }}
          title="Edit"
          appearance="text"
          size="small"
          analyticEventName={defaultAnalyticEvents.NONE}
          onPress={() => setShowModal(true)}
        />
      )
    })
  }, [navigation, setShowModal])

  const [updateProfile] = coreService.useUpdateProfileMutation()
  const { data: currentUser } = coreService.useCurrentUserQuery()

  const [avatar, setAvatar] = useState(avatarURL)

  const handleShareChipperTag = async () => {
    const url = `https://${CONST.CHIPPER_DOMAIN_NAME_ME}/@${currentUser?.tag}`

    const message = `${url}`

    await Share.share(
      {
        title: 'Join me on Chipper Cash, the future of African Payments.',
        message
      },
      {
        dialogTitle: message
      }
    )
  }

  const modalOptionTap = (callback: () => void) => {
    setShowModal(false)
    setTimeout(() => {
      callback()
    }, 500)
  }

  const handleProfileImagePress = async () => {
    try {
      const response = await ImageCropPicker.openPicker({
        width: 200,
        height: 200,
        cropping: true,
        cropperCircleOverlay: true,
        mediaType: 'photo'
      })

      const identifier = uuid()
      let selectedAvatar = response.path
      const avatarUrl = await remoteStorage.uploadProfileAvatar({
        documentId: identifier,
        pathToFile: selectedAvatar
      })

      await updateProfile({
        body: {
          firstName,
          lastName,
          avatar: avatarUrl
        }
      })

      setAvatar(avatarUrl)

      analytics.track(analytics.events.AVATAR_CHANGED)
    } catch (error: any) {
      if (error?.message === 'Required permission missing') {
        handleImageCropPickerPermissionNotAllowed(error)
      } else if (error?.message !== 'User cancelled image selection') {
        errorService.handleError(error, 'Profile Photo Upload')
      }
    }
  }

  const handleUpgradeLevel = () => {
    analytics.track(analytics.events.IDENTITY_HUB_PROFILE_HEADER_TAPPED)
    NavigationService.navigate(routeConst.PROFILE_IDENTITY_HUB_LEVELS, {
      screen: routeConst.PROFILE_IDENTITY_HUB_LEVELS
    })
  }

  const handleNamePressed = () => {
    NavigationService.navigate(routeConst.ONBOARDING_NAME_INPUT, {
      firstName,
      lastName,
      avatar: avatarURL
    })
  }

  return (
    <>
      <UserProfileSettingsModal
        isActive={showModal}
        onNewPhotoTap={() => modalOptionTap(handleProfileImagePress)}
        onEditNameTap={() => modalOptionTap(handleNamePressed)}
        onTagTap={() =>
          modalOptionTap(() =>
            NavigationService.navigate(routeConst.ONBOARDING_TAG_INPUT, { tag })
          )
        }
        onShareTagPagePressed={() =>
          modalOptionTap(() =>
            NavigationService.navigate(routeConst.TAG_SHARE_SCREEN, {})
          )
        }
        onCancel={() => {
          modalOptionTap(() => setShowModal(false))
        }}
        enableShowPaymentLink={enableShowPaymentLink}
      />
      <Stack
        spacing="s0"
        alignItems="center"
        justifyContent="center"
        gutterTop="s32">
        <Pressable hitSlop={normalize(20)} onPress={() => setShowModal(true)}>
          <Avatar
            displayName={displayName}
            size="large"
            url={avatar}
            onPress={() => setShowModal(true)}
            analyticEventName={defaultAnalyticEvents.NONE}
          />
        </Pressable>
        <WhiteContainer>
          <Pressable
            onPress={() =>
              NavigationService.navigate(routeConst.PROFILE_QRVIEWER)
            }>
            <IconButton
              name="qr"
              size="medium"
              analyticEventName={defaultAnalyticEvents.NONE}
              onPress={() =>
                NavigationService.navigate(routeConst.PROFILE_QRVIEWER)
              }
            />
          </Pressable>
        </WhiteContainer>
        <Stack
          flexDirection="row"
          spacing="s2"
          alignItems="center"
          gutterBottom="s4"
          gutterTop="s4">
          {displayName && <Text type="listDefault">{displayName}</Text>}
          {status === verification.CONST.FEATURE_STATUS.AVAILABLE && (
            <StyledImage
              accessibilityIgnoresInvertColors
              source={require('@images/verified.png')}
              resizeMode="contain"
            />
          )}
        </Stack>
        <Text type="listSubTitle" color="textSecondary">
          @{tag}
        </Text>
        <Stack
          flexDirection="row"
          spacing="s4"
          gutterBottom="s4"
          alignItems="center"
          justifyContent="center"
          gutterHorizontal="s24"
          gutterTop="s4">
          <Button
            title="Share Profile"
            appearance="text"
            size="small"
            analyticEventName={defaultAnalyticEvents.NONE}
            onPress={handleShareChipperTag}
            hasHaptic
          />
        </Stack>
        {nextLevel && (
          <Stack
            spacing="s4"
            gutterHorizontal="s24"
            gutterBottom="s12"
            fullWidth={true}>
            <Button
              title={`Upgrade to ${nextLevel}`}
              appearance="secondary"
              size="small"
              analyticEventName={defaultAnalyticEvents.NONE}
              onPress={handleUpgradeLevel}
              hasHaptic
            />
          </Stack>
        )}
      </Stack>
    </>
  )
}

const StyledImage = styled(Image)`
  width: ${({ theme }) => theme.spacing.s14};
  height: ${({ theme }) => theme.spacing.s14};
`

const WhiteContainer = styled.View`
  background-color: ${({ theme }) => theme.colors.bgBase};
  padding: 3px;
  right: -20px;
  top: -20px;
  border-radius: 10px;
  border-color: white;
`

export const Divider = styled.View`
  height: 1px;
  width: 100%;
  background-color: ${({ theme }) => theme.colors.divider};
`

export const StyledButton = styled(Button)`
  border: none;
`
