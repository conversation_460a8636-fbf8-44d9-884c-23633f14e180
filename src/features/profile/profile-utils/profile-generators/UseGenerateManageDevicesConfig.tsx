import React, { useState } from 'react'

import {
  Alert,
  defaultAnalyticEvents,
  triggerLocalNotification
} from '@chippercash/chipper-ui'
import moment from 'moment'

import { auth, coreService, countries, sca } from '@shared/services'

import { LoadingIndicator } from '../../profile-components/ProfileLoadingIndicator'
import { SectionListConfigType, SectionType } from '../profileTypes'

interface DeviceType {
  deviceId?: string
  deviceType?: string
  ipAddress?: string
  ipCity?: string
  ipCountry?: string
  osVersion?: string
  updatedAt?: string
}

const getLocation = (device: { ipCountry?: string; ipCity?: string }) => {
  const ipCountry = device?.ipCountry
  const ipCity = device?.ipCity

  const city = ipCity || 'Unknown City'
  const country =
    countries.getName(ipCountry as countries.CountryCode) || 'Unknown'

  return `${city}, ${country}`
}

const getTimestamp = (device: { updatedAt?: string }) => {
  return moment(device?.updatedAt).format('LLL')
}

interface GenerateManageDeviceConfigProps {
  userDevices?: coreService.AllDevicesApiResponse
  deviceId?: string
  refetchUserDevices: () => void
}

export const useGenerateManageDevicesConfig = ({
  userDevices,
  deviceId,
  refetchUserDevices
}: GenerateManageDeviceConfigProps): SectionListConfigType[] => {
  const devices: DeviceType[] = userDevices?.devices || []
  const [selectedUserId, setSelectedUserId] = useState<null | string>(null)

  const [unlinkDevice] = coreService.useUnlinkDeviceMutation()

  const otherDevices = devices?.filter(device => device.deviceId !== deviceId)
  const currentDevice = devices?.find(
    device => device?.deviceId === deviceId
  ) as DeviceType

  async function onDeleteDevice(device: DeviceType) {
    setSelectedUserId(device?.deviceId as string)

    const _innerOnDeleteDevice = async () => {
      await unlinkDevice({
        body: {
          deviceId: device?.deviceId
        }
      })

      const message = `${device.deviceType} near ${getLocation(
        device
      )} has been successfully removed`
      const type = 'info'

      triggerLocalNotification({ message, type })
    }

    await sca.checkPinRequiredBeforeAction(
      sca.actions.removeDevice,
      _innerOnDeleteDevice
    )

    refetchUserDevices()
    setSelectedUserId(null)
  }

  const removeDeviceAlert = (device: DeviceType) => {
    Alert.alert({
      message: 'Are you sure you want to remove this device',
      buttons: [
        {
          text: 'Cancel',
          onPress: () => {}
        },
        {
          text: 'Remove device',
          onPress: () => onDeleteDevice(device),
          style: 'destructive'
        }
      ]
    })
  }

  const otherDevicesList: SectionType[] = otherDevices?.map?.(device => {
    let lastSeen = `Last Seen ${getTimestamp(device)}`
    const location = getLocation(device)

    const isProcessing = selectedUserId === device?.deviceId

    if (isProcessing) {
      return {
        type: 'custom',
        component: <LoadingIndicator />
      }
    }

    return {
      type: 'list',
      componentProps: {
        title: `${device?.deviceType}`,
        subTitle: `Near ${location} \nLast Seen ${lastSeen}`,
        trailingElementType: 'icon',
        onPress: () => removeDeviceAlert(device),
        analyticEventName: defaultAnalyticEvents.NONE,
        trailingElementProps: {
          name: 'more-horizontal'
        }
      }
    }
  })

  function confirmSignOutAllDevices() {
    Alert.alert({
      title: 'Sign out everywhere?',
      message: 'This will sign you out of all sessions and devices.',
      buttons: [
        {
          text: 'Confirm',
          style: 'cancel',
          onPress: () => auth.onSignOut('Profile Sign Out All', true)
        },
        {
          text: 'Cancel'
        }
      ]
    })
  }

  return [
    {
      title: 'This Device',
      show: Boolean(currentDevice?.deviceId),
      data: [
        {
          type: 'list',
          componentProps: {
            title: currentDevice?.deviceType as string,
            subTitle: `Near ${getLocation(
              currentDevice
            )} \n ${getTimestamp(currentDevice)}`,
            onPress: () => removeDeviceAlert(currentDevice),
            analyticEventName: defaultAnalyticEvents.NONE,
            trailingElementType: 'none',
            hasDivider: false
          }
        }
      ]
    },
    {
      title: 'Your Other Devices',
      show: true,
      data: [
        ...otherDevicesList,
        {
          type: 'divider'
        },
        {
          type: 'button',
          componentProps: {
            title: 'Sign Out of All Devices',
            appearance: 'text',
            onPress: () => confirmSignOutAllDevices(),
            analyticEventName: defaultAnalyticEvents.NONE
          }
        }
      ]
    }
  ]
}
