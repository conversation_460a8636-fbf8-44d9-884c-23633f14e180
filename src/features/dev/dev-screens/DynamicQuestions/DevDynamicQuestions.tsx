import React, { useEffect } from 'react'
import { <PERSON><PERSON>View } from 'react-native'

import {
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  Con<PERSON>er,
  <PERSON>ack,
  Text,
  defaultAnalyticEvents
} from '@chippercash/chipper-ui'
import { useNavigation } from '@react-navigation/core'

import { DynamicFields } from '@shared/features/dynamic-form'
import { useDynamicForm } from '@shared/features/dynamic-form/dynamic-form-hooks'
import {
  BankFieldMode,
  CountrySelectionMode,
  FieldConfiguration,
  FieldKind,
  SelectFieldPresentation,
  TPinFieldMode
} from '@shared/features/dynamic-form/dynamic-form-utils'

const mockQuestions: FieldConfiguration[] = [
  {
    kind: FieldKind.SELECT_TRANSACTION,
    label: 'Select transaction (USD)',
    name: 'transactionDetails',
    required: true,
    params: {
      transactionType: {
        value: 'PAYMENT'
      },
      limit: {
        value: 120
      }
    }
  },
  {
    name: 'bank_question',
    kind: FieldKind.BANK,
    label: 'Select your bank',
    mode: BankFieldMode.SELECT,
    params: {
      country: { value: 'NG' },
      currency: { value: 'NGN' }
    },
    required: 'Bank is required'
  },
  {
    name: 'mobile_money_operator_question',
    kind: FieldKind.MOBILE_MONEY_OPERATOR,
    label: 'Select your mobile money operator',
    params: {
      country: { value: 'GH' }
    },
    required: 'Mobile money operator is required'
  },
  {
    name: 'tpin_question',
    kind: FieldKind.TPIN,
    label: 'Enter your TPIN',
    mode: TPinFieldMode.LOOKUP,
    params: {
      nrcNumber: { value: '*********' }
    },
    required: 'TPIN is required'
  },
  {
    name: 'amount_question',
    kind: FieldKind.AMOUNT,
    label: 'Enter amount',
    availableCurrencies: ['NGN', 'USD', 'GHS'],
    selectTitle: 'Select currency',
    required: 'Amount is required'
  },
  {
    name: 'text_question',
    kind: FieldKind.TEXT,
    label: 'What is your name?',
    type: 'string',
    required: 'Name is required'
  },
  {
    name: 'phone_question',
    kind: FieldKind.PHONE,
    label: 'What is your phone number?',
    required: 'Phone number is required'
  },
  {
    name: 'select_question',
    kind: FieldKind.SELECT,
    label: 'Select a color',
    title: 'Choose color',
    presentation: SelectFieldPresentation.BOTTOM_SHEET,
    options: [
      { label: 'Red', value: 'red' },
      { label: 'Green', value: 'green' },
      { label: 'Blue', value: 'blue' }
    ],
    defaultValue: 'red'
  },
  {
    name: 'multi_select_question',
    kind: FieldKind.MULTI_SELECT,
    label: 'Select multiple colors',
    title: 'Choose colors',
    options: [
      { label: 'Red', value: 'red' },
      { label: 'Green', value: 'green' },
      { label: 'Blue', value: 'blue' }
    ],
    defaultValue: ['red', 'blue'],
    showWhen: {
      or: [
        {
          fieldName: 'select_question',
          operator: 'equals',
          value: 'red'
        },
        {
          fieldName: 'select_question',
          operator: 'equals',
          value: 'green'
        }
      ]
    }
  },
  {
    name: 'country_question',
    kind: FieldKind.COUNTRY,
    label: 'Select a country',
    mode: CountrySelectionMode.MULTI,
    options: [
      { label: 'Nigeria', value: 'NG' },
      { label: 'United States', value: 'US' },
      { label: 'United Kingdom', value: 'GB' }
    ],
    defaultValue: 'NG'
  },
  {
    name: 'email_question',
    kind: FieldKind.EMAIL,
    label: 'Enter your email',
    defaultValue: {
      email: '<EMAIL>'
    },
    invalid: 'Invalid email',
    hint: 'We will send you a confirmation email'
  },
  {
    name: 'address_question',
    kind: FieldKind.ADDRESS,
    label: 'Enter your address',
    defaultValue: {
      street: '123 Main St',
      city: 'Lagos',
      region: 'LA',
      postalCode: '100001',
      country: 'NG'
    }
  },
  {
    name: 'ssn_question',
    kind: FieldKind.SSN,
    label: 'Enter your SSN',
    invalid: 'Invalid SSN'
  },
  {
    label: 'Supporting documents',
    name: 'document',
    fileType: 'PDF',
    kind: FieldKind.FILE,
    fieldProps: {
      activeState: {
        title: 'Document uploaded',
        subTitle: 'Document upload successful',
        icon: 'file'
      },
      inActiveState: {
        title: 'Supporting documents',
        subTitle: 'Upload a PDF copy of the originators transfer receipt',
        icon: 'upload-cloud'
      }
    }
  },
  {
    name: 'download_form',
    kind: FieldKind.FILE_DOWNLOAD,
    label: 'Download chargeback form',
    url: 'https://firebasestorage.googleapis.com/v0/b/chipper-cash.appspot.com/o/files%2Fform-question%2Fmerchant-service-dispute.pdf?alt=media&token=d6eff213-20e8-43bf-9e34-595120a8934a',
    filename: 'chargeback_form'
  }
]

export function DevDynamicQuestions() {
  const form = useDynamicForm({
    formKey: 'dev-dynamic-questions'
  })

  const { key, handleSubmit, reset, clear } = form

  const navigation = useNavigation()

  useEffect(() => {
    return navigation.addListener('beforeRemove', () => {
      reset()
    })
  }, [navigation, reset])

  return (
    <Container white justifyContent="flex-start">
      <ScrollView>
        <Container justifyContent="flex-start" innerScreenContainer>
          <Text>Is Valid: {String(form.isValid)}</Text>
          <DynamicFields fields={mockQuestions} formKey={key} />
        </Container>
      </ScrollView>

      <Stack gutterHorizontal="s24" gutterTop="s8" spacing="s12">
        <Button
          title="Reset"
          appearance="secondary"
          onPress={() => {
            reset()
          }}
          analyticEventName={defaultAnalyticEvents.NONE}
        />
        <Button
          title="Clear"
          appearance="secondary"
          onPress={() => {
            clear()
          }}
          analyticEventName={defaultAnalyticEvents.NONE}
        />

        <Button
          title="Submit"
          disabled={!form.isValid}
          onPress={() => {
            handleSubmit(
              values => {
                console.log('Form values:', values)
                Alert.alert({
                  title: 'Form submitted',
                  message: JSON.stringify(values)
                })
                reset()
              },
              errors => {
                console.log('Form errors:', errors)
                Alert.alert({
                  title: 'Form validation failed',
                  message: JSON.stringify(errors)
                })
              }
            )
          }}
          analyticEventName={defaultAnalyticEvents.NONE}
        />
      </Stack>
    </Container>
  )
}
