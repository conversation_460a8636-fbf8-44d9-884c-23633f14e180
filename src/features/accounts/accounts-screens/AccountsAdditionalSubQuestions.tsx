import React, { FC, useCallback, useEffect, useState } from 'react'
import { <PERSON><PERSON>, ScrollView, TouchableOpacity, View } from 'react-native'

import {
  Box,
  Button,
  Container,
  DropdownInput,
  FixedFooter,
  Icon,
  ListItem,
  Modal,
  Stack,
  Text,
  TextInput,
  alert,
  defaultAnalyticEvents,
  normalize,
  triggerLocalNotification,
   DateInput
} from '@chippercash/chipper-ui'
import { BottomSheetView } from '@gorhom/bottom-sheet'
import moment from 'moment'
import { Swipeable } from 'react-native-gesture-handler'
import Reanimated, { FadeInRight } from 'react-native-reanimated'
import styled from 'styled-components/native'

import { BottomSheetScreen } from '@shared/components'
import { DocumentSelector, FileSizeError } from '../../../shared/components/DocumentSelector'
import { analytics, complianceService, errorService } from '@shared/services'

import { AccountToS } from '@features/accounts/accounts-components/AccountToS'
import {
  useAccountToS,
  useAccountVerification
} from '@features/accounts/accounts-hooks'

import { routeConst } from '@navigation'

import { AdditionalSubQuestionsScreenProps } from '../accounts-utils'

const isValidUrl = (url: string) => {
  try {
    return !!new URL(url)
  } catch {
    return false
  }
}
export const AccountsAdditionalSubQuestions: FC<
  AdditionalSubQuestionsScreenProps
> = ({ route, navigation }) => {
  const screenConfig = route.params?.subquestions
  const currency = route.params?.currency
  const isVerificationCompleted = route.params?.isVerificationCompleted
  const [fieldsData, setFormData] = useState<Record<string, string>>({})
  const [showError, setShowError] = useState(false)
  const [showDocumentPicker, setShowDocumentPicker] = useState(false)
  const [isUploading, setIsUploading] = useState(false)
  const [documentUrls, setDocumentUrls] = useState<Record<string, string>>({})
  const [supportingDocuments, setSupportingDocuments] = useState<
    Record<
      string,
      {
        documentType: string
        key: string
        url: string
      }
    >
  >({})
  const [activeFileKey, setActiveFileKey] = useState<string>('')
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [websiteErrorMessages, setWebsiteErrorMessages] = useState('')
  const [showDocumentTypeSelector, setShowDocumentTypeSelector] =
    useState(false)
  const [selectedDocumentType, setSelectedDocumentType] = useState('')

  const { refetchComplianceAccountConfig } = useAccountVerification()

  const [submitQuestioner] =
    complianceService.useVirtualAccountQuestionnaireConfigurationMutation()

  const {
    submitToS,
    isTermsAccepted,
    areAllTermsAccepted,
    termsOfService,
    acceptTerms
  } = useAccountToS({
    currency
  })

  useEffect(() => {
    if (!screenConfig) {
      return navigation.goBack()
    }
  }, [navigation, screenConfig])

  const submitForm = useCallback(async () => {
    setShowError(false)

    if (!screenConfig?.questions?.length) {
      return
    }

    const fieldKeys = screenConfig?.questions?.map(item => item.key)

    const fileField = screenConfig?.questions.find(item => item.type === 'file')

    const dateField = screenConfig?.questions.find(item => item.type === 'date')

    const websiteFields = screenConfig?.questions.filter(
      item => item.type === 'website'
    )

    const allWebsiteFieldsValid = websiteFields?.every(field => {
      const value = fieldsData[field.key]
      return isValidUrl(value)
    })

    setWebsiteErrorMessages(
      !allWebsiteFieldsValid
        ? 'Invalid website, please enter a valid URL e.g. https://example.com/username'
        : ''
    )

    if (fileField?.key) {
      const fileFields = screenConfig?.questions.filter(
        item => item.type === 'file'
      )

      const proofOfAddressField = fileFields?.find(field => field.key === 'proofOfAddress')
      const hasProofOfAddress = proofOfAddressField && documentUrls[proofOfAddressField.key]

      const requiredFileFields = fileFields?.filter(field => {
        if (field.key === 'proofOfEarnings' && hasProofOfAddress) {
          return false
        }
        return true
      })

      const allRequiredFilesHaveUrls = requiredFileFields?.every(
        field => documentUrls[field.key]
      )

      if (!allRequiredFilesHaveUrls) {
        return setShowError(true)
      }

      fileFields?.forEach(field => {
        if (documentUrls[field.key]) {
          fieldsData[field.key] = documentUrls[field.key]
        } else if (field.key === 'proofOfEarnings' && hasProofOfAddress) {
          fieldsData[field.key] = ''
        }
      })
    }

    if (dateField?.key) {
      const dateFields = screenConfig?.questions.filter(
        item => item.type === 'date'
      )
      const allDateFieldsHaveUrls = dateFields?.every(
        field => fieldsData[field.key]
      )

      if (!allDateFieldsHaveUrls) {
        return setShowError(true)
      }

      dateFields?.forEach(field => {
        fieldsData[field.key] = fieldsData[field.key]
      })
    }

    const formattedSupportingDocuments = Object.entries(
      supportingDocuments
    ).map(([documentType, doc]) => ({
      key: documentType,
      value: doc.url
    }))
    fieldsData.documents = JSON.stringify(formattedSupportingDocuments)

    const allFieldHasValue = fieldKeys?.every(key => {
      if (key === 'proofOfEarnings' && fieldsData.proofOfAddress?.trim()?.length > 0) {
        return true
      }
      return fieldsData[key]?.trim()?.length > 0
    })

    if (!allFieldHasValue) {
      return setShowError(true)
    }

    if (isVerificationCompleted && !areAllTermsAccepted) {
      return Alert.alert('Please accept the terms of service to continue')
    }

    setIsSubmitting(true)

    fieldsData.reason = screenConfig.screenTitle ?? ''

    await submitToS()

    submitQuestioner({
      body: {
        currency,
        questionnaireData: fieldsData
      }
    })
      .unwrap()
      .then(async () => {
        await refetchComplianceAccountConfig()
        analytics.track(
          analytics.events.VIRTUAL_ACCOUNTS_ADDITIONAL_QUESTION_SUBMITTED
        )
        navigation.navigate(routeConst.ACCOUNTS_APPLICATION_SCREEN, {
          currency
        })
        triggerLocalNotification({
          message: 'Submitted successfully.',
          type: 'info'
        })
        setIsSubmitting(false)
      })
      .catch(error => {
        alert.error({
          message:
            'An error occurred saving the questionnaire please try again.'
        })
        errorService.handleQueryError(error)
        setIsSubmitting(false)
      })
  }, [
    supportingDocuments,
    areAllTermsAccepted,
    currency,
    documentUrls,
    fieldsData,
    isVerificationCompleted,
    navigation,
    refetchComplianceAccountConfig,
    screenConfig,
    submitQuestioner,
    submitToS
  ])

  if (!screenConfig) {
    return null
  }

  return (
    <Container white>
      <ScrollView
        contentContainerStyle={{
          paddingBottom: normalize(100)
        }}>
        <HeaderContainer>
          <Text type={'h3'}>{screenConfig.title}</Text>
        </HeaderContainer>

        {screenConfig.description && (
          <DescriptionContainer>
            <DescriptionIconContainer>
              <Icon name={'alert-filled'} color={'textYellow'} />
            </DescriptionIconContainer>
            <DescriptionContentContainer>
              <Text type={'bodySmall'}>{screenConfig.description}</Text>
            </DescriptionContentContainer>
          </DescriptionContainer>
        )}

        {screenConfig?.questions?.map((item, index) => {
          if (item.type === 'select') {
            let currentValue = fieldsData[item.key]

            if (item.multiple && currentValue) {
              // Convert the first letter of each word to uppercase
              currentValue = currentValue
                .split(',')
                .map((v: string) => `${v.charAt(0).toUpperCase()}${v.slice(1)}`)
                .join(', ')
            }

            return (
              <Reanimated.View
                entering={FadeInRight.delay(index * 100)}
                key={index}>
                <FieldContainer>
                  <DropdownInput
                    title={item.label}
                    label={item.label}
                    value={currentValue}
                    onChange={(value: string) => {
                      setFormData({ ...fieldsData, [item.key!]: value })
                    }}
                    placeholder={item.placeholder}
                    options={item.options || []}
                    hint={item.hint}
                    errorMessage={
                      showError && !fieldsData[item.key]?.trim()?.length
                        ? `${item.label} is required`
                        : ''
                    }
                    multiple={item.multiple}
                    onMultipleChange={(value: string) => {
                      setFormData({ ...fieldsData, [item.key!]: value })
                    }}
                  />
                </FieldContainer>
              </Reanimated.View>
            )
          }

          if (item.type === 'file') {
            const documentUrl = documentUrls[item.key]

            const hasProofOfAddress = documentUrls.proofOfAddress

            let label = documentUrl
              ? `Document for ${item.label} uploaded`
              : item.label

            if (item.key === 'proofOfEarnings' && hasProofOfAddress) {
              label = documentUrl
                ? `Document for ${item.label} uploaded`
                : `${item.label} (Optional if proof of address is provided)`
            }

            if (isUploading && activeFileKey === item.key) {
              label = 'Uploading document...'
            }

            let subTitle = documentUrl ? 'Tap to change' : item.placeholder

            if (isUploading && activeFileKey === item.key) {
              subTitle = ''
            }

            return (
              <Reanimated.View
                entering={FadeInRight.delay(index * 100)}
                key={index}>
                <ListItem
                  key={index}
                  title={label || ''}
                  leadingElementType={'icon'}
                  leadingElementProps={{
                    name: documentUrl ? 'check' : 'file',
                    withContainer: !documentUrl,
                    color: documentUrl ? 'bgSuccess' : undefined
                  }}
                  subTitle={subTitle}
                  analyticEventName={defaultAnalyticEvents.NONE}
                  onPress={() => {
                    setActiveFileKey(item.key)
                    setShowDocumentPicker(true)
                  }}
                  disabled={isUploading}
                />
                {!isUploading && showError && !documentUrl && !hasProofOfAddress ?   (
                  <FieldContainer style={{ marginTop: 8 }}>
                    <Text spacing={'s4'} color={'textRed'} type={'bodySmall'}>
                      {item.key === 'proofOfEarnings' && hasProofOfAddress
                        ? 'This document is optional if proof of address is provided'
                        : `Upload a document for ${item.label} to continue`}
                    </Text>
                  </FieldContainer>
                ) : null}
              </Reanimated.View>
            )
          }

          if (item.type === 'multiple_file_select') {
            const validValues = new Set(
              item?.options?.map(option => option.value)
            )

            const uploadedSupportingDocuments = Object.values(
              supportingDocuments
            ).filter(doc => validValues.has(doc.documentType))

            return (
              <Reanimated.View
                entering={FadeInRight.delay(index * 100)}
                key={index}>
                {uploadedSupportingDocuments.map((doc, docIndex) => {
                  let label = `Supporting document for ${doc.documentType.replace(
                    /_/g,
                    ' '
                  )} uploaded`
                  if (isUploading && activeFileKey === doc.key) {
                    label = 'Uploading document...'
                  }

                  let subTitle = 'Tap to change or swipe left to delete'
                  if (isUploading && activeFileKey === doc.key) {
                    subTitle = ''
                  }

                  return (
                    <Reanimated.View
                      entering={FadeInRight.delay(docIndex * 100)}
                      key={docIndex}>
                      <Swipeable
                        key={index}
                        overshootRight={false}
                        enableTrackpadTwoFingerGesture={true}
                        renderRightActions={() => (
                          <SwipeableActionButtonContainer key={index}>
                            <SwipeableActionButton
                              key={index}
                              disabled={isSubmitting}
                              onPress={() => {
                                setSupportingDocuments(prev => {
                                  const newSupportingDocuments = { ...prev }
                                  delete newSupportingDocuments[doc.key]
                                  return newSupportingDocuments
                                })
                              }}>
                              <Icon key={index} name="trash" color="bgDanger" />
                            </SwipeableActionButton>
                          </SwipeableActionButtonContainer>
                        )}>
                        <ListItem
                          key={docIndex}
                          title={label}
                          titleProps={{
                            type: 'label'
                          }}
                          leadingElementType={'icon'}
                          leadingElementProps={{
                            name: 'file'
                          }}
                          subTitle={subTitle}
                          analyticEventName={defaultAnalyticEvents.NONE}
                          onPress={() => {
                            setActiveFileKey(doc.key)
                            setSelectedDocumentType(doc.documentType)
                            setShowDocumentPicker(true)
                          }}
                          trailingElementType={'none'}
                          trailingElementProps={{
                            name: 'trash',
                            color: 'bgDanger'
                          }}
                          disabled={isUploading}
                          hasDivider={true}
                        />
                      </Swipeable>
                    </Reanimated.View>
                  )
                })}
                <ListItem
                  key={index}
                  title={'Upload supporting documents'}
                  titleProps={{
                    type: 'listMedium'
                  }}
                  leadingElementType={'icon'}
                  leadingElementProps={{
                    name: 'plus',
                    withContainer: true
                  }}
                  analyticEventName={defaultAnalyticEvents.NONE}
                  onPress={() => {
                    setActiveFileKey(item.key)
                    setShowDocumentTypeSelector(true)
                  }}
                  disabled={isUploading}
                  hasDivider={false}
                />
              </Reanimated.View>
            )
          }

          if (item.type === 'date') {
            const selectedDate = fieldsData[item.key]
              ? new Date(fieldsData[item.key])
              : null

            return (
              <Reanimated.View
                entering={FadeInRight.delay(index * 100)}
                key={index}>
                <Box gutterHorizontal={'s24'}>
                  <Text type="label">{item.label}</Text>
                  <DateInput
                    testID="dateTimePicker"
                    value={selectedDate || new Date()}
                    onChange={(date?: Date) => {
                      if (date) {
                        setFormData({
                          ...fieldsData,
                          [item.key!]: moment(date).format('YYYY-MM-DD')
                        })
                      }
                    }}
                    disabled={isSubmitting}
                    maximumDate={moment().toDate()}
                  />
                </Box>

                {!isSubmitting && showError && !selectedDate ? (
                  <FieldContainer>
                    <Text spacing={'s4'} color={'textRed'} type={'hint'}>
                      `{item.label} is required`
                    </Text>
                  </FieldContainer>
                ) : null}
              </Reanimated.View>
            )
          }

          const currentValue = fieldsData[item.key]?.trim()
          const isWebsite = item.type === 'website'

          const errorMessage = isWebsite
            ? websiteErrorMessages
            : `${item.label} is required`

          let showErrorMessage = showError && !currentValue?.length

          if (isWebsite) {
            showErrorMessage = !!websiteErrorMessages
          }

          return (
            <Reanimated.View
              entering={FadeInRight.delay(index * 100)}
              key={index}>
              <FieldContainer>
                <TextInput
                  label={item.label}
                  value={fieldsData[item.key]}
                  onChange={(value: string) => {
                    setFormData({ ...fieldsData, [item.key!]: value })
                  }}
                  placeholder={item.placeholder}
                  hint={item.hint}
                  errorMessage={showErrorMessage ? errorMessage : ''}
                  style={{ marginBottom: item.hint ? normalize(14) : 0 }}
                />
              </FieldContainer>
            </Reanimated.View>
          )
        })}
        {isVerificationCompleted ? (
          <Stack gutterHorizontal={'s24'} gutterTop={'s24'}>
            <Reanimated.View
              entering={FadeInRight.delay(
                Number(screenConfig?.questions?.length) * 100
              )}>
              {termsOfService.map(term => {
                if (term.id) {
                  return (
                    <AccountToS
                      key={term.id}
                      data={term}
                      currency={currency}
                      handlePress={() => acceptTerms(term.id)}
                      accepted={isTermsAccepted(term.id)}
                    />
                  )
                }
              })}
            </Reanimated.View>
          </Stack>
        ) : null}
      </ScrollView>
      <FixedFooter>
        <Button
          disabled={isUploading}
          onPress={submitForm}
          analyticEventName={defaultAnalyticEvents.NONE}
          title={'Continue'}
          loading={isSubmitting}
        />
      </FixedFooter>
      {showDocumentPicker && (
        <DocumentSelector
          minFileSizeKB={11}
          handleFileSizeError={true}
          visible={showDocumentPicker}
          setLoadingState={isLoading => {
            setIsUploading(isLoading)
          }}
          onClose={() => {
            setShowDocumentPicker(false)
            setActiveFileKey('')
          }}
          onDocumentUploadToRemoteStorage={(result: string | FileSizeError) => {
            // Check if the result is a string (successful upload) or an error object
            if (typeof result === 'string') {
              // Handle successful upload
              setSupportingDocuments(prev => ({
                ...prev,
                [`${selectedDocumentType}`]: {
                  documentType: selectedDocumentType,
                  key: selectedDocumentType,
                  url: result
                }
              }))
              setDocumentUrls(prev => ({
                ...prev,
                [activeFileKey]: result
              }))
              setIsUploading(false)
              setShowDocumentPicker(false)
              setActiveFileKey('')
              setSelectedDocumentType('')
            } else {
              setIsUploading(false)
              setShowDocumentPicker(false)

              if (result.validationType === 'max') {
                alert.error({
                  message: `File too large. Maximum size is ${result.maxSize}KB, but your file is ${result.actualSize}KB.`
                })
              } else if (result.validationType === 'min') {
                alert.error({
                  message: `File too small. Minimum size is ${result.minSize}KB, but your file is ${result.actualSize}KB.`
                })
              } else {
                alert.error({
                  message: `File size validation failed: ${result.error}`
                })
              }
            }
          }}
        />
      )}
      <Modal visible={showDocumentTypeSelector} transparent>
        <BottomSheetScreen
          bottomSheetProps={{
            index: showDocumentTypeSelector ? 0 : -1,
            snapPoints: ['75%'],
            onClose: () => {
              setShowDocumentTypeSelector(false)
            }
          }}
          childComponent={() => (
            <BottomSheetView>
              <Box gutterHorizontal={'s24'} gutterTop={'s16'}>
                <Text type="h3SemiBold">Select document type to upoad</Text>
              </Box>
              {screenConfig?.questions && (
                <ScrollView
                  showsVerticalScrollIndicator={false}
                  contentContainerStyle={{ paddingBottom: normalize(120) }}>
                  {screenConfig.questions
                    .find(q => q.type === 'multiple_file_select')
                    ?.options?.map((option, index) => (
                      <ListItem
                        key={index}
                        title={option.label}
                        onPress={() => {
                          setSelectedDocumentType(option.value)
                          setActiveFileKey(`${activeFileKey}`)
                          setShowDocumentTypeSelector(false)
                          setShowDocumentPicker(true)
                        }}
                        analyticEventName={defaultAnalyticEvents.NONE}
                        hasDivider={true}
                      />
                    ))}
                </ScrollView>
              )}
            </BottomSheetView>
          )}
        />
      </Modal>
    </Container>
  )
}

const FieldContainer = styled(View)`
  padding-horizontal: ${({ theme }) => theme.spacing.s24};
`
const HeaderContainer = styled.View`
  padding-vertical: ${({ theme }) => theme.spacing.s16};
  padding-horizontal: ${({ theme }) => theme.spacing.s24};
`

const DescriptionContainer = styled.View`
  flex-direction: row;
  align-items: flex-start;
  padding-horizontal: ${({ theme }) => theme.spacing.s24};
  margin-bottom: ${({ theme }) => theme.spacing.s16};
`

const DescriptionIconContainer = styled.View`
  margin-right: ${({ theme }) => theme.spacing.s12};
  padding: 6px;
  border-radius: 4px;
  background-color: ${({ theme }) => theme.colors.bgLayerOne};
`
const DescriptionContentContainer = styled.View`
  flex-direction: row;
  flex: 1;
`
const SwipeableActionButtonContainer = styled(Swipeable)`
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
`

const SwipeableActionButton = styled(TouchableOpacity)`
  margin-right: 30px;
  height: 100%;
  justify-content: center;
  align-items: center;
  background-color: ${({ theme }) => theme.colors.bgBase};
`
