import React, { use<PERSON><PERSON>back, useEffect, useMemo, useState } from 'react'
import { FlatList, Modal, RefreshControl, ScrollView } from 'react-native'

import {
  Alert,
  Box,
  ButtonGroup,
  Container,
  ErrorMessage,
  FixedFooter,
  ListItem,
  Spinner,
  Stack,
  Text,
  defaultAnalyticEvents,
  normalize,
  theme
} from '@chippercash/chipper-ui'
import { BottomSheetView } from '@gorhom/bottom-sheet'
import styled from 'styled-components/native'

import { BottomSheetScreen } from '@shared/components'
import {
  useExperimentFeature,
  useFetchPaymentMethodsConfig,
  useFetchUserBalances,
  useInsufficientBalanceHandler,
  usePrimaryCurrency
} from '@shared/hooks'
import {
  CONST,
  analytics,
  authService,
  complianceService,
  coreService,
  errorService,
  format,
  sca,
  support
} from '@shared/services'
import {
  CHIPPER_BALANCE,
  MANAGE_MOMO_PASSTHROUGH_PAYMENT_METHODS_TRIGGER,
  providerLogoUrls
} from '@shared/services/constants/constants'
import {
  BalanceKind,
  PaymentMethodType,
  USRegulatoryDocTypes
} from '@shared/types'
import { AuthModes, OtpMethod } from '@shared/utils'

import { NavigationService, routeConst } from '@navigation'

import { AccountToS } from '../accounts-components/AccountToS'
import { AccountClaimButton } from '../accounts-components/AccountsClaimButton'
import { useAccountToS, useAccountVerification } from '../accounts-hooks'
import { AccountApplicationScreenProps } from '../accounts-utils/accountsTypes'

export const AccountsApplicationScreen = ({
  navigation,
  route
}: AccountApplicationScreenProps) => {
  const { currency } = route.params
  const { refetch: refetchAddress } = complianceService.useGetAddressQuery()
  const { refetch: refetchAuthMethods } =
    authService.useGetAllAuthMethodsQuery()
  const { refetch: refetchAccountConfigurtion } =
    coreService.useAccountConfigurationQuery()
  const { refetch: refetchUserInfo } = complianceService.useGetUserInfoQuery()
  const [updateAddress] = complianceService.useUpdateUserAddressMutation()
  const balanceQuery = coreService.useBalanceQuery()
  const isUSDPrimaryCurrency =
    balanceQuery.data?.currency === CONST.CURRENCIES.USD.currency

  const { data, isLoading: isLoadingConfig } =
    coreService.useGetVirtualAccountsConfigurationQuery()
  const [createAccount] =
    coreService.chipperVirtualAccountsApi.useCreateVirtualAccountMutation()
  const { balances } = useFetchUserBalances()
  const insufficientBalanceHandler = useInsufficientBalanceHandler()

  const [showError, setShowError] = useState(false)

  const [isSubmitting, setIsSubmitting] = useState(false)

  const [isPaymentMethodModalOpen, setIsPaymentMethodModalOpen] =
    useState(false)
  const [isNameConfirmationModalOpen, setIsNameConfirmationModalOpen] =
    useState(false)

  const {
    submitToS,
    isTermsAccepted,
    areAllTermsAccepted,
    isLoading: isLoadingTerms,
    termsOfService,
    acceptTerms
  } = useAccountToS({
    currency
  })

  const { data: currentUser } = coreService.useCurrentUserQuery()

  const {
    isDocumentUploadPending,
    isLoading: isLoadingAccountConfig,
    isDocumentUploadRejected,
    refetchComplianceAccountConfig,
    supportedDocuments,
    isDocumentUploadRequired,
    isVerificationCompleted,
    isLimitReached,
    isQuestionPendingReview,
    isQuestionCompleted,
    getDocumentUploadInfo,
    getEmailAddressInfo,
    getAddressInfo,
    getPhoneNumberInfo,
    getAdditionalQuestionInfo,
    getSSNInfo,
    getMaverickInfo,
    isHasIdentityHub,
    isMaverickUpdateRequired
  } = useAccountVerification()

  const { paymentMethods } = useFetchPaymentMethodsConfig()
  const momoPassthroughFeature = useExperimentFeature(
    CONST.EXPERIMENT_KEYS.MOMO_PASSTHROUGH
  )

  const primaryCurrency = usePrimaryCurrency()

  const canUseMomoPassthrough = useMemo(() => {
    const { enabled, payload } = momoPassthroughFeature || {}

    // return early when feature isn't enabled
    if (!enabled || !payload?.features?.length) {
      return false
    }

    const { currencies: enabledCurrencies, features: enabledFeatures } =
      payload || {}

    return (
      enabled &&
      enabledCurrencies?.includes?.(primaryCurrency) &&
      enabledFeatures?.includes?.('VIRTUAL_ACCOUNT_CREATION_FEES')
    )
  }, [momoPassthroughFeature, primaryCurrency])

  const availableBalances = useMemo(() => {
    const availableMobileMoneyPaymentMethods =
      paymentMethods?.filter(
        item =>
          item.is_external === false &&
          item.is_verified === true &&
          item.marked_as_malicious === false &&
          item.type === PaymentMethodType.MOBILE_MONEY
      ) || []

    const chipperPaymentMethod = {
      display_name: CHIPPER_BALANCE.title,
      type: BalanceKind.Balance,
      mobile_money_phone: CHIPPER_BALANCE.subTitle,
      mobile_money_carrier: null
    }

    availableMobileMoneyPaymentMethods.unshift(chipperPaymentMethod as any)

    const addPaymentMethodOption = {
      display_name: MANAGE_MOMO_PASSTHROUGH_PAYMENT_METHODS_TRIGGER.title,
      type: undefined,
      mobile_money_phone: '',
      id: null,
      mobile_money_carrier: null
    }

    return [...availableMobileMoneyPaymentMethods, addPaymentMethodOption]
  }, [paymentMethods])

  const virtualAccountConfig = useMemo(() => data?.[currency], [data, currency])
  const documentInfo = getDocumentUploadInfo()
  const addressInfo = getAddressInfo()
  const phoneNumberInfo = getPhoneNumberInfo()
  const emailAddressInfo = getEmailAddressInfo()
  const additionalQuestionInfo = getAdditionalQuestionInfo()
  const socialSecurityInfo = getSSNInfo()
  const maverickInfo = getMaverickInfo()

  const isCheckPassed = useMemo(() => {
    if (isUSDPrimaryCurrency) {
      return isVerificationCompleted
    }

    return (
      isVerificationCompleted &&
      !isQuestionCompleted &&
      !isQuestionPendingReview
    )
  }, [
    isUSDPrimaryCurrency,
    isVerificationCompleted,
    isQuestionCompleted,
    isQuestionPendingReview
  ])

  const refreshDocuments = useCallback(async () => {
    if (isDocumentUploadPending) {
      // Fetch the compliance account config
      refetchComplianceAccountConfig()
    }
  }, [isDocumentUploadPending, refetchComplianceAccountConfig])

  useEffect(() => {
    refetchUserInfo()
    refetchAuthMethods()
    refetchAddress()
    refetchComplianceAccountConfig()
  }, [
    refetchAddress,
    refetchAuthMethods,
    refetchComplianceAccountConfig,
    refetchUserInfo,
    refreshDocuments
  ])

  const feeCurrency = useMemo(
    () => virtualAccountConfig?.fee?.currency,
    [virtualAccountConfig?.fee?.currency]
  )

  const feeAmount = useMemo(
    () => virtualAccountConfig?.fee?.amount,
    [virtualAccountConfig?.fee?.amount]
  )

  const formattedFeeAmount = format.fiat.getFormattedValue(
    feeAmount,
    feeCurrency,
    {
      useCurrencyAsSymbol: false
    }
  )

  const contactSupport = useCallback(async () => {
    await support.handleSpecificRequest()
  }, [])

  const hasSufficientBalance = useMemo(() => {
    if (!feeCurrency || !feeAmount) {
      return true
    }

    const walletBalance = Number(balances?.[feeCurrency]?.availableBalance)

    const virtualAccountFee = Number(virtualAccountConfig?.fee?.amount)

    return walletBalance >= virtualAccountFee
  }, [balances, feeAmount, feeCurrency, virtualAccountConfig?.fee?.amount])

  const requestVirtualAccount = useCallback(
    async (source: { type: BalanceKind; linkedAccountId?: number }) => {
      setIsSubmitting(true)
      try {
        await submitToS()

        const response = await createAccount({
          body: {
            currency,
            source
          }
        }).unwrap()

        await refetchAccountConfigurtion()

        if (source.type === BalanceKind.Balance) {
          // creating VA via balance typically returns the account created, on rare case it doesn't we should let the user know
          if (!response?.account) {
            Alert.alert({
              title: 'Account creation in progress',
              message:
                "Account creation is currently in progress, you'll be notified when it's ready.",
              options: {
                cancelable: false
              },
              buttons: [
                {
                  text: 'OK',
                  onPress: () => {
                    navigation.push(routeConst.HOME)
                  }
                }
              ]
            })
            return
          }
          navigation.replace(routeConst.ACCOUNTS_DETAILS_SCREEN, {
            currency
          })
        } else {
          navigation.replace(routeConst.ACCOUNTS_CONFIRMATION_SCREEN, {
            currency
          })
        }
      } catch (error) {
        errorService.handleQueryError(error)
      } finally {
        setIsSubmitting(false)
      }
    },
    [createAccount, currency, navigation, refetchAccountConfigurtion, submitToS]
  )

  const handleCreateAccountViaBalance = useCallback(async () => {
    if (!hasSufficientBalance) {
      return insufficientBalanceHandler({
        asset: feeCurrency!,
        message: `There is a ${formattedFeeAmount} fee for claiming a ${currency} account, please top up your balance.`,
        redirect: {
          route: routeConst.ACCOUNTS_APPLICATION_SCREEN,
          params: {
            currency
          }
        }
      })
    }

    await requestVirtualAccount({
      type: BalanceKind.Balance
    })
  }, [
    currency,
    feeCurrency,
    formattedFeeAmount,
    hasSufficientBalance,
    insufficientBalanceHandler,
    requestVirtualAccount
  ])

  const handleCreateAccount = useCallback(() => {
    if (!isVerificationCompleted) {
      Alert.alert({
        title: '',
        message: 'Please complete all verification requirements'
      })
      return
    }

    if (!areAllTermsAccepted) {
      setShowError(true)
      return
    }

    if (canUseMomoPassthrough) {
      setIsPaymentMethodModalOpen(true)
      return
    }

    return handleCreateAccountViaBalance()
  }, [
    areAllTermsAccepted,
    canUseMomoPassthrough,
    handleCreateAccountViaBalance,
    isVerificationCompleted
  ])

  useEffect(() => {
    if (areAllTermsAccepted) {
      setShowError(false)
    }
  }, [areAllTermsAccepted])

  const handlePaymentMethodOptionSelection = useCallback(
    async (item: typeof availableBalances[0]) => {
      const isChipperBalance = item.type === BalanceKind.Balance

      if (isChipperBalance) {
        // This function ensures we check the customer balance before proceeding
        return handleCreateAccountViaBalance()
      }

      await requestVirtualAccount({
        type: BalanceKind.LinkedAccount,
        linkedAccountId: item.id!
      })
    },
    [handleCreateAccountViaBalance, requestVirtualAccount]
  )

  const BottomSheetContent = useCallback(
    ({ closeModal }: { closeModal?: () => void }) => {
      return (
        <BottomSheetView style={{ paddingBottom: normalize(35) }}>
          <Stack gutterHorizontal={'s24'}>
            <Text type="h4SemiBold">Select payment method</Text>
          </Stack>
          <FlatList
            data={availableBalances}
            renderItem={({ item }) => {
              const isLastOptionItem =
                item === availableBalances?.[availableBalances.length - 1]

              if (
                item.display_name ===
                MANAGE_MOMO_PASSTHROUGH_PAYMENT_METHODS_TRIGGER.title
              ) {
                return (
                  <ListItem
                    title={item.display_name}
                    leadingElementType="icon"
                    leadingElementProps={{
                      name: 'cog',
                      withContainer: true
                    }}
                    onPress={() => {
                      NavigationService.navigate(routeConst.MONEY_MOVEMENT, {
                        screen: routeConst.PAYMENT_METHOD_LIST,
                        params: {}
                      })
                      setIsPaymentMethodModalOpen(false)
                    }}
                    analyticEventName={defaultAnalyticEvents.NONE}
                    hasDivider={!isLastOptionItem}
                  />
                )
              }

              if (item.display_name === CHIPPER_BALANCE.title) {
                return (
                  <ListItem
                    title={item.display_name}
                    subTitle={CHIPPER_BALANCE.subTitle}
                    leadingElementType="icon"
                    leadingElementProps={{
                      name: 'chipper',
                      withContainer: true
                    }}
                    onPress={() => {
                      handlePaymentMethodOptionSelection(item)
                      closeModal?.()
                    }}
                    analyticEventName={defaultAnalyticEvents.NONE}
                    hasDivider={!isLastOptionItem}
                  />
                )
              }

              const logoUrl = providerLogoUrls[`${item?.mobile_money_carrier}`]

              return (
                <ListItem
                  title={item.display_name ?? ''}
                  leadingElementType="logo"
                  leadingElementProps={{
                    url: logoUrl,
                    hasBackground: true
                  }}
                  subTitle={item.mobile_money_phone}
                  onPress={() => {
                    handlePaymentMethodOptionSelection(item)
                    closeModal?.()
                  }}
                  analyticEventName={defaultAnalyticEvents.NONE}
                  hasDivider={!isLastOptionItem}
                />
              )
            }}
          />
        </BottomSheetView>
      )
    },
    [availableBalances, handlePaymentMethodOptionSelection]
  )

  const NameConfirmationBottomSheetContent = useCallback(
    ({ closeModal }: { closeModal?: () => void }) => {
      return (
        <BottomSheetView style={{ paddingBottom: normalize(35) }}>
          <Stack
            gutterHorizontal={'s24'}
            gutterTop={'s24'}
            gutterBottom={'s16'}>
            <Text type="h4SemiBold">Name on Virtual Account</Text>
          </Stack>
          <Stack gutterHorizontal={'s24'}>
            <Text type="listDefault">
              Your USD Virtual Account will be created under the name{' '}
              <Text type="listMedium">{currentUser?.display_name} </Text>. If
              there’s an error with your name, please contact support for
              assistance before proceeding. Otherwise, click continue.
            </Text>
          </Stack>
          <Box gutterHorizontal={'s24'} gutterVertical={'s16'}>
            <ButtonGroup
              buttons={[
                {
                  title: 'Contact support',
                  appearance: 'text',
                  analyticEventName: defaultAnalyticEvents.NONE,
                  onPress: () => {
                    contactSupport()
                    closeModal?.()
                  }
                },
                {
                  title: 'Continue',
                  analyticEventName: defaultAnalyticEvents.NONE,
                  onPress: () => {
                    setIsNameConfirmationModalOpen(false)
                    handleCreateAccount()
                  }
                }
              ]}
            />
          </Box>
        </BottomSheetView>
      )
    },
    [contactSupport, currentUser, handleCreateAccount]
  )

  const handleAddressConfirmation = () => {
    if (!addressInfo.actionRequired) {
      return
    }

    NavigationService.navigateWithSCA({
      screen: routeConst.ONBOARDING_ADDRESS_INPUT,
      scaAction: sca.actions.updateAddress,
      params: {
        source: 'Account',
        defaultTitle: 'Enter your address',
        dismissible: true,
        onAddressComponentsUpdated: async (
          values: complianceService.UpdateUserAddressApiArg['body']
        ) => {
          await updateAddress({
            body: values
          })
          refetchAddress()
          navigation.getParent()?.goBack()
        }
      }
    })
  }

  const handleIdentifierConfirmation = (method: OtpMethod) => {
    if (method === OtpMethod.EMAIL && !emailAddressInfo.actionRequired) {
      return
    } else if (method === OtpMethod.PHONE && !phoneNumberInfo.actionRequired) {
      return
    }
    NavigationService.navigateWithSCA({
      screen: routeConst.OTP_AUTH_INPUT,
      scaAction: sca.actions.addAuthMethod,
      scaOptions: {
        pinMessageArgs: {
          methodKind: method === OtpMethod.EMAIL ? 'email' : 'phone'
        }
      },
      params: {
        otpMethod: method,
        authMode: AuthModes.ADD_NEW_IDENTIFIER,
        sourceScreen: routeConst.ACCOUNTS_APPLICATION_SCREEN,
        sourceScreenParams: {
          currency
        }
      }
    })
  }

  const handleDocumentUpload = async () => {
    if (!isDocumentUploadRequired) {
      return
    }
    // We also fetch the list of acceptable Photo Id documents for this user
    NavigationService.navigate(routeConst.VERIFICATION_KYC_DOC_OPTION, {
      source: 'Accounts Application',
      isSecondary: true,
      supportedIDTypes: supportedDocuments || [],
      onCompletion: async () => {
        await refetchComplianceAccountConfig()
        analytics.track(analytics.events.VIRTUAL_ACCOUNTS_DOCUMENTS_UPLOADED, {
          Section: 'Document Upload'
        })
        navigation.getParent()?.goBack()
      }
    })
  }

  const handleSSNInput = () => {
    if (isLimitReached) {
      support.handleSpecificRequest(
        "Hello, I've reached the limit for SSN. Can you help me out?"
      )
      return
    }

    if (!socialSecurityInfo.actionRequired) {
      return
    }

    NavigationService.navigate(routeConst.VERIFICATION_KYC_US_SSN_INPUT, {
      selectedIDType: USRegulatoryDocTypes.SSN,
      onCompletion: () => {
        analytics.track(analytics.events.VIRTUAL_ACCOUNTS_SSN_SUBMITTED, {
          Section: 'SSN Input'
        })
      }
    })
  }

  const handleMaverickUpgrade = () => {
    analytics.track(analytics.events.IDENTITY_HUB_PROFILE_HEADER_TAPPED)
    if (maverickInfo.actionRequired) {
      NavigationService.navigate(routeConst.PROFILE_IDENTITY_HUB_LEVELS)
    }
  }

  if (isLoadingConfig || isLoadingTerms || isLoadingAccountConfig) {
    return (
      <Container white justifyContent="center">
        <Spinner size="large" />
      </Container>
    )
  }

  return (
    <Container white>
      <ScrollView
        refreshControl={
          <RefreshControl
            refreshing={false}
            onRefresh={refetchComplianceAccountConfig}
            tintColor={theme.colors.brandPrimary100}
          />
        }>
        <Stack gutterHorizontal="s24" gutterVertical="s14">
          <Text type="h3" spacing="s8">
            {isCheckPassed
              ? 'Verification requirements completed'
              : 'Complete additional verification requirements'}
          </Text>

          {isCheckPassed && (
            <Text spacing="s8">
              You’re all set to create a {currency} Account! 🎉
            </Text>
          )}

          {!isVerificationCompleted &&
            !(isDocumentUploadPending || isDocumentUploadRejected) && (
              <>
                <Text spacing="s12" type="bodySmall">
                  Complete the sections below to claim a USD account (typically
                  takes 1 minute per section).
                </Text>
                <Text spacing="s0" type="bodySmall">
                  You can come back and complete these at any time. 👍🏾
                </Text>
              </>
            )}
        </Stack>
        {isUSDPrimaryCurrency ? (
          <ListItem
            title={'Social Security Number'}
            subTitle={socialSecurityInfo.subtitle}
            onPress={handleSSNInput}
            analyticEventName={defaultAnalyticEvents.NONE}
            leadingElementType="icon"
            leadingElementProps={socialSecurityInfo.leadingElementProps}
          />
        ) : null}
        <ListItem
          title={'Identification Document'}
          subTitle={documentInfo.subtitle}
          onPress={handleDocumentUpload}
          analyticEventName={defaultAnalyticEvents.NONE}
          leadingElementType="icon"
          leadingElementProps={documentInfo.leadingElementProps}
        />
        {!isMaverickUpdateRequired && (
          <ListItem
            title={'Address'}
            subTitle={addressInfo.subtitle}
            onPress={handleAddressConfirmation}
            analyticEventName={defaultAnalyticEvents.NONE}
            leadingElementType="icon"
            leadingElementProps={addressInfo.leadingElementProps}
          />
        )}
        <ListItem
          title={'Phone Number'}
          subTitle={phoneNumberInfo.subtitle}
          onPress={() => handleIdentifierConfirmation(OtpMethod.PHONE)}
          analyticEventName={defaultAnalyticEvents.NONE}
          leadingElementType="icon"
          leadingElementProps={phoneNumberInfo.leadingElementProps}
        />
        <ListItem
          title={'Email Address'}
          subTitle={emailAddressInfo.subtitle}
          onPress={() => handleIdentifierConfirmation(OtpMethod.EMAIL)}
          analyticEventName={defaultAnalyticEvents.NONE}
          leadingElementType="icon"
          leadingElementProps={emailAddressInfo.leadingElementProps}
        />
        {(isHasIdentityHub && isMaverickUpdateRequired) && (
            <ListItem
              title={'Upgrade to Maverick'}
              subTitle={maverickInfo.subtitle}
              onPress={handleMaverickUpgrade}
              analyticEventName={defaultAnalyticEvents.NONE}
              leadingElementType="icon"
              leadingElementProps={maverickInfo.leadingElementProps}
            />
          )}
        {!isUSDPrimaryCurrency ? (
          <ListItem
            title={'Additional questions'}
            subTitle={additionalQuestionInfo.subtitle}
            onPress={() => {
              if (!additionalQuestionInfo.actionRequired) {
                return
              }
              navigation.navigate(routeConst.ACCOUNTS_ADDITIONAL_QUESTIONS, {
                currency,
                isVerificationCompleted
              })
            }}
            analyticEventName={
              analytics.events.VIRTUAL_ACCOUNTS_ADDITIONAL_QUESTION_TAPPED
            }
            leadingElementType="icon"
            leadingElementProps={additionalQuestionInfo.leadingElementProps}
            hasDivider={false}
          />
        ) : null}
        {isCheckPassed && (
          <Stack gutterHorizontal="s24" gutterVertical="s14">
            <StyledErrorMessage
              showErrorMessage={showError}
              message={'To continue you must agree to the Terms and Conditions'}
            />
            <>
              {termsOfService.map(term => {
                if (term.id) {
                  return (
                    <AccountToS
                      key={term.id}
                      data={term}
                      currency={currency}
                      handlePress={() => acceptTerms(term.id)}
                      accepted={isTermsAccepted(term.id)}
                    />
                  )
                }
              })}
            </>
          </Stack>
        )}
        {!isVerificationCompleted && (
          <Stack gutterHorizontal="s24" gutterVertical="s14">
            <Text type="listSubTitle" color="textSecondary" alignment="center">
              {virtualAccountConfig?.verification?.terms}
            </Text>
          </Stack>
        )}
      </ScrollView>
      <FixedFooter>
        {!isVerificationCompleted && (
          <Text type="listSubTitle" color="textSecondary" alignment="center">
            {virtualAccountConfig?.verification?.terms}
          </Text>
        )}
        {isCheckPassed && (
          <AccountClaimButton
            amount={virtualAccountConfig?.fee?.amount}
            currency={virtualAccountConfig?.fee?.currency}
            onPress={() => setIsNameConfirmationModalOpen(true)}
            loading={isSubmitting}
            analyticEventName={
              analytics.events.VIRTUAL_ACCOUNTS_CLAIM_ACCOUNT_TAPPED
            }
          />
        )}
      </FixedFooter>
      <Modal visible={isNameConfirmationModalOpen} transparent>
        <BottomSheetScreen
          bottomSheetProps={{
            index: isNameConfirmationModalOpen ? 0 : -1,
            onClose: () => {
              setIsNameConfirmationModalOpen(false)
            }
          }}
          childComponent={NameConfirmationBottomSheetContent}
        />
      </Modal>
      <Modal visible={isPaymentMethodModalOpen} transparent>
        <BottomSheetScreen
          bottomSheetProps={{
            index: isPaymentMethodModalOpen ? 0 : -1,
            onClose: () => {
              setIsPaymentMethodModalOpen(false)
            }
          }}
          childComponent={BottomSheetContent}
        />
      </Modal>
    </Container>
  )
}

const StyledErrorMessage = styled(ErrorMessage)`
  padding-bottom: ${p => p.theme.spacing.s16};
`
