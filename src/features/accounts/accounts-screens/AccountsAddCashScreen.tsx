import React, { useMemo } from 'react'
import { FlatList } from 'react-native'

import {
  ButtonGroup,
  Container,
  FixedFooter,
  Image,
  ListItem,
  Text,
  defaultAnalyticEvents,
  normalize
} from '@chippercash/chipper-ui'

import { useCurrencyDepositOptions, useDefaultUserBalance } from '@shared/hooks'
import { useMessageCenterModal } from '@shared/hooks/useMessageCenterModal'
import { MessageCenterProductTags } from '@shared/services/constants/constants'
import { paymentOption } from '@shared/types/paymentOption'

import { AppNavigatorParamsList, NavigationService, routeConst } from '@navigation'
import { CONST, coreService } from '@shared/services'
import styled from 'styled-components'

export const AccountsAddCashScreen = ({
  navigation,
  route
}: AppNavigatorParamsList) => {
  useMessageCenterModal({
    productTags: [MessageCenterProductTags.ADD_CASH2]
  })
  const { defaultBalance: userBalance } = useDefaultUserBalance()
  const currency = route?.params?.currency || userBalance?.currency

  const { data: rwfVirtualAccounts} =
    coreService.useGetVirtualAccountsQuery(
      { currency: CONST.CURRENCIES.RWF.currency },
      { skip: currency !== CONST.CURRENCIES.RWF.currency }
    )

  const hasActiveRwfVirtualAccount = useMemo(() => {
    return !!rwfVirtualAccounts?.accounts?.some(
      account => account.status === 'ACTIVE'
    )
  }, [rwfVirtualAccounts])

  const paymentOptions = useCurrencyDepositOptions(
    currency
  )

  if (currency === CONST.CURRENCIES.RWF.currency && !hasActiveRwfVirtualAccount) {
    return (
      <>
        <Container justifyContent="center" gutterVertical="s24" gutterHorizontal="s24">
          <StyledImage source={require('@images/error.png')} />
          <TitleText alignment="center" type="h3">
            Uh-oh!
          </TitleText>
          <Text alignment="center" type="bodySmall">
            {'We noticed you do not have an eKash account which is required for depositing cash. Please claim your eKash account to continue.'}
          </Text>
        </Container>

        <FixedFooter
          layoutMode="relative"
          style={{ marginBottom: normalize(24) }}>
          <ButtonGroup
            buttons={[
              {
                title: 'Claim eKash Account',
                onPress: () => {
                  NavigationService.navigate(routeConst.ACCOUNTS_DETAILS_SCREEN, {
                    currency: CONST.CURRENCIES.RWF.currency
                  })
                },
                analyticEventName: defaultAnalyticEvents.NONE,
                appearance: 'primary'
              }
            ]}
          />
        </FixedFooter>
      </>
    )
  }

  const renderItem = ({
    item: option,
    index
  }: {
    item: paymentOption
    index: number
  }) => {
    return (
      <ListItem
        key={index}
        title={option.title}
        subTitle={option.subtitle}
        onPress={() => {
          navigation.navigate(option.routeConfig.screen, {
            ...option.routeConfig.params,
            selectedOption: {
              accountType: option.accountType
            }
          })
        }}
        analyticEventName={defaultAnalyticEvents.NONE}
        {...option.leadingElement}
        {...option.trailingElement}
      />
    )
  }

  return (
    <Container white>
      <FlatList renderItem={renderItem} data={paymentOptions} />
    </Container>
  )
}

const StyledImage = styled(Image)`
  align-self: center;
  height: ${normalize(180)}px;
  width: ${normalize(180)}px;
`

const TitleText = styled(Text)`
  margin-top: ${p => p.theme.spacing.s24};
`
