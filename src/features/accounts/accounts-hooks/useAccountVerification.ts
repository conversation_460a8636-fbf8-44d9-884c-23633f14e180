import { IconLeadingElementType } from '@chippercash/chipper-ui'

import {
  CONST,
  authService,
  complianceService,
  coreService
} from '@shared/services'
import { Currency } from '@shared/store'

type VerificationInfo = {
  subtitle: string
  actionRequired: boolean
  leadingElementProps: IconLeadingElementType['leadingElementProps']
  isLimitReached?: boolean
}

enum NGUserTier {
  TIER_0 = 'TIER_0',
  TIER_1 = 'TIER_1',
  TIER_2 = 'TIER_2',
  TIER_3 = 'TIER_3'
}

export const useAccountVerification = () => {
  const {
    data: complianceAccountConfig,
    refetch,
    isLoading: isLoadingCompliance
  } = complianceService.useGetConfigurationQuery()
  const { data: authMethods, isLoading: isLoadingAuth } =
    authService.useGetAllAuthMethodsQuery()
  const addressQuery = complianceService.useGetAddressQuery()
  const balanceQuery = coreService.useBalanceQuery()
  const { data: identityHubData } = complianceService.useGetIdentityHubQuery()
  const { data: ngUserTierInfo } = coreService.useGetNGUserTierInfoQuery()
  const { data: currentUser } = coreService.useCurrentUserQuery()

  const hasIdentityHub = complianceAccountConfig?.featureFlags?.identityHub
  const maverickStatus = identityHubData?.find(
    item => item.name === 'Maverick'
  )?.status

  const addressData = addressQuery.data
  const currency = balanceQuery.data?.currency as Currency
  const { phoneNumbers, emails } = authMethods || {}

  const isLoading =
    isLoadingCompliance ||
    balanceQuery.isLoading ||
    addressQuery.isLoading ||
    isLoadingAuth

  const documentUpload =
    complianceAccountConfig?.virtualAccounts?.verificationSteps
      ?.documentUploadVirtualAccount
  const isDocumentUploadPending =
    documentUpload?.status === CONST.VERIFICATION_STEP_STATUS.PENDING
  const isDocumentUploadRejected =
    documentUpload?.status === CONST.VERIFICATION_STEP_STATUS.REJECTED
  const isDocumentUploadStatusCompleted =
    documentUpload?.status === CONST.VERIFICATION_STEP_STATUS.COMPLETED
  const isDocumentUploadRequired =
    documentUpload?.status === CONST.VERIFICATION_STEP_STATUS.NOT_STARTED ||
    isDocumentUploadRejected
  const isDocumentUploadCompleted =
    !isDocumentUploadRequired || isDocumentUploadStatusCompleted

  const isUSDPrimaryCurrency = currency === CONST.CURRENCIES.USD.currency
  const isNGNPrimaryCurrency = currency === CONST.CURRENCIES.NGN.currency

  const isQuestionPendingReview =
    complianceAccountConfig?.virtualAccounts?.verificationSteps
      ?.virtualAccountQuestionnaire?.status ===
    CONST.VERIFICATION_STEP_STATUS.PENDING

  const socialSecurity =
    complianceAccountConfig?.virtualAccounts?.verificationSteps?.socialSecurity

  const isLimitReached =
    socialSecurity?.status === CONST.VERIFICATION_STEP_STATUS.LIMIT_REACHED

  const allowedTiers = ngUserTierInfo?.allowed_tier
  const isOnlyTier3Allowed = allowedTiers?.length === 1 && allowedTiers[0] === NGUserTier.TIER_3

  const isRequireMaverickUpdate = () => {
    if (currentUser?.is_business) {
      return false
    }

    if (isOnlyTier3Allowed && isNGNPrimaryCurrency) {
      return true
    }

    return false
  }

  const getDocumentUploadInfo = (): VerificationInfo => {
    if (documentUpload?.status === CONST.VERIFICATION_STEP_STATUS.COMPLETED) {
      return {
        subtitle: 'Document approved',
        actionRequired: false,
        leadingElementProps: {
          name: 'check',
          isFeatherIcon: true,
          color: 'textGreen'
        }
      }
    }

    if (documentUpload?.status === CONST.VERIFICATION_STEP_STATUS.REJECTED) {
      return {
        subtitle: 'Submit a government-issued identification document',
        actionRequired: true,
        leadingElementProps: {
          name: 'x',
          isFeatherIcon: true,
          color: 'textRed'
        }
      }
    }

    if (documentUpload?.status === CONST.VERIFICATION_STEP_STATUS.PENDING) {
      return {
        subtitle: 'Pending review',
        actionRequired: false,
        leadingElementProps: {
          name: 'clock',
          isFeatherIcon: true,
          color: 'textYellow'
        }
      }
    }

    return {
      subtitle: 'Submit a government-issued identification document',
      actionRequired: true,
      leadingElementProps: {
        name: 'user',
        isFeatherIcon: true,
        color: 'textPrimary'
      }
    }
  }

  const getSSNInfo = (): VerificationInfo => {
    if (!isUSDPrimaryCurrency) {
      return {
        subtitle: '',
        actionRequired: false,
        leadingElementProps: {
          name: 'check',
          isFeatherIcon: true,
          color: 'textGreen'
        }
      }
    }
    if (socialSecurity?.status === CONST.VERIFICATION_STEP_STATUS.COMPLETED) {
      return {
        subtitle: 'Social Security Number confirmed',
        actionRequired: false,
        leadingElementProps: {
          name: 'check',
          isFeatherIcon: true,
          color: 'textGreen'
        }
      }
    }

    if (socialSecurity?.status === CONST.VERIFICATION_STEP_STATUS.REJECTED) {
      return {
        subtitle: 'Submit a valid Social Security Number',
        actionRequired: true,
        leadingElementProps: {
          name: 'x',
          isFeatherIcon: true,
          color: 'textRed'
        }
      }
    }

    if (isLimitReached) {
      return {
        subtitle:
          'You have reached the limit for Social Security Number attempts, please contact support.',
        actionRequired: false,
        isLimitReached: true,
        leadingElementProps: {
          name: 'x',
          isFeatherIcon: true,
          color: 'textRed'
        }
      }
    }

    if (socialSecurity?.status === CONST.VERIFICATION_STEP_STATUS.PENDING) {
      return {
        subtitle: 'Verification in progress',
        actionRequired: false,
        leadingElementProps: {
          name: 'clock',
          isFeatherIcon: true,
          color: 'textYellow'
        }
      }
    }

    return {
      subtitle: 'Submit a Social Security Number',
      actionRequired: true,
      leadingElementProps: {
        name: 'user',
        isFeatherIcon: true,
        color: 'textPrimary'
      }
    }
  }

  const getAddressInfo = (): VerificationInfo => {
    if (addressData?.address) {
      if (currency === CONST.CURRENCIES.NGN.currency) {
        const invalidCountry = addressData?.address?.country !== 'NG'
        if (invalidCountry) {
          return {
            subtitle: 'Please confirm a valid Nigerian 🇳🇬 address',
            actionRequired: true,
            leadingElementProps: {
              name: 'x',
              isFeatherIcon: true,
              color: 'textRed'
            }
          }
        }
      }

      return {
        subtitle: 'Address confirmed',
        actionRequired: false,
        leadingElementProps: {
          name: 'check',
          isFeatherIcon: true,
          color: 'textGreen'
        }
      }
    }

    return {
      subtitle: 'Provide a valid address',
      actionRequired: true,
      leadingElementProps: {
        name: 'home',
        isFeatherIcon: true,
        color: 'textPrimary'
      }
    }
  }
  const getPhoneNumberInfo = (): VerificationInfo => {
    if (phoneNumbers && phoneNumbers.length > 0) {
      return {
        subtitle: 'Phone number confirmed',
        actionRequired: false,
        leadingElementProps: {
          name: 'check',
          isFeatherIcon: true,
          color: 'textGreen'
        }
      }
    }

    return {
      subtitle: 'Provide your phone number',
      actionRequired: true,
      leadingElementProps: {
        name: 'phone',
        isFeatherIcon: true,
        color: 'textPrimary'
      }
    }
  }
  const getEmailAddressInfo = (): VerificationInfo => {
    if (emails && emails.length > 0) {
      return {
        subtitle: 'Email Address confirmed',
        actionRequired: false,
        leadingElementProps: {
          name: 'check',
          isFeatherIcon: true,
          color: 'textGreen'
        }
      }
    }

    return {
      subtitle: 'Provide your email address',
      actionRequired: true,
      leadingElementProps: {
        name: 'mail',
        isFeatherIcon: true,
        color: 'textPrimary'
      }
    }
  }
  const getAdditionalQuestionInfo = (): VerificationInfo => {
    if (isUSDPrimaryCurrency) {
      return {
        subtitle: '',
        actionRequired: false,
        leadingElementProps: {
          name: 'check',
          isFeatherIcon: true,
          color: 'textGreen'
        }
      }
    }
    const status =
      complianceAccountConfig?.virtualAccounts?.verificationSteps
        ?.virtualAccountQuestionnaire?.status

    if (status === CONST.VERIFICATION_STEP_STATUS.COMPLETED) {
      return {
        subtitle: 'Additional Questions approved',
        actionRequired: false,
        leadingElementProps: {
          name: 'check',
          isFeatherIcon: true,
          color: 'textGreen'
        }
      }
    }

    if (isQuestionPendingReview) {
      return {
        subtitle: 'Pending review',
        actionRequired: false,
        leadingElementProps: {
          name: 'clock',
          isFeatherIcon: true,
          color: 'textYellow'
        }
      }
    }

    if (status === CONST.VERIFICATION_STEP_STATUS.REJECTED) {
      return {
        subtitle: 'Additional Questions rejected',
        actionRequired: true,
        leadingElementProps: {
          name: 'x',
          isFeatherIcon: true,
          color: 'textRed'
        }
      }
    }

    return {
      subtitle: 'Let us know how you plan on using your USD account',
      actionRequired: true,
      leadingElementProps: {
        name: 'help-circle',
        withContainer: true
      }
    }
  }

  const getMaverickInfo = (): VerificationInfo => {
    if (!isNGNPrimaryCurrency) {
      return {
        subtitle: '',
        actionRequired: false,
        leadingElementProps: {
          name: 'check',
          isFeatherIcon: true,
          color: 'textGreen'
        }
      }
    }

    if (maverickStatus === CONST.VERIFICATION_STEP_STATUS.COMPLETED) {
      return {
        subtitle: 'Upgrade completed',
        actionRequired: false,
        leadingElementProps: {
          name: 'check',
          isFeatherIcon: true,
          color: 'textGreen'
        }
      }
    }

    if (maverickStatus === CONST.VERIFICATION_STEP_STATUS.REJECTED) {
      return {
        subtitle: 'Upgrade rejected',
        actionRequired: true,
        leadingElementProps: {
          name: 'x',
          isFeatherIcon: true,
          color: 'textRed'
        }
      }
    }

    if (maverickStatus === CONST.VERIFICATION_STEP_STATUS.PENDING) {
      return {
        subtitle: 'Pending review',
        actionRequired: false,
        leadingElementProps: {
          name: 'clock',
          isFeatherIcon: true,
          color: 'textYellow'
        }
      }
    }

    return {
      subtitle: 'Upgrade to Maverick',
      actionRequired: true,
      leadingElementProps: {
        name: 'trending-up',
        isFeatherIcon: true
      }
    }
  }

  const isVerificationCompleted =
    isDocumentUploadStatusCompleted &&
    !getPhoneNumberInfo()?.actionRequired &&
    !getEmailAddressInfo()?.actionRequired &&
    !getSSNInfo()?.actionRequired &&
    !getSSNInfo()?.isLimitReached &&
    !getMaverickInfo()?.actionRequired &&
    !getAddressInfo()?.actionRequired

  return {
    documentUpload,
    isLoading,
    complianceAccountConfig,
    supportedDocuments:
      complianceAccountConfig?.virtualAccounts?.verificationSteps
        ?.documentUploadVirtualAccount?.supportedDocuments,
    refetchComplianceAccountConfig: refetch,
    isDocumentUploadPending,
    isDocumentUploadRejected,
    isDocumentUploadStatusCompleted,
    isDocumentUploadRequired,
    isDocumentUploadCompleted,
    getDocumentUploadInfo,
    getPhoneNumberInfo,
    getEmailAddressInfo,
    getAddressInfo,
    getAdditionalQuestionInfo,
    getSSNInfo,
    isVerificationCompleted,
    isLimitReached,
    isQuestionPendingReview,
    isQuestionCompleted: getAdditionalQuestionInfo()?.actionRequired,
    getMaverickInfo,
    isHasIdentityHub: hasIdentityHub,
    isMaverickUpdateRequired: isRequireMaverickUpdate()
  }
}
