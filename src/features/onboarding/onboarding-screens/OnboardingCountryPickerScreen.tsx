import React, { useCallback, useState } from 'react'
import { View } from 'react-native'

import { Box, SearchInput, Text } from '@chippercash/chipper-ui'
import { FlashList } from '@shopify/flash-list'
import styled from 'styled-components/native'

import { Flag, SectionHeader } from '@shared/components'
import { countries as countriesService } from '@shared/services'
import { CONST } from '@shared/services'
import { store } from '@shared/store'

import { OnboardingCountryPickerScreenProps } from '@navigation'

interface CountryCodeItem {
  countryCode: countriesService.CountryCode
}

const ITEM_HEIGHT = 64

export const OnboardingCountryPickerScreen = ({
  navigation,
  route
}: OnboardingCountryPickerScreenProps) => {
  const { flagStyle = 'emoji', countries = [] } = route.params

  const [searchTerm, setSearchTerm] = useState('')

  const handleCountryTapped = (country: countriesService.CountryCode) => {
    route.params.onCountrySelected?.(country)
    navigation.goBack()
  }

  const getMappedCountries = (countryList: countriesService.CountryCode[]) => {
    return countryList.map(c => {
      return {
        countryCode: c
      } as CountryCodeItem
    })
  }

  const getCountrySections = useCallback(() => {
    let sectionData: Array<string | CountryCodeItem> = []

    // either build a list of top countries, if specified in route params
    // or build full list
    if (countries && countries.length > 0) {
      sectionData = sectionData
        .concat('Available Countries')
        .concat(
          getMappedCountries(countries).filter(c =>
            countriesService
              .getName(c.countryCode)
              .toLowerCase()
              .includes(searchTerm.toLowerCase())
          )
        )
    } else {
      let topCountries: countriesService.CountryCode[] = [
        'GH',
        'KE',
        'NG',
        'RW',
        'ZA',
        'TZ',
        'UG',
        'GB',
        'US'
      ]

      if (store.currency === CONST.CURRENCIES.GBP.currency) {
        topCountries = ['KE', 'NG', 'RW', 'ZA', 'TZ', 'GB']
      }

      // gets an array with all the alpha2 country codes
      const allCountries = Object.keys(
        countriesService.getAll()
      ) as countriesService.CountryCode[]

      sectionData = sectionData
        .concat('Top Countries')
        .concat(
          getMappedCountries(topCountries).filter(c =>
            countriesService
              .getName(c.countryCode)
              .toLowerCase()
              .includes(searchTerm.toLowerCase())
          )
        )
        .concat('All Countries')
        .concat(
          getMappedCountries(allCountries).filter(c =>
            countriesService
              .getName(c.countryCode)
              .toLowerCase()
              .includes(searchTerm.toLowerCase())
          )
        )
    }

    return sectionData
  }, [countries, searchTerm])

  const getFlagComponent = useCallback(
    (countryCode: countriesService.CountryCode) => {
      switch (flagStyle) {
        case 'flat':
          return (
            <Flag
              country={countryCode}
              style={{ marginLeft: 0, marginRight: 0 }}
            />
          )
        case 'emoji':
        default:
          return (
            <FlagText>{countriesService.getFlagEmoji(countryCode)}</FlagText>
          )
      }
    },
    [flagStyle]
  )

  return (
    <Container>
      <FlashList
        data={getCountrySections()}
        ListHeaderComponent={
          <Box>
            <SearchInput
              value={searchTerm}
              onChange={setSearchTerm}
              placeholder="Search countries..."
            />
          </Box>
        }
        renderItem={({ item }) => {
          if (typeof item === 'string') {
            // Header
            return <ListSectionHeader title={item} />
          } else {
            // Flag + Country
            return (
              <StyledRow onPress={() => handleCountryTapped(item.countryCode)}>
                {getFlagComponent(item.countryCode)}
                <View style={{ marginHorizontal: 15 }}>
                  <Text spacing="s0" weightOverride="primaryMedium">
                    {countriesService.getName(item.countryCode)}
                  </Text>
                </View>
              </StyledRow>
            )
          }
        }}
        estimatedItemSize={ITEM_HEIGHT}
      />
    </Container>
  )
}

const ListSectionHeader = styled(SectionHeader)`
  background-color: ${p => p.theme.colors.bgLayerOne};
`

const StyledRow = styled.TouchableOpacity`
  flex-direction: row;
  align-items: center;
  padding-horizontal: ${p => p.theme.spacing.s20};
  padding-vertical: ${p => p.theme.spacing.s12};
  background-color: ${p => p.theme.colors.bgLayerOne};
`

const FlagText = styled.Text`
  font-size: 32px;
`

const Container = styled.SafeAreaView`
  flex: 1;
  background-color: ${p => p.theme.colors.bgLayerOne};
`
