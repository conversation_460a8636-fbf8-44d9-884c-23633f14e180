import React, { useMemo } from 'react'

import { Icon, Text, theme } from '@chippercash/chipper-ui'
import { ScrollView } from 'react-native-gesture-handler'
import styled from 'styled-components/native'

import { CONST, coreService } from '@shared/services'

import { NavigationService, routeConst } from '@navigation'
import { Alert } from 'react-native'
import { useShowExperimentFeature } from '@shared/hooks'

/**
 * Shows all investment products
 *
 */
export const InvestmentHome = () => {
  const { data: userAccountConfig } = coreService.useAccountConfigurationQuery()
  const { isStocksTabAvailable, isCryptoTradingAvailable, isMutualFundsProductAvailable } =
    userAccountConfig || {}

  const isTreasuryBillsEnabled = useShowExperimentFeature(
    CONST.EXPERIMENT_KEYS.TREASURY_BILLS
  )

  const isLocalEquityEnabled = useShowExperimentFeature(
    CONST.EXPERIMENT_KEYS.LOCAL_EQUITY
  )

  const allProducts = useMemo(() => {
    return [
        {
            imageSource: require('@images/mutual-funds/mutual-funds-card.png'),
            title: 'Mutual funds',
            isEnabled: isMutualFundsProductAvailable,
            onPress: () => {
              NavigationService.navigate(routeConst.MUTUAL_FUNDS, {
                screen: routeConst.MUTUAL_FUNDS
              })
            }
          },
          {
            imageSource: require('@images/treasury-bills/treasury-bills-card.png'),
            title: 'Treasury bills',
            isEnabled: isTreasuryBillsEnabled,
            onPress: () => {
                Alert.alert('Coming soon!🎉')
            }
          },
          {
            imageSource: require('@images/local-equity/local-equity-card.png'),
            title: 'Local equity',
            isEnabled: isLocalEquityEnabled,
            onPress: () => {
               Alert.alert('Coming soon!🎉')
            }
          },
          {
            imageSource: require('@images/stocks/stocks-card.png'),
            title: 'Stocks',
            isEnabled: isStocksTabAvailable,
            onPress: () => {
                NavigationService.navigate(routeConst.STOCKS, {
                    screen: routeConst.INVEST_STOCKS_TAB
                })
            }
        },
        {
            imageSource: require('@images/crypto/crypto-card.png'),
            title: 'Crypto',
            isEnabled: isCryptoTradingAvailable,
            onPress: () => {
                NavigationService.navigate(routeConst.CRYPTO, {
                    screen: routeConst.INVEST_CRYPTO_TAB
                })
            }
        }
    ]
  }, [isCryptoTradingAvailable, isLocalEquityEnabled, isMutualFundsProductAvailable, isStocksTabAvailable, isTreasuryBillsEnabled])

  const products = useMemo(() => {
    const enabledProducts = allProducts.filter(item => item.isEnabled)
    return enabledProducts
  }, [allProducts])

  return (
    <>
      <Container>
        <ScrollView>
          <BoxWrapper>
            {products.map((product, index) => {
              const { onPress, imageSource, title } = product
              return (
                <BoxItem key={index} onPress={onPress}>
                  <Image
                    accessibilityIgnoresInvertColors
                    source={imageSource}
                  />
                  <WhiteContainer>
                    <Text
                      spacing={'s0'}
                      numberOfLines={1}
                      alignment="left"
                      type="label"
                      color="textPrimary">
                      {title}
                    </Text>

                    <Icon
                      size="medium"
                      name={'chevron-right'}
                      isFeatherIcon={true}
                      color="black100"
                    />
                  </WhiteContainer>
                </BoxItem>
              )
            })}
          </BoxWrapper>
        </ScrollView>
      </Container>
    </>
  )
}

const Container = styled.View`
  flex: 1;
  padding: 24px;
  justify-content: center;
  background-color:${theme.colors.bgLayerOne};
`

const Image = styled.Image`
  width: 100%;
  resize-mode: cover;
  border-top-right-radius: 10px;
  border-top-left-radius: 10px;
`
const WhiteContainer = styled.View`
  flex-direction: row;
  background-color: white;
  border-bottom-right-radius: 10px;
  border-bottom-left-radius: 10px;
  padding: 10px;
  align-items: center;
  justify-content: space-between;
`
const BoxItem = styled.TouchableOpacity`
  width: 47%;
  background-color: ${theme.colors.bgLayerOne};
  border-width: 1px;
  border-color: ${theme.colors.border};
  border-radius: 10px;
`
const BoxWrapper = styled.View`
  flex: 1;
  flex-wrap: wrap;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  gap: 16px;
`
