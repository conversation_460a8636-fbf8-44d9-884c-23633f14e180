import { StackScreenProps } from '@react-navigation/stack'

import { coreService } from '@shared/services'
import { ArrayElement, UserVoucher } from '@shared/types'

import { routeConst } from '@navigation'

export type StockCategorySymbol =
  ArrayElement<coreService.CategoriesApiResponse>
export type StockSymbol = ArrayElement<coreService.TopSymbolsApiResponse>
export type StockAccount = coreService.AccountApiResponse
export type StockOrders = coreService.OrdersApiResponse
export type CategoryInstruments =
  coreService.CategoryInstrumentsApiResponse['instruments']
export type StockStatement =
  ArrayElement<coreService.AccountStatementsApiResponse>

export type EnrichedStockSymbol = StockSymbol & {
  currentValue: number
  pendingAmountInUSD: number
}

export type StockOrder =
  ArrayElement<coreService.PaymentsApiResponse>['stockTrade']

export type StocksDocuments = 'statements' | 'confirms'

export type StocksParamList = {
  [routeConst.INVEST_STOCKS_TAB]: undefined
  [routeConst.STOCKS_APPLICATION]: undefined
  [routeConst.STOCKS_APPLICATION_CONTACT_INFORMATION]: undefined
  [routeConst.STOCKS_QUESTIONS]: {
    userInfo: any
  }
  [routeConst.STOCKS_INVESTOR_PROFILE_QUESTIONS]: {
    userInfo: any
  }
  [routeConst.STOCKS_PRICE]: {
    stock: StockSymbol
  }
  [routeConst.STOCKS_CANCEL]: {
    stock: StockOrder
  }
  [routeConst.STOCKS_BUY_SELL]: {
    mode: 'Buy' | 'Sell'
    stock: EnrichedStockSymbol
  }
  [routeConst.STOCKS_ACCOUNT_STATEMENTS]: {
    name: string
    type: StocksDocuments
  }

  [routeConst.STOCKS_CATEGORIES]: {
    categories: StockCategorySymbol[]
    stocks: StockSymbol[]
  }

  [routeConst.STOCKS_CATEGORY]: {
    category: StockCategorySymbol
  }

  [routeConst.STOCKS_VOUCHER]: {
    userVoucher: UserVoucher
  }
}

export type StockVoucherScreenProps = StackScreenProps<
  StocksParamList,
  typeof routeConst.STOCKS_VOUCHER
>

export type StockCategoryScreenProps = StackScreenProps<
  StocksParamList,
  typeof routeConst.STOCKS_CATEGORY
>

export type StocksCategorySearchScreenProps = StackScreenProps<
  StocksParamList,
  typeof routeConst.STOCKS_CATEGORIES
>

export type StocksQuestionsScreenProps = StackScreenProps<
  StocksParamList,
  typeof routeConst.STOCKS_QUESTIONS
>

export type StocksInvestorProfileQuestionsScreenProps = StackScreenProps<
  StocksParamList,
  typeof routeConst.STOCKS_INVESTOR_PROFILE_QUESTIONS
>

export type StocksPriceScreenProps = StackScreenProps<
  StocksParamList,
  typeof routeConst.STOCKS_PRICE
>

export type StocksCancelScreenProps = StackScreenProps<
  StocksParamList,
  typeof routeConst.STOCKS_CANCEL
>

export type StocksBuySellScreenProps = StackScreenProps<
  StocksParamList,
  typeof routeConst.STOCKS_BUY_SELL
>

export type StocksAccountStatementScreenProps = StackScreenProps<
  StocksParamList,
  typeof routeConst.STOCKS_ACCOUNT_STATEMENTS
>

export enum StockProvider {
  DRIVE_WEALTH = 'DRIVE_WEALTH',
  USE = 'USE'
}

export interface StockDividendsMetadata {
  amount: string
}

export interface StockStatsMetadata {
  dividendYield: number
  marketcap: number
  peRatio: number
  ttmEPS: number
  week52high: number
  week52low: number
}

export interface StockQuoteMetadata {
  avgTotalVolume: number
  close: number
  latestVolume: number
  open: number
}

export interface StockNewsMetadata {
  datetime: Date
  headline: string
  image: string
  source: string
  url: string
}

export interface StocksStatisticsTerms {
  term: string
  definition: string
}
