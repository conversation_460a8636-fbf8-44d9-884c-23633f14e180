import React, { useEffect, useMemo, useState } from 'react'
import { ActivityIndicator, ScrollView, View } from 'react-native'

import { Button, Container, Icon, Text, alert } from '@chippercash/chipper-ui'
import styled from 'styled-components/native'

import { useFetchExchangeRates, useShowExperimentFeature } from '@shared/hooks'
import { useMessageCenterModal } from '@shared/hooks/useMessageCenterModal'
import {
  CONST,
  analytics,
  complianceService,
  coreService,
  format,
  localStorage,
  sca,
  verification
} from '@shared/services'
import { MessageCenterProductTags } from '@shared/services/constants/constants'
import {
  ReturnedFeatureWithName,
  VerificationActions
} from '@shared/services/verification/verificationTypes'

import { NavigationService, routeConst } from '@navigation'

import {
  Companies,
  HomeContent,
  PdtLandedWarning,
  StocksErrorState
} from '../stocks-components'

export const Landing = () => {
  useMessageCenterModal({
    productTags: [MessageCenterProductTags.STOCKS]
  })
  const [tappedNotifyMe, setTappedNotifyMe] = useState(false)
  const [hasShowedLandedOnPdtWarning, setHasShowedLandedOnPdtWarning] =
    useState(false)
  const { data: stockConfiguration, isLoading: isLoadingConfig } =
    coreService.useConfigurationQuery()

  const filteredStockConfiguration = useMemo(() => {
    if (!stockConfiguration?.details?.length) {
      return stockConfiguration
    }
    return {
        ...stockConfiguration,
        details: stockConfiguration.details.filter(detail => detail.title !== 'Mutual Funds')
      }
  }, [stockConfiguration])

  const isStocksConfigEnabled = useShowExperimentFeature(
    CONST.EXPERIMENT_KEYS.STOCKS_CONFIG
  )
  const { data: account, isFetching: isFetchingAccount } =
    coreService.useAccountQuery()
  const balanceQuery = coreService.useBalanceQuery()
  const currency = balanceQuery.data?.currency
  const { rates, isFetching: isFetchingRates } = useFetchExchangeRates({
    userSettlementRate: 'true'
  })
  const adjustedRate = rates?.USD?.adjustedRate
  const {
    data: accountStatus,
    isFetching: isFetchingAccountStatus,
    refetch: refetchAccountStatus
  } = coreService.useAccountStatusQuery()
  const {
    data: addressQueryData,
    isFetching: isFetchingAddress,
    refetch: refetchAddress
  } = complianceService.useGetAddressQuery()
  const { data: complianceAccountConfig } =
    complianceService.useGetConfigurationQuery()
  const { data: userAccountConfig } = coreService.useAccountConfigurationQuery()
  const [updateUserAddress] = complianceService.useUpdateUserAddressMutation()
  const [createRecord] = coreService.useCreateRecordMutation()

  const isProcessing =
    isFetchingAccount ||
    isFetchingAccountStatus ||
    isFetchingAddress ||
    isFetchingRates ||
    isLoadingConfig

  const status = accountStatus?.status

  useEffect(() => {
    if (status) {
      analytics.once(analytics.events.STOCKS_MAIN_SCREEN_VIEWED, {
        Status: status
      })
    }
  }, [status])

  useEffect(() => {
    const checkTappedNotify = async () => {
      const notifyMeCurrent = await localStorage.getBoolean(
        CONST.TAPPED_NOTIFY_ME_STOCKS
      )
      setTappedNotifyMe(!!notifyMeCurrent)
    }

    const setWarningVisibility = async () => {
      const showedLandedOnPdtWarning =
        (await localStorage.getBoolean(
          CONST.LANDED_ON_PDT_WARNING_SHOWED_STOCKS
        )) || false
      setHasShowedLandedOnPdtWarning(showedLandedOnPdtWarning)
    }

    checkTappedNotify()
    setWarningVisibility()
  }, [])

  const fractionalSharesSubtitle = useMemo(() => {
    const defaultSubtitle = 'Start now with as little as $1 USD'

    if (currency !== 'USD') {
      return `${defaultSubtitle} (approx. ${format.fiat.getFormattedValue(
        1 / adjustedRate,
        currency
      )})`
    }

    return defaultSubtitle
  }, [adjustedRate, currency])

  const badges = useMemo(() => {
    const defaultConfig = [
      {
        title: 'Fractional Shares',
        subtitle: fractionalSharesSubtitle,
        icon: 'pie-chart'
      },
      {
        title: 'Lowest Commissions',
        subtitle: 'Invest more, save on fees',
        icon: 'percent'
      },
      {
        title: 'Invest in the Best',
        subtitle:
          'Own top US stocks like Amazon, Nike, Tesla, McDonalds & more',
        icon: 'bar-chart'
      },
      {
        title: 'Trade Instantly',
        subtitle: 'Fastest trade execution & confirmation',
        icon: 'zap'
      },
      {
        title: 'Account Protection',
        subtitle: 'SIPC protection of securities and cash up to $500,000 USD',
        icon: 'shield'
      }
    ]

    if (filteredStockConfiguration?.details?.length) {
      if (isStocksConfigEnabled) {
        return filteredStockConfiguration.details
      }

      return [...filteredStockConfiguration.details, ...defaultConfig]
    }
    return defaultConfig
  }, [fractionalSharesSubtitle, isStocksConfigEnabled, filteredStockConfiguration])

  const onTradeButtonPressed = async () => {
    if (
      !addressQueryData?.address ||
      addressQueryData?.address?.street?.replace(/\s/g, '') === ''
    ) {
      const goToOnboarding = async () => {
        await refetchAddress()
        NavigationService.pop()
        await createRecord()
        startOnboarding()
      }

      alert.error({
        title: 'Address is required to trade stocks',
        message: 'To begin investing in Stocks, you must have a valid address.',
        options: { cancelable: true },
        buttons: [
          {
            text: 'Update Address',
            onPress: () => {
              analytics.track(analytics.events.STOCKS_ONBOARDING_TAPPED, {
                Section: 'Update Address'
              })

              NavigationService.navigateWithSCA({
                screen: routeConst.ONBOARDING_ADDRESS_INPUT,
                scaAction: sca.actions.updateAddress,
                params: {
                  source: 'Stocks',
                  defaultTitle: 'Update your address',
                  dismissible: true,
                  onAddressComponentsUpdated: async (values: {
                    country: string
                    ghanaDigitalAddress?: string
                  }) => {
                    // show success state only for Ghana
                    if (
                      values.country === 'GH' &&
                      !!values.ghanaDigitalAddress
                    ) {
                      NavigationService.navigate(
                        routeConst.ONBOARDING_SUCCESS_FAIL,
                        {
                          message: 'Digital Address\nadded successfully',
                          action: () =>
                            updateUserAddress({
                              body: {
                                ...values
                              }
                            }),
                          onDismiss: goToOnboarding
                        }
                      )
                    } else {
                      await updateUserAddress({
                        body: {
                          ...values
                        }
                      })
                      analytics.track(
                        analytics.events.STOCKS_ONBOARDING_COMPLETED,
                        {
                          Section: 'Update Address'
                        }
                      )
                      goToOnboarding()
                    }
                  }
                }
              })
            }
          },
          {
            text: 'Not Now',
            onPress: () => {
              analytics.track(analytics.events.STOCKS_ACTION_TAPPED, {
                Action: 'Update Address Reject'
              })
            }
          }
        ]
      })
    } else {
      await createRecord()
      startOnboarding()
    }
  }
  const startOnboarding = () => {
    verification.routeToFeature({
      verificationActions: complianceAccountConfig?.identityVerification
        ?.verificationSteps as VerificationActions,
      feature: {
        ...(complianceAccountConfig?.features
          ?.stocks as ReturnedFeatureWithName),
        name: verification.CONST.FEATURES.STOCKS_APPLICATION_CUSTOM
      },
      options: { manageUnhappyPath: true },
      navigationParams: {
        screen: routeConst.STOCKS_APPLICATION
      }
    })
  }

  const notifyMe = () => {
    localStorage.setBoolean(CONST.TAPPED_NOTIFY_ME_STOCKS, true)
    setTappedNotifyMe(true)
  }

  const StockBadges = () => {
    return badges.map(({ title, subtitle, icon }) => {
      return (
        <BadgeRow key={title}>
          <Icon name={icon} customSize={20} color="brandPrimary100" />
          <Container gutterHorizontal="s8">
            <Text type="listMedium">{title}</Text>

            <Text spacing="s4" type="bodySmall">
              {subtitle}
            </Text>
          </Container>
        </BadgeRow>
      )
    })
  }

  // eslint-disable-next-line react/no-unstable-nested-components
  const NoAccount = () => {
    const isStocksSignUpAvailable = userAccountConfig?.isStocksSignUpAvailable
    return (
      <>
        <ScrollView>
          <Container white gutterHorizontal="s24">
            <Companies allowAction={false} />
            <InvestTitle spacing="s20" type="h1">
              Stocks Investing for Everyone
            </InvestTitle>
            <View>{StockBadges()}</View>
            {!isStocksSignUpAvailable && !tappedNotifyMe && (
              <Text alignment="center" spacing="s16" type="bodySmall">
                {
                  'Tap “Notify Me” to be notified as soon as Stocks are available in your country'
                }
              </Text>
            )}
          </Container>
        </ScrollView>
        <FooterContainer>
          {!isStocksSignUpAvailable && tappedNotifyMe && (
            <NotifyMeContainer>
              <Text type="bodySmall" alignment="center" spacing="s0">
                {
                  'You will be notified as soon as Stocks are available in your country'
                }
              </Text>
            </NotifyMeContainer>
          )}
          {!isStocksSignUpAvailable && !tappedNotifyMe && (
            <Button
              title="Notify Me"
              onPress={notifyMe}
              analyticEventName={analytics.events.STOCKS_NOTIFY_ME_TAPPED}
            />
          )}
          {isStocksSignUpAvailable && (
            <Button
              title={
                status === 'LANDING_SCREEN'
                  ? 'Continue Investing'
                  : 'Begin Investing'
              }
              onPress={onTradeButtonPressed}
              analyticEventName={
                status === 'LANDING_SCREEN'
                  ? analytics.events.STOCKS_CONTINUE_INVESTING_TAPPED
                  : analytics.events.STOCKS_BEGIN_INVESTING_TAPPED
              }
            />
          )}
        </FooterContainer>
      </>
    )
  }

  const changeLandedOnPdtWarning = () => {
    setHasShowedLandedOnPdtWarning(true)
  }

  if (isProcessing) {
    return (
      <Container justifyContent={'center'} white>
        <ActivityIndicator size={'large'} />
      </Container>
    )
  }

  if (
    account?.dwAccount?.accountStatus ===
      CONST.STOCKS_ACCOUNT_CONFIG.OPEN_NO_NEW_TRADES &&
    !hasShowedLandedOnPdtWarning
  ) {
    return (
      <PdtLandedWarning changeLandedOnPdtWarning={changeLandedOnPdtWarning} />
    )
  }

  return (
    <Container white>
      {(status === 'NEW' || status === 'LANDING_SCREEN') && NoAccount()}
      {status === 'ACCEPTED' && <HomeContent />}
      {!status && (
        <StocksErrorState
          retry={refetchAccountStatus}
          isLoading={isFetchingAccountStatus}
        />
      )}
    </Container>
  )
}

const FooterContainer = styled.View`
  background-color: ${p => p.theme.colors.bgBase};
  position: relative;
  bottom: 0;
  width: 100%;
  padding-vertical: ${p => p.theme.spacing.s8};
  padding-horizontal: ${p => p.theme.spacing.s20};
  box-shadow: 0.5px -0.5px 10px rgba(0, 0, 0, 0.06);
`

const NotifyMeContainer = styled.View`
  padding: ${p => p.theme.spacing.s16};
  background-color: ${p => p.theme.colors.brandPrimary40};
  border-radius: ${p => p.theme.borderRadius.medium};
`

const BadgeRow = styled.View`
  flex-direction: row;
  padding-bottom: 24px;
`

const InvestTitle = styled(Text)`
  padding-top: 40px;
`
