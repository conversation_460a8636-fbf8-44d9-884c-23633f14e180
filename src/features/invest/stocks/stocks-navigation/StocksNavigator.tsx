import React from 'react'

import { createStackNavigator } from '@react-navigation/stack'

import { routeConst } from '@navigation'
import { modalScreenConfig, stackScreenConfig } from '@navigation/config'

import { StocksCancelHeader } from '../stocks-components/StocksCancelHeader'
import { StocksMarketPickerHeader } from '../stocks-components/StocksMarketPickerHeader'
import * as screens from '../stocks-screens'
import { types } from '../stocks-utils'
import { Landing as StocksLanding } from '../stocks-screens'

const Stack = createStackNavigator<types.StocksParamList>()

export const StocksActiveScreenGroup = () => (
  <Stack.Group screenOptions={stackScreenConfig()}>
    <Stack.Screen
      name={routeConst.STOCKS_PRICE}
      component={screens.Price}
      options={{
        headerShown: false
      }}
    />
    <Stack.Screen
      name={routeConst.STOCKS_CANCEL}
      component={screens.Cancel}
      options={({ route }) => {
        return modalScreenConfig({
          customTitleComponent: (
            <StocksCancelHeader stockOrder={route.params?.stock} />
          )
        })
      }}
    />
    <Stack.Screen
      name={routeConst.STOCKS_BUY_SELL}
      component={screens.BuySell}
      options={({ route }) => {
        return stackScreenConfig({
          showBalance: {
            type: 'stocks',
            symbol: route.params?.stock?.symbol,
            action: route.params.mode
          },
          customRightIcon: <StocksMarketPickerHeader />
        })
      }}
    />

    <Stack.Screen
      name={routeConst.STOCKS_VOUCHER}
      component={screens.StockVoucher}
      options={{ title: 'Vouchers' }}
    />
  </Stack.Group>
)

export const StocksNavigator = () => {
  return (
    <Stack.Navigator screenOptions={stackScreenConfig()}>
      <Stack.Screen
        name={routeConst.INVEST_STOCKS_TAB}
        component={StocksLanding}
        options={{
          title: 'Stocks'
        }}
      />
      <Stack.Screen
        name={routeConst.STOCKS_APPLICATION}
        component={screens.Application}
      />
      <Stack.Screen
        name={routeConst.STOCKS_APPLICATION_CONTACT_INFORMATION}
        component={screens.ApplicationContactInformation}
        options={{
          title: 'Contact Information'
        }}
      />
      <Stack.Screen
        name={routeConst.STOCKS_QUESTIONS}
        component={screens.Questions}
        options={{ headerShown: false }}
      />
      <Stack.Screen
        name={routeConst.STOCKS_INVESTOR_PROFILE_QUESTIONS}
        component={screens.InvestorProfileQuestions}
        options={{
          headerShown: false
        }}
      />
      <Stack.Screen
        name={routeConst.STOCKS_ACCOUNT_STATEMENTS}
        component={screens.AccountStatements}
        options={({ route }) => {
          return { title: route?.params?.name }
        }}
      />

      <Stack.Screen
        name={routeConst.STOCKS_CATEGORIES}
        component={screens.CategorySearch}
        options={{ title: 'Search Stocks' }}
      />

      <Stack.Screen
        name={routeConst.STOCKS_CATEGORY}
        component={screens.StockCategory}
      />
    </Stack.Navigator>
  )
}
