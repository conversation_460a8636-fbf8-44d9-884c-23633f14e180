import { StackScreenProps } from '@react-navigation/stack'
import { StackNavigationProp } from '@react-navigation/stack'

import {
  FieldConfiguration,
  FieldKind,
  SelectFieldPresentation
} from '@shared/features/dynamic-form/dynamic-form-utils/types'
import { TransactionType } from '@shared/types'

import { routeConst } from '@navigation'
import { RedirectParam } from '@navigation/helpers'

export type EmergencyContactData = {
  firstName: string
  surName: string
  phoneNumber: { country: string; phone: string }
}

export type NextOfKinData = {
  firstName: string
  surName: string
  dateOfBirth: string
}

export type CombinedContactData = {
  emergencyContact: EmergencyContactData
  nextOfKin: NextOfKinData
  isNextOfKinSame: boolean
}

export type FinanceProfileData = {
  employmentStatus: string
  institutionName?: string | null
  annualIncome: string
  sourceOfFunds: string | string[]
  profession: string
}

export type BankDetailsData = {
  accountName: string
  branchName: string
  bankName: { bankId: string; bankName: string }
  accountNumber: string
}

export type FinanceProfileField = {
  kind: FieldKind.SELECT | FieldKind.TEXT | FieldKind.MULTI_SELECT
  name: string
  label: string
  required: boolean
  placeholder?: string
  options?: Array<{ label: string; value: string }>
  defaultValue?: string | string[] | { country?: string; phone?: string }
  value?: string | string[]
  presentation?: SelectFieldPresentation
  type?: string
  invalid?: string
  hint?: string
}

export interface MutualFundsFormData {
  emergencyContact?: EmergencyContactData
  nextOfKin?: NextOfKinData
  financeProfile?: FinanceProfileData
  bankDetails?: BankDetailsData
}

export type MutualFundsParamList = {
  [routeConst.MUTUAL_FUNDS]: undefined
  [routeConst.MUTUAL_FUNDS_INVESTMENT_PROFILE]:
    | { financeProfile: FinanceProfileData }
    | { bankDetails: BankDetailsData }
    | { conbinedContact: CombinedContactData }
    | undefined
  [routeConst.MUTUAL_FUNDS_LANDING_V2]: undefined
  [routeConst.MUTUAL_FUNDS_EMERGENCY_CONTACT_AND_NEXT_OF_KIN]: {
    fields: {
      emergencyContact: any
      nextOfKin: any
    }
  }
  [routeConst.MUTUAL_FUNDS_FINANCE_PROFILE]: {
    fields: FinanceProfileField[]
  }
  [routeConst.MUTUAL_FUNDS_BANK_DETAILS]: {
    fields: FieldConfiguration[]
    country: string
  }
  [routeConst.MUTUAL_FUNDS_SUCCESS_V2]:
    | { transactionType?: TransactionType; paymentMethod?: string }
    | undefined
  [routeConst.VERIFICATION_KYC_LIVENESS_VERIFACE]: {
    standalone: boolean
  }
  [routeConst.VERIFICATION_KYC_DOC_OPTION]: {
    source: string
    isSecondary: boolean
    supportedIDTypes: any[]
    onCompletion: () => void
  }
  [routeConst.DYNAMIC_FORM_BANK_SELECTION]: {
    formKey?: string
    fieldName: string
    redirect?: RedirectParam
    currency: string
    country: string
  }
  [routeConst.MUTUAL_FUNDS_AMOUNT_INPUT_SCREEN]: { transactionType: TransactionType }
  [routeConst.MUTUAL_FUND_DETAIL_SCREEN]: { hasInvestedInMutualFund: boolean }
  [routeConst.MUTUAL_FUNDS_HOME_SCREEN]: undefined
  [routeConst.MUTUAL_FUNDS_PRODUCT_ONBOARDING_SCREEN]: undefined
  [routeConst.MUTUAL_FUNDS_PAYMENT_METHOD_SELECTION_SCREEN]: undefined
  [routeConst.MUTUAL_FUNDS_REVIEW_DETAILS_SCREEN]: undefined
}

export type MutualFundsInvestmentProfileScreenProps = StackScreenProps<
  MutualFundsParamList,
  typeof routeConst.MUTUAL_FUNDS_INVESTMENT_PROFILE
>

export type MutualFundsEmergencyContactScreenProps = {
  navigation: StackNavigationProp<any>
  route: {
    params: {
      fields: {
        emergencyContact: FieldConfiguration[]
        nextOfKin: FieldConfiguration[]
      }
    }
  }
}

export type MutualFundsFinanceProfileScreenProps = StackScreenProps<
  MutualFundsParamList,
  typeof routeConst.MUTUAL_FUNDS_FINANCE_PROFILE
>

export type MutualFundsBankDetailsScreenProps = StackScreenProps<
  MutualFundsParamList,
  typeof routeConst.MUTUAL_FUNDS_BANK_DETAILS
>

export type MutualFundsApplicationScreenProps = StackScreenProps<
  MutualFundsParamList,
  typeof routeConst.MUTUAL_FUNDS
>

export type MutualFundsProductOnboardingScreenProps = StackScreenProps<
  MutualFundsParamList,
  typeof routeConst.MUTUAL_FUNDS_PRODUCT_ONBOARDING_SCREEN
>

export type MutualFundsAmountInputScreenProps = StackScreenProps<
  MutualFundsParamList,
  typeof routeConst.MUTUAL_FUNDS_AMOUNT_INPUT_SCREEN
>
export type MutualFundsPaymentMethodSelectionScreenProps = StackScreenProps<
  MutualFundsParamList,
  typeof routeConst.MUTUAL_FUNDS_PAYMENT_METHOD_SELECTION_SCREEN
>

export type MutualFundsReviewDetailsScreenProps = StackScreenProps<
  MutualFundsParamList,
  typeof routeConst.MUTUAL_FUNDS_REVIEW_DETAILS_SCREEN
>

export type MutualFundsSuccessScreenV2Props = StackScreenProps<
  MutualFundsParamList,
  typeof routeConst.MUTUAL_FUNDS_SUCCESS_V2
>

export type MutualFundsDetailScreenProps = StackScreenProps<
  MutualFundsParamList,
  typeof routeConst.MUTUAL_FUND_DETAIL_SCREEN
>
