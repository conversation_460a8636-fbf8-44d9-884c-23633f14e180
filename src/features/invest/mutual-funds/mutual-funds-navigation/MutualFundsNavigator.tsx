import React from 'react'

import { Box } from '@chippercash/chipper-ui'
import { createStackNavigator } from '@react-navigation/stack'
import { useSelector } from 'react-redux'

import { mutualFundSlice } from '@shared/store'
import { TransactionType } from '@shared/types'

import { routeConst } from '@navigation'
import { stackScreenConfig } from '@navigation/config'

import * as screen from '../mutual-funds-screens'
import { MutualFundsParamList } from '../mutual-funds-utils'

const Stack = createStackNavigator<MutualFundsParamList>()

export const MutualFundsNavigator = () => {
  const fundName = useSelector(mutualFundSlice.selectors.selectedMutualFundName)
  return (
    <Stack.Navigator screenOptions={stackScreenConfig()}>
      <Stack.Screen
        name={routeConst.MUTUAL_FUNDS}
        component={screen.MutualFundsApplication}
        options={{
          title: 'Mutual Funds'
        }}
      />
      <Stack.Screen
        name={routeConst.MUTUAL_FUNDS_INVESTMENT_PROFILE}
        component={screen.MutualFundsInvestmentProfileScreen}
        options={{
          title: 'Chipper Investment Profile',
          headerShown: true
        }}
      />

      <Stack.Screen
        name={routeConst.MUTUAL_FUNDS_LANDING_V2}
        component={screen.MutualFundsLandingScreenV2}
        options={{
          title: 'Mutual funds',
          headerShown: true
        }}
      />

      <Stack.Screen
        name={routeConst.MUTUAL_FUNDS_EMERGENCY_CONTACT_AND_NEXT_OF_KIN}
        component={screen.MutualFundsEmergencyContactAndNextOfKinScreen}
        options={{
          title: 'Emergency contact and next of kin',
          headerShown: true
        }}
      />

      <Stack.Screen
        name={routeConst.MUTUAL_FUNDS_BANK_DETAILS}
        component={screen.MutualFundsBankDetailsScreen}
        options={{
          headerShown: true,
          title: 'Bank details'
        }}
      />

      <Stack.Screen
        name={routeConst.MUTUAL_FUNDS_FINANCE_PROFILE}
        component={screen.MutualFundsFinanceProfileScreen}
        options={{
          title: 'Employment details',
          headerShown: true
        }}
      />

      <Stack.Screen
        name={routeConst.MUTUAL_FUNDS_SUCCESS_V2}
        component={screen.MutualFundsSuccessScreenV2}
        options={{
          headerShown: false
        }}
      />

      <Stack.Screen
        name={routeConst.MUTUAL_FUNDS_HOME_SCREEN}
        component={screen.MutualFundsHomeScreen}
        options={{
          title: 'Mutual funds',
          headerShown: true,
          headerBackground: () => (
            <Box flex={1} backgroundColor={'bgLayerOne'} />
          )
        }}
      />

      <Stack.Screen
        name={routeConst.MUTUAL_FUNDS_PRODUCT_ONBOARDING_SCREEN}
        component={screen.MutualFundsProductOnboardingScreen}
        options={{
          title: 'Mutual funds',
          headerShown: true
        }}
      />

      <Stack.Screen
        name={routeConst.MUTUAL_FUNDS_AMOUNT_INPUT_SCREEN}
        component={screen.MutualFundsAmountInputScreen}
        options={({ route }) => {
          const transactionType = route?.params?.transactionType
          const title =
            transactionType === TransactionType.DEPOSIT ? 'Top up' : 'Withdrawal'
          return stackScreenConfig({
            title,
            headerShadowVisible: false
          })
        }}
      />

      <Stack.Screen
        name={routeConst.MUTUAL_FUNDS_PAYMENT_METHOD_SELECTION_SCREEN}
        component={screen.MutualFundsPaymentMethodSelectionScreen}
        options={{
          title: 'Select payment method',
          headerShown: true
        }}
      />

      <Stack.Screen
        name={routeConst.MUTUAL_FUNDS_REVIEW_DETAILS_SCREEN}
        component={screen.MutualFundsReviewDetailsScreen}
        options={{
          title: 'Review details',
          headerShown: true
        }}
      />

      <Stack.Screen
        name={routeConst.MUTUAL_FUND_DETAIL_SCREEN}
        component={screen.MutualFundsDetailScreen}
        options={{
          title: fundName,
          headerShadowVisible: false,
          headerBackground: () => (
            <Box flex={1} backgroundColor={'bgLayerOne'} />
          )
        }}
      />
    </Stack.Navigator>
  )
}
