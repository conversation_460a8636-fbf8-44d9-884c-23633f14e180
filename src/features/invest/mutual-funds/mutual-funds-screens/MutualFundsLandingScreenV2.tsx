import React, { useEffect, useMemo, useState } from 'react'
import { ScrollView } from 'react-native'

import {
  Box,
  Button,
  Checkbox,
  Container,
  FixedFooter,
  Icon,
  Image,
  Spinner,
  Stack,
  Text,
  defaultAnalyticEvents
} from '@chippercash/chipper-ui'
import styled from 'styled-components/native'

import { ErrorContactSupportState } from '@shared/components'
import { analytics, coreService, errorService } from '@shared/services'
import { MutualFundsAccountStatus } from '@shared/services/api/core'

import { NavigationService, routeConst } from '@navigation'

export const MutualFundsLandingScreenV2 = () => {
  const {
    data: accountStatus,
    isLoading: isLoadiingAccountStatus,
    refetch: refetchAccountStatus,
    isError: isErrorFetchingAccountStatus,
    error: fetchAccountStatusError
  } = coreService.useGetAccountStatusQuery()
  const userAccountStatus = accountStatus?.status as MutualFundsAccountStatus

  const {
    data: configuration,
    isLoading: isLoadingConfig,
    refetch: refetchConfiguration,
    isError: isErrorFetchingConfiguration,
    error: fetchConfigurationError
  } = coreService.useGetConfigurationQuery()

  const errorMessage =
    errorService.errorMessage(fetchConfigurationError as Error) ||
    errorService.errorMessage(fetchAccountStatusError as Error) ||
    'We’re unable to load mutual funds details at this time. Please try again'

  const [checkTerms, setCheckTerms] = useState<boolean>(false)

  useEffect(() => {
    refetchAccountStatus()
    refetchConfiguration()
  }, [refetchAccountStatus, refetchConfiguration])

  const badges = useMemo(() => {
    return configuration?.length ? configuration : []
  }, [configuration])

  const investMutualFundsTermsUrl =
    'https://support.chippercash.com/en/articles/8118457-terms-of-use-ghana'

  const handleLinkPressed = () => {
    NavigationService.navigate(routeConst.WEB_VIEW_SCREEN, {
      url: investMutualFundsTermsUrl,
      title: 'Mutual Funds'
    })
  }

  const handleBlackStarLinkPressed = () => {
    NavigationService.navigate(routeConst.WEB_VIEW_SCREEN, {
      url: 'https://blackstargroup.ai/about',
      title: 'Black Star'
    })
  }

  const handleCheckBoxPressed = () => {
    if (checkTerms === false) {
      setCheckTerms(true)
    } else {
      setCheckTerms(false)
    }
  }

  const handleBeginInvesting = async () => {
    analytics.track(analytics.events.MUTUAL_FUNDS_BEGIN_INVESTING_TAPPED)
    NavigationService.navigate(routeConst.MUTUAL_FUNDS_INVESTMENT_PROFILE, {})
  }

  const renderTerms = () => {
    return (
      <Text type="bodySmall">
        I agree to the {''}
        <Text
          color="brandPrimary100"
          onPress={handleLinkPressed}
          type="bodySmall">
          {'Terms and Conditions'}
        </Text>
        . I also confirm that I'm not conducting this transaction on behalf of
        another person.
      </Text>
    )
  }

  //   Show spinner when loading account status and configurations
  if (isLoadingConfig || isLoadiingAccountStatus) {
    return (
      <Container white>
        <Stack
          gutterVertical="s24"
          alignItems="center"
          justifyContent="center"
          fullWidth
          style={{ flex: 1 }}>
          <Spinner size="large" />
        </Stack>
      </Container>
    )
  }

  if (isErrorFetchingConfiguration || isErrorFetchingAccountStatus) {
    return (
      <ErrorContactSupportState
        title={'Woops!'}
        supportMessage={
          "Hi, I'm receiving an error when trying to view the mutual funds tab"
        }
        subtitle={errorMessage}
        retry={() => refetchAccountStatus()}
        supportAnalyticEvent={
          analytics.events.MUTUAL_FUNDS_ACCOUNT_SUPPORT_TAPPED
        }
        retryAnalyticEvent={analytics.events.MUTUAL_FUNDS_ACCOUNT_RETRY_TAPPED}
      />
    )
  }

  return (
    <Container white>
      <ScrollView>
        <Box flex={1} gutterHorizontal="s24">
          <Box>
            <Image
              style={{
                width: 328,
                height: 274
              }}
              accessibilityIgnoresInvertColors
              source={require('@images/mutual-funds/landing-card.png')}
            />
          </Box>
          <Box flex={1}>
            <Text spacing="s20" type="h1">
              Mutual Funds
            </Text>
          </Box>
          {badges.map(({ title, subtitle, icon }) => {
            return (
              <Box gap={20} key={title}>
                <>
                  <Box gutterVertical="s12" flexDirection={'row'}>
                    <Icon name={icon} customSize={20} color="brandPrimary100" />
                    <Container gutterHorizontal="s8">
                      <Text type="listMedium">{title}</Text>

                      <Text
                        parse={[
                          {
                            // bold amount and figure e.g. GHS 100 or USD 100
                            pattern:
                              /([A-Z]{3}|[A-Z]{2}₵)\s?(\d+(?:\.\d{2})?)/g,
                            textProps: {
                              weightOverride: 'primarySemiBold'
                            }
                          }
                        ]}
                        spacing="s4"
                        type="bodySmall">
                        {subtitle}
                      </Text>
                    </Container>
                  </Box>
                </>
              </Box>
            )
          })}
          <Box gutterTop="s24">
            <StyledCheckbox
              text={renderTerms()}
              checked={checkTerms}
              onPress={handleCheckBoxPressed}
              analyticEventName={defaultAnalyticEvents.NONE}
            />
          </Box>
        </Box>
      </ScrollView>

      <FixedFooter layoutMode="relative">
        <ButtomTextContainer>
          <Text alignment="center" type="bodySmall" color="textSecondary">
            Mutual Funds powered by {''}
            <Text
              color="brandPrimary100"
              weightOverride="primarySemiBold"
              onPress={handleBlackStarLinkPressed}
              type="bodySmall">
              {'Black star'}
            </Text>
          </Text>
        </ButtomTextContainer>
        <Button
          title={
            userAccountStatus === MutualFundsAccountStatus.LANDING_SCREEN
              ? 'Continue Investing'
              : 'Begin Investing'
          }
          onPress={handleBeginInvesting}
          analyticEventName={
            userAccountStatus === MutualFundsAccountStatus.LANDING_SCREEN
              ? analytics.events.MUTUAL_FUNDS_CONTINUE_INVESTING_TAPPED
              : analytics.events.MUTUAL_FUNDS_BEGIN_INVESTING_TAPPED
          }
          disabled={!checkTerms}
          loading={isLoadingConfig || isLoadiingAccountStatus}
        />
      </FixedFooter>
    </Container>
  )
}

const StyledCheckbox = styled(Checkbox)`
  margin-bottom: ${p => p.theme.spacing.s16};
`
const ButtomTextContainer = styled.View`
  padding-horizontal: ${p => p.theme.spacing.s8};
  padding-vertical: ${p => p.theme.spacing.s16};
`
