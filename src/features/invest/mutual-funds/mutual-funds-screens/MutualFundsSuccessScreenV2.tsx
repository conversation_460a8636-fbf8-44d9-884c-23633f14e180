import React, { useEffect } from 'react'

import {
  <PERSON><PERSON>,
  Container,
  FixedFooter,
  Image,
  Text,
  defaultAnalyticEvents,
  normalize
} from '@chippercash/chipper-ui'
import { useDispatch } from 'react-redux'
import styled from 'styled-components/native'

import { ErrorActionContainer } from '@shared/components'
import { mutualFundSlice } from '@shared/store'
import { TransactionType } from '@shared/types'

import { NavigationService, routeConst } from '@navigation'

import { MutualFundsSuccessScreenV2Props } from '../mutual-funds-utils'
import { ButtomTextContainer } from './MutualFundsReviewDetailsScreen'

export const MutualFundsSuccessScreenV2 = ({
  route,
  navigation
}: MutualFundsSuccessScreenV2Props) => {
  const { transactionType, paymentMethod } = route?.params || {}

  const dispatch = useDispatch()

  useEffect(() => {
    dispatch(mutualFundSlice.actions.resetMutualFundState())
  }, [dispatch])

  const handleDonePressed = () => {
    NavigationService.popToTop()
  }
  const handleBlackStarLinkPressed = () => {
    NavigationService.navigate(routeConst.WEB_VIEW_SCREEN, {
      url: 'https://blackstargroup.ai/about',
      title: 'Black Star'
    })
  }
  if (paymentMethod === 'momo' && transactionType === TransactionType.DEPOSIT) {
    return (
      <Container white>
        <ErrorActionContainer
          imageSize={{
            height: 205,
            width: 330
          }}
          imagePosition="top"
          textAlign="left"
          imageSrc={require('@images/transaction_requires_approval.png')}
          title={`We are waiting for a confirmation from your ${paymentMethod} provider`}
          subtitle={
            'You will receive a USSD pop-up on your device to enter your mobile money PIN and approve this transaction.'
          }
          primaryButton={{
            analyticEventName: defaultAnalyticEvents.NONE,
            title: 'Done',
            onPress: () =>
              navigation.navigate(routeConst.MUTUAL_FUNDS_HOME_SCREEN)
          }}
        />
      </Container>
    )
  }

  const title =
    transactionType === TransactionType.DEPOSIT
      ? 'You have sucessfully topped up'
      : 'Withdrawal request successful'

  const message =
    transactionType === TransactionType.DEPOSIT
      ? 'Go and grow your money'
      : 'Withdrawals can take up to 24 business hours to process'

  return (
    <Container white>
      <InnerContainer>
        <ImageContainer>
          <StyledImage
            source={require('@images/mutual-funds/check-mark.png')}
          />
          <TitleText alignment="center" type="h4SemiBold">
            {/* Same screen is used for onboarding conpletion */}
            {transactionType
              ? title
              : 'Thank you for submitting your KYC information'}
          </TitleText>
          <Text type="bodyDefault" alignment="center">
            {transactionType
              ? message
              : 'You will be notified once your investment profile is ready.'}
          </Text>
        </ImageContainer>
      </InnerContainer>

      <FixedFooter layoutMode="relative">
        <ButtomTextContainer>
          <Text alignment="center" type="bodySmall" color="textSecondary">
            Mutual Funds powered by {''}
            <Text
              color="brandPrimary100"
              weightOverride="primarySemiBold"
              onPress={handleBlackStarLinkPressed}
              type="bodySmall">
              {'Black star'}
            </Text>
          </Text>
        </ButtomTextContainer>
        <Button
          title={'Done'}
          onPress={handleDonePressed}
          analyticEventName={defaultAnalyticEvents.NONE}
        />
      </FixedFooter>
    </Container>
  )
}

const imageSize = 150

const StyledImage = styled(Image)`
  align-items: center;
  height: ${normalize(imageSize)}px;
  width: ${normalize(imageSize)}px;
`

const ImageContainer = styled.View`
  align-items: center;
  margin-bottom: ${normalize(70)}px;
`

const InnerContainer = styled.View`
  align-items: center;
  flex: 1;
  justify-content: space-between;
  padding-top: ${p => p.theme.spacing.s72};
  padding-bottom: ${p => p.theme.spacing.s24};
  padding-horizontal: ${p => p.theme.spacing.s24};
`

const TitleText = styled(Text)`
  margin-top: ${p => p.theme.spacing.s24};
`
