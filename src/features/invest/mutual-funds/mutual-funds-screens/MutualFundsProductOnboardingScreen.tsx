import React, { useMemo, useState } from 'react'
import { ScrollView } from 'react-native'

import {
  Box,
  Button,
  Checkbox,
  Container,
  FixedFooter,
  Icon,
  Spinner,
  Stack,
  Text,
  defaultAnalyticEvents
} from '@chippercash/chipper-ui'
import { useDispatch, useSelector } from 'react-redux'
import styled from 'styled-components/native'

import { ErrorContactSupportState } from '@shared/components'
import { analytics, coreService, errorService } from '@shared/services'
import { mutualFundSlice } from '@shared/store'

import { NavigationService, routeConst } from '@navigation'

import { MutualFundsProductOnboardingScreenProps } from '../mutual-funds-utils'
import { TransactionType } from '@shared/types'

export const MutualFundsProductOnboardingScreen = ({
  navigation
}: MutualFundsProductOnboardingScreenProps) => {
  const {
    selectedMutualFundInvestmentId,
    selectedMutualFundSymbol,
    selectedMutualFundDocumentUrl
  } = mutualFundSlice.selectors
  const documentation_url = useSelector(selectedMutualFundDocumentUrl)
  const symbol = useSelector(selectedMutualFundSymbol)
  const investmentId = useSelector(selectedMutualFundInvestmentId)

  const [checkTerms, setCheckTerms] = useState<boolean>(false)

  const {
    data: configuration,
    isLoading: isLoadingConfig,
    refetch: refetchConfiguration,
    isError: isErrorFetchingConfiguration,
    error: fetchConfigurationError
  } = coreService.useGetConfigurationQuery()

  const errorMessage =
    errorService.errorMessage(fetchConfigurationError as Error) ||
    'We’re unable to load mutual funds details at this time. Please try again'

  const badges = useMemo(() => {
    return configuration?.length ? configuration : []
  }, [configuration])

  const handleLinkPressed = () => {
    NavigationService.navigate(routeConst.WEB_VIEW_SCREEN, {
      url: documentation_url,
      title: 'Mutual Funds'
    })
  }

  const handleBlackStarLinkPressed = () => {
    NavigationService.navigate(routeConst.WEB_VIEW_SCREEN, {
      url: 'https://blackstargroup.ai/about',
      title: 'Black Star'
    })
  }

  const handleCheckBoxPressed = () => {
    if (checkTerms === false) {
      setCheckTerms(true)
    } else {
      setCheckTerms(false)
    }
  }

  const dispatch = useDispatch()

  const handleContinueInvesting = () => {
    analytics.track(analytics.events.MUTUAL_FUNDS_CONTINUE_INVESTING_TAPPED)
    dispatch(mutualFundSlice.actions.setInvestmentId(investmentId))
    navigation.navigate(routeConst.MUTUAL_FUNDS_AMOUNT_INPUT_SCREEN, {transactionType: TransactionType.DEPOSIT})
  }

  //   Show spinner when loading configurations
  if (isLoadingConfig) {
    return (
      <Container white>
        <Stack
          gutterVertical="s24"
          alignItems="center"
          justifyContent="center"
          fullWidth
          style={{ flex: 1 }}>
          <Spinner size="large" />
        </Stack>
      </Container>
    )
  }

  if (isErrorFetchingConfiguration) {
    return (
      <ErrorContactSupportState
        title={'Woops!'}
        supportMessage={
          "Hi, I'm receiving an error when trying to view the mutual funds tab"
        }
        subtitle={errorMessage}
        retry={() => refetchConfiguration()}
        supportAnalyticEvent={
          analytics.events.MUTUAL_FUNDS_ACCOUNT_SUPPORT_TAPPED
        }
        retryAnalyticEvent={analytics.events.MUTUAL_FUNDS_ACCOUNT_RETRY_TAPPED}
      />
    )
  }

  const renderTerms = () => {
    return (
      <Text type="bodySmall">
        I have read the {''}
        <Text
          color="brandPrimary100"
          onPress={handleLinkPressed}
          type="bodySmall">
          {'Fund documentation'}
        </Text>
        . I also confirm that I'm not conducting this transaction on behalf of
        another person.
      </Text>
    )
  }

  return (
    <Container white>
      <ScrollView>
        <Box flex={1} gutterHorizontal="s24" gutterTop={'s24'}>
          <Box flex={1}>
            <Text spacing="s20" type="h2" color="textPrimary">
              {symbol}
            </Text>
          </Box>
          {badges.map(({ title, subtitle, icon }) => {
            return (
              <Box gap={20} key={title}>
                <>
                  <Box gutterVertical="s12" flexDirection={'row'}>
                    <Icon name={icon} customSize={20} color="brandPrimary100" />
                    <Container gutterHorizontal="s8">
                      <Text type="listMedium">{title}</Text>

                      <Text spacing="s4" type="bodySmall">
                        {subtitle}
                      </Text>
                    </Container>
                  </Box>
                </>
              </Box>
            )
          })}
          <Box gutterTop="s24">
            <StyledCheckbox
              text={renderTerms()}
              checked={checkTerms}
              onPress={handleCheckBoxPressed}
              analyticEventName={defaultAnalyticEvents.NONE}
            />
          </Box>
        </Box>
      </ScrollView>

      <FixedFooter layoutMode="relative">
        <ButtomTextContainer>
          <Text alignment="center" type="bodySmall" color="textSecondary">
            Mutual Funds powered by {''}
            <Text
              color="brandPrimary100"
              weightOverride="primarySemiBold"
              onPress={handleBlackStarLinkPressed}
              type="bodySmall">
              {'Black star Advisors'}
            </Text>
          </Text>
        </ButtomTextContainer>
        <Button
          title={'Continue'}
          onPress={handleContinueInvesting}
          analyticEventName={
            analytics.events.MUTUAL_FUNDS_PRODUCT_CONTINUE_ONBOARDING_TAPPED
          }
          disabled={!checkTerms}
        />
      </FixedFooter>
    </Container>
  )
}

const StyledCheckbox = styled(Checkbox)`
  margin-bottom: ${p => p.theme.spacing.s16};
`
const ButtomTextContainer = styled.View`
  padding-horizontal: ${p => p.theme.spacing.s8};
  padding-vertical: ${p => p.theme.spacing.s16};
`
