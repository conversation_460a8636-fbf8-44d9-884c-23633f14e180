import React, { useCallback, useEffect, useState } from 'react'
import { RefreshControl, ScrollView } from 'react-native'

import {
  Container,
  ListItem,
  Spinner,
  Stack,
  Text,
  defaultAnalyticEvents,
  theme
} from '@chippercash/chipper-ui'
import { FlashList } from '@shopify/flash-list'
import { useDispatch } from 'react-redux'

import { ErrorContactSupportState } from '@shared/components'
import { coreService, errorService } from '@shared/services'
import { analytics } from '@shared/services'
import { mutualFundSlice } from '@shared/store'

import { routeConst } from '@navigation'

import { PortfolioBalance } from '../mutual-funds-components/portfolio/PortfolioBalance'
import { PortfolioOverview } from '../mutual-funds-components/portfolio/PortfolioOverview'
import { PortfolioSegments } from '../mutual-funds-components/portfolio/PortfolioSegment'
import { TransactionsSection } from '../mutual-funds-components/transactions/TransactionsList'
import { MutualFundsApplicationScreenProps } from '../mutual-funds-utils'

export const MutualFundsHomeScreen = ({
  navigation
}: MutualFundsApplicationScreenProps) => {
  const {
    data: products,
    isLoading: isLoadingProducts,
    refetch: refetchProducts,
    isError: isErrorFetchingProducts,
    error: fetchInvestmentProductsError
  } = coreService.useGetAllInvestmentProductsQuery()

  const {
    data: overviewDetails,
    isLoading: isLoadingOverviewDetails,
    refetch: refetchOverviewDetails,
    isError: isErrorFetchingOverviewDetails,
    error: fetchOverviewDetailsError
  } = coreService.useGetAccountOverviewQuery()

  const refetchMutualFundsDetails = useCallback(async () => {
    refetchProducts()
    refetchOverviewDetails()
  }, [refetchOverviewDetails, refetchProducts])

  const [activeSegment, setActiveSegment] = useState<string>('overview')

  useEffect(() => {
    refetchMutualFundsDetails()
  }, [refetchMutualFundsDetails])

  const dispatch = useDispatch()

  const handleMutualFundProductTapped = (
    documentation_url: string,
    symbol: string,
    investmentId: string
  ) => {
    const { setDocumentUrl, setSymbol, setInvestmentId } =
      mutualFundSlice.actions
    dispatch(setDocumentUrl(documentation_url))
    dispatch(setSymbol(symbol))
    dispatch(setInvestmentId(investmentId))
    navigation.navigate(routeConst.MUTUAL_FUNDS_PRODUCT_ONBOARDING_SCREEN)
  }

  const segments = [
    { label: 'Overview', value: 'overview' },
    { label: 'Transactions', value: 'transactions' }
  ]

  //   Show spinner when loading account status
  if (isLoadingProducts || isLoadingOverviewDetails) {
    return (
      <Container backgroundColor="bgLayerOne">
        <Stack
          gutterVertical="s24"
          alignItems="center"
          justifyContent="center"
          fullWidth
          style={{ flex: 1 }}>
          <Spinner size="large" />
        </Stack>
      </Container>
    )
  }

  if (isErrorFetchingProducts || isErrorFetchingOverviewDetails) {
    const investmentProductsErrorMessage =
      errorService.errorMessage(fetchInvestmentProductsError as Error) || ''
    const userInvestmentProductsErrorMessage =
      errorService.errorMessage(fetchOverviewDetailsError as Error) || ''
    const errorMessage =
      investmentProductsErrorMessage || userInvestmentProductsErrorMessage
    return (
      <Container justifyContent="flex-start" gutterHorizontal="s24">
        <ErrorContactSupportState
          title={'Woops!'}
          supportMessage={
            "Hi, I'm receiving an error when trying to view the mutual fund detail"
          }
          subtitle={errorMessage}
          retry={() => refetchMutualFundsDetails()}
          supportAnalyticEvent={
            analytics.events.MUTUAL_FUNDS_ACCOUNT_SUPPORT_TAPPED
          }
          retryAnalyticEvent={
            analytics.events.MUTUAL_FUNDS_ACCOUNT_RETRY_TAPPED
          }
        />
      </Container>
    )
  }

  if (overviewDetails?.activeInvestments.length === 0) {
    return (
      <Container>
        <FlashList
          data={products}
          estimatedItemSize={80}
          ListEmptyComponent={
            <Stack flexOne={false} gutterHorizontal="s24">
              <Text type="bodySmall" color="textSecondary">
                No recent purchases
              </Text>
            </Stack>
          }
          renderItem={({ item, index }) => {
            const { id, symbol, description, icon_url, documentation_url } =
              item

            const isLastItem = index >= (products?.length ?? 0) - 1

            return (
              <ListItem
                leadingElementType="logo"
                leadingElementProps={{ url: icon_url ?? undefined }}
                title={symbol}
                subTitle={description}
                analyticEventName={defaultAnalyticEvents.NONE}
                onPress={() =>
                  handleMutualFundProductTapped(documentation_url, symbol, id)
                }
                hasDivider={!isLastItem}
                alignTrailingElementItems="flex-start"
              />
            )
          }}
        />
      </Container>
    )
  }

  return (
    <Container backgroundColor="bgLayerOne">
      <PortfolioBalance overviewDetails={overviewDetails} />
      <PortfolioSegments
        activeSegment={activeSegment}
        onSegmentChange={setActiveSegment}
        segments={segments}
      />

      {activeSegment === 'overview' && (
        <ScrollView
          refreshControl={
            <RefreshControl
              refreshing={false}
              onRefresh={() => {
                refetchMutualFundsDetails()
              }}
              tintColor={theme.colors.brandPrimary100}
            />
          }>
          <PortfolioOverview overviewDetails={overviewDetails} />
        </ScrollView>
      )}

      {activeSegment === 'transactions' && <TransactionsSection />}
    </Container>
  )
}
