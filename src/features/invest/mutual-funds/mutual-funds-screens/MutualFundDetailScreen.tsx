import React, { useCallback, useEffect, useState } from 'react'
import { RefreshControl, ScrollView } from 'react-native'

import {
  ButtonGroup,
  ButtonGroupProps,
  Container,
  FixedFooter,
  Spinner,
  Stack,
  theme
} from '@chippercash/chipper-ui'
import { useDispatch, useSelector } from 'react-redux'

import { ErrorContactSupportState } from '@shared/components'
import { analytics, coreService, errorService } from '@shared/services'
import { mutualFundSlice } from '@shared/store'
import { TransactionType } from '@shared/types'

import { routeConst } from '@navigation'

import { DocumentsSection } from '../mutual-funds-components/documents/DocumentsSection'
import { PortfolioSegments } from '../mutual-funds-components/portfolio/PortfolioSegment'
import { ProductOverview } from '../mutual-funds-components/product/ProductOverview'
import { ProductPrice } from '../mutual-funds-components/product/ProductPrice'
import { ProductStatistics } from '../mutual-funds-components/product/ProductStatistics'
import { MutualFundsDetailScreenProps } from '../mutual-funds-utils'

export const MutualFundsDetailScreen = ({
  route,
  navigation
}: MutualFundsDetailScreenProps) => {
  const productId = useSelector(
    mutualFundSlice.selectors.selectedMutualFundInvestmentId
  )

  const [activeSegment, setActiveSegment] = useState<string>('overview')
  const [hasInvestedInMutualFund, setHasInvestedInMutualFund] =
    useState<boolean>(false)

  const {
    data: product,
    isLoading: isLoadingProduct,
    refetch: refetchProduct,
    isError: isErrorFetchingProduct,
    error: fetchProductError,
    isUninitialized: isUninitializedFetchProduct
  } = coreService.useGetInvestmentProductByIdQuery(
    { productId },
    {
      skip: hasInvestedInMutualFund
    }
  )

  const {
    data: userInvestment,
    isLoading: isLoadingUserInvestment,
    refetch: refetchUserInvestment,
    isError: isErrorFetchingUserInvestment,
    error: fetchUserInvestmentError,
    isUninitialized: isUninitializedFetchUserInvestment
  } = coreService.useGetUserInvestmentQuery(
    { id: productId },
    {
      skip: !hasInvestedInMutualFund
    }
  )

  const {
    data: documents,
    isLoading: isLoadingDocuments,
    refetch: refetchDocuments,
    isUninitialized: isUninitializedFetchDocuments
  } = coreService.useGetFundDocumentsQuery({ investmentId: productId })

  const refetchInvestmentDetails = useCallback(async () => {
    if (
      !isUninitializedFetchDocuments &&
      !isUninitializedFetchProduct &&
      !isUninitializedFetchUserInvestment
    ) {
      refetchProduct()
      refetchUserInvestment()
      refetchDocuments()
    }
  }, [
    isUninitializedFetchDocuments,
    isUninitializedFetchProduct,
    isUninitializedFetchUserInvestment,
    refetchDocuments,
    refetchProduct,
    refetchUserInvestment
  ])

  const dispatch = useDispatch()

  useEffect(() => {
    setHasInvestedInMutualFund(route.params.hasInvestedInMutualFund)
    dispatch(
      mutualFundSlice.actions.setInvestInMutualFund(hasInvestedInMutualFund)
    )
  }, [dispatch, hasInvestedInMutualFund, route.params.hasInvestedInMutualFund])

  const handleTopUpTapped = () => {
    if (hasInvestedInMutualFund) {
      navigation.navigate(routeConst.MUTUAL_FUNDS_AMOUNT_INPUT_SCREEN, {
        transactionType: TransactionType.DEPOSIT
      })
      return
    }
    navigation.navigate(routeConst.MUTUAL_FUNDS_PRODUCT_ONBOARDING_SCREEN)
  }

  const handleWithdrawTapped = () => {
    navigation.navigate(routeConst.MUTUAL_FUNDS_AMOUNT_INPUT_SCREEN, {
      transactionType: TransactionType.WITHDRAWAL
    })
  }

  let buttons: ButtonGroupProps['buttons'] = [
    {
      title: 'Top up',
      onPress: handleTopUpTapped,
      analyticEventName: analytics.events.MUTUAL_FUNDS_ACCOUNT_TOP_UP_TAPPED
    }
  ]

  if (hasInvestedInMutualFund) {
    buttons.push({
      title: 'Withdraw',
      onPress: handleWithdrawTapped,
      analyticEventName:
        analytics.events.MUTUAL_FUNDS_ACCOUNT_WITHDRAW_FUNDS_TAPPED,
      appearance: 'secondary'
    })
  }

  const segments = [
    { label: 'Overview', value: 'overview' },
    { label: 'Documents', value: 'documents' },
    { label: 'Statistics', value: 'statistics' }
  ]

  //   Show spinner when loading product and user investment
  if (isLoadingProduct || isLoadingUserInvestment || isLoadingDocuments) {
    return (
      <Container backgroundColor="bgLayerOne">
        <Stack
          gutterVertical="s24"
          alignItems="center"
          justifyContent="center"
          fullWidth
          style={{ flex: 1 }}>
          <Spinner size="large" />
        </Stack>
      </Container>
    )
  }

  if (isErrorFetchingProduct || isErrorFetchingUserInvestment) {
    const investmentProductErrorMessage =
      errorService.errorMessage(fetchUserInvestmentError as Error) || ''
    const userInvestmentErrorMessage =
      errorService.errorMessage(fetchProductError as Error) || ''
    const errorMessage =
      investmentProductErrorMessage || userInvestmentErrorMessage

    return (
      <ErrorContactSupportState
        title={'Woops!'}
        supportMessage={
          "Hi, I'm receiving an error when trying to view the mutual fund detail"
        }
        subtitle={errorMessage}
        retry={() => refetchInvestmentDetails()}
        supportAnalyticEvent={
          analytics.events.MUTUAL_FUNDS_ACCOUNT_SUPPORT_TAPPED
        }
        retryAnalyticEvent={analytics.events.MUTUAL_FUNDS_ACCOUNT_RETRY_TAPPED}
      />
    )
  }

  return (
    <Container backgroundColor="bgLayerOne">
      <ScrollView
        refreshControl={
          <RefreshControl
            refreshing={false}
            onRefresh={() => {
              refetchInvestmentDetails()
            }}
            tintColor={theme.colors.brandPrimary100}
          />
        }>
        <ProductPrice
          userInvestment={userInvestment}
          product={product}
          hasInvestedInMutualFund={hasInvestedInMutualFund}
        />
        <PortfolioSegments
          activeSegment={activeSegment}
          onSegmentChange={setActiveSegment}
          segments={segments}
        />

        {activeSegment === 'documents' && (
          <DocumentsSection fundDocumentsDetails={documents} />
        )}
        {activeSegment === 'overview' && (
          <ProductOverview
            userInvestment={userInvestment}
            product={product}
            hasInvestedInMutualFund={hasInvestedInMutualFund}
          />
        )}
        {activeSegment === 'statistics' && (
          <ProductStatistics
            userInvestment={userInvestment}
            product={product}
            hasInvestedInMutualFund={hasInvestedInMutualFund}
          />
        )}
      </ScrollView>

      <FixedFooter>
        <ButtonGroup direction="horizontal" buttons={buttons} />
      </FixedFooter>
    </Container>
  )
}
