import React, { useState } from 'react'
import { ScrollView } from 'react-native'

import {
  Box,
  Button,
  Container,
  FixedFooter,
  ListItem,
  Text,
  defaultAnalyticEvents
} from '@chippercash/chipper-ui'
import { useSelector } from 'react-redux'
import styled from 'styled-components/native'

import { coreService, errorService, format } from '@shared/services'
import { analytics } from '@shared/services'
import { mutualFundSlice } from '@shared/store'
import { TransactionType } from '@shared/types'

import { routeConst } from '@navigation'

import { MutualFundsReviewDetailsScreenProps } from '../mutual-funds-utils'

export const MutualFundsReviewDetailsScreen = ({
  navigation
}: MutualFundsReviewDetailsScreenProps) => {
  const {
    selectedMutualFundInvestmentId,
    selectedMutualFundInvestmentAmount,
    selectedMutualFundBeneficiaryDetails,
    selectedMutualFundPaymentMethod,
    selectedTransactionType
  } = mutualFundSlice.selectors
  const amount = useSelector(selectedMutualFundInvestmentAmount)
  const investmentId = useSelector(selectedMutualFundInvestmentId)
  const beneficiary = useSelector(selectedMutualFundBeneficiaryDetails)
  const paymentMethodDetails = useSelector(selectedMutualFundPaymentMethod)
  const transactionType = useSelector(selectedTransactionType)
  const { contactName, phoneNumber } = beneficiary || {}
  const { paymentMethod, source, carrier } = paymentMethodDetails || {}

  const [buyOrder] = coreService.useBuyOrderMutation()
  const [withdrawFunds] = coreService.useWithdrawFundsMutation()
  const [isProcessing, setIsProcessing] = useState<boolean>()

  const topUpTransaction = transactionType === TransactionType.DEPOSIT

  const handleContinueTapped = async () => {
    setIsProcessing(true)
    analytics.track(analytics.events.MUTUAL_FUNDS_CONTINUE_INVESTING_TAPPED)
    try {
      if (beneficiary && paymentMethodDetails) {
        if (topUpTransaction) {
          await buyOrder({
            body: {
              investmentId,
              amount: Number(amount),
              contactName: beneficiary.contactName,
              phoneNumber: beneficiary.phoneNumber,
              source: paymentMethodDetails.source
            }
          }).unwrap()
        } else {
          await withdrawFunds({
            body: {
              investmentId,
              amount: Number(amount),
              source: paymentMethodDetails.source
            }
          }).unwrap()
        }

        setIsProcessing(false)
        navigation.navigate(routeConst.MUTUAL_FUNDS_SUCCESS_V2, {
          transactionType,
          paymentMethod
        })
      }
    } catch (error) {
      errorService.handleError(error, `${transactionTitle} Mutual Fund product`)
    } finally {
      setIsProcessing(false)
    }
  }

  const formattedAmount = format.fiat.getFormattedValue(
    amount,
    source?.currency,
    {
      useCurrencyAsSymbol: true
    }
  )

  const transactionTitle = topUpTransaction ? 'Add' : 'Withdraw'

  return (
    <Container>
      <Box margin={'s8'}>
        <ScrollView>
          <Box gutterHorizontal="s24" gutterVertical="s24" flex={1}>
            <Text type="h3" spacing="s0">
              {`${transactionTitle} ${formattedAmount}`}
            </Text>
          </Box>

          <ListItem
            key="method"
            title="Payment method"
            size="short"
            analyticEventName={defaultAnalyticEvents.NONE}
            trailingElementType={'value'}
            trailingElementProps={{
              children:
                paymentMethod === 'momo' ? 'Mobile money' : paymentMethod,
              type: 'listMedium'
            }}
            shrinkTrailingElement={true}
          />

          <ListItem
            key="name"
            title="Name"
            size="short"
            analyticEventName={defaultAnalyticEvents.NONE}
            trailingElementType={'value'}
            trailingElementProps={{
              children: contactName,
              type: 'listMedium'
            }}
            shrinkTrailingElement={true}
          />

          <ListItem
            key="number"
            size="short"
            title={paymentMethod === 'momo' ? 'Phone number' : 'Account number'}
            analyticEventName={defaultAnalyticEvents.NONE}
            trailingElementType={'value'}
            trailingElementProps={{
              children: phoneNumber,
              type: 'listMedium'
            }}
            shrinkTrailingElement={true}
          />

          {paymentMethod === 'momo' && (
            <ListItem
              key="carrier"
              size="short"
              title="Mobile carrier"
              analyticEventName={defaultAnalyticEvents.NONE}
              trailingElementType={'value'}
              trailingElementProps={{
                children: carrier,
                type: 'listMedium'
              }}
              shrinkTrailingElement={true}
            />
          )}

          <ListItem
            key="total"
            size="short"
            title="You pay"
            analyticEventName={defaultAnalyticEvents.NONE}
            trailingElementType={'value'}
            trailingElementProps={{
              children: formattedAmount,
              type: 'listMedium'
            }}
            shrinkTrailingElement={true}
          />

          <ListItem
            key="recieve"
            title="You recieve"
            titleColor="brandPrimary100"
            size="short"
            analyticEventName={defaultAnalyticEvents.NONE}
            trailingElementType={'value'}
            trailingElementProps={{
              children: formattedAmount,
              type: 'listDefault',
              color: 'brandPrimary100'
            }}
            shrinkTrailingElement={true}
            hasDivider={false}
          />
        </ScrollView>
      </Box>

      <FixedFooter layoutMode="relative">
        {topUpTransaction && (
          <ButtomTextContainer>
            <Text alignment="center" type="bodySmall" color="textSecondary">
              To avoid failure, make sure you have enough money in your Mobile
              Money wallet
            </Text>
          </ButtomTextContainer>
        )}
        <Button
          onPress={() => handleContinueTapped()}
          title="Continue"
          analyticEventName={defaultAnalyticEvents.NONE}
          loading={isProcessing}
        />
      </FixedFooter>
    </Container>
  )
}

export const ButtomTextContainer = styled.View`
  padding-horizontal: ${p => p.theme.spacing.s8};
  padding-vertical: ${p => p.theme.spacing.s16};
`
