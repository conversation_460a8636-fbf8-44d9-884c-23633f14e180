import React, { useCallback, useState } from 'react'

import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON><PERSON>,
  Container,
  FixedFooter,
  defaultAnalyticEvents
} from '@chippercash/chipper-ui'
import { useDispatch, useSelector } from 'react-redux'

import { PaymentMethodSelectionInput } from '@shared/components/PaymentMethodSelectionInput'
import { Currency, mutualFundSlice } from '@shared/store'
import { BalanceKind, TransactionType } from '@shared/types'

import { routeConst } from '@navigation'

import { MutualFundsPaymentMethodSelectionScreenProps } from '../mutual-funds-utils'

export const MutualFundsPaymentMethodSelectionScreen = ({
  navigation
}: MutualFundsPaymentMethodSelectionScreenProps) => {
  type SourceType = {
    type: BalanceKind
    currency: Currency
    linkedAccountId: string
  }

  const [source, setSource] = useState<SourceType | undefined>(undefined)

  const dispatch = useDispatch()

  const transactionType = useSelector(
    mutualFundSlice.selectors.selectedTransactionType
  )

  const handlePaymentMethodSelected = useCallback(
    (paymentMethod: any) => {
      const selectedPaymentMethod = {
        type:
          paymentMethod && paymentMethod.kind === 'momo'
            ? BalanceKind.LinkedAccount
            : BalanceKind.Balance,
        currency: paymentMethod.currency,
        linkedAccountId: paymentMethod.linkedAccountId
      }
      const { setPaymentMethod, setBeneficairyDetails } =
        mutualFundSlice.actions
      dispatch(
        setPaymentMethod({
          source: selectedPaymentMethod,
          paymentMethod: paymentMethod?.kind,
          carrier: paymentMethod.carrier
        })
      )

      dispatch(
        setBeneficairyDetails({
          contactName: paymentMethod.title,
          phoneNumber: paymentMethod.subtitle
        })
      )
      setSource(selectedPaymentMethod)
    },
    [dispatch]
  )

  const handleNextTapped = () => {
    if (!source || Object.keys(source).length === 0) {
      Alert.alert({
        title: 'Ooops',
        message: 'Please select a payment method to continue',
        options: {
          cancelable: false
        },
        buttons: [
          {
            text: 'OK'
          }
        ]
      })
      return
    }
    navigation.navigate(routeConst.MUTUAL_FUNDS_REVIEW_DETAILS_SCREEN)
  }

  return (
    <Container>
      <Box margin={'s24'}>
        <AnimatedBox>
          <PaymentMethodSelectionInput
            label="Select payment method"
            onSelect={handlePaymentMethodSelected}
            appearance="outline"
            // Currently only momo payment methods is supported for deposits
            kind={
              transactionType === TransactionType.DEPOSIT
                ? 'momo'
                : 'momoAndBank'
            }
          />
        </AnimatedBox>
      </Box>

      <FixedFooter>
        <Button
          onPress={() => handleNextTapped()}
          title="Next"
          analyticEventName={defaultAnalyticEvents.NONE}
        />
      </FixedFooter>
    </Container>
  )
}
