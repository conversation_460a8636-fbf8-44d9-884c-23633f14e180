import React, { useEffect, useLayoutEffect } from 'react'

import { <PERSON><PERSON><PERSON>, <PERSON>ner, Stack } from '@chippercash/chipper-ui'

import { ErrorContactSupportState } from '@shared/components'
import { analytics, coreService, errorService } from '@shared/services'
import { MutualFundsAccountStatus } from '@shared/services/api/core'

import { routeConst } from '@navigation'

import { MutualFundsApplicationScreenProps } from '../mutual-funds-utils'

export const MutualFundsApplication = ({
  navigation
}: MutualFundsApplicationScreenProps) => {
  const {
    data: accountStatus,
    isLoading: isLoadingAccountStatus,
    refetch: refetchAccountStatus,
    isError,
    error
  } = coreService.useGetAccountStatusQuery()
  const userAccountStatus = accountStatus?.status as MutualFundsAccountStatus
  const errorMessage =
    errorService.errorMessage(error as Error) ||
    'We’re unable to load mutual funds details at this time. Please try again'

  useEffect(() => {
    refetchAccountStatus()
  }, [refetchAccountStatus])

  useLayoutEffect(() => {
    if (userAccountStatus) {
      if (
        userAccountStatus === MutualFundsAccountStatus.NEW ||
        userAccountStatus === MutualFundsAccountStatus.LANDING_SCREEN
      ) {
        navigation.replace(routeConst.MUTUAL_FUNDS_LANDING_V2)
      } else if (userAccountStatus === MutualFundsAccountStatus.ACCEPTED) {
        navigation.replace(routeConst.MUTUAL_FUNDS_INVESTMENT_PROFILE)
      } else {
        navigation.replace(routeConst.MUTUAL_FUNDS_HOME_SCREEN)
      }
    }
  }, [navigation, userAccountStatus])

  //   Show spinner when loading account status
  return isLoadingAccountStatus ? (
    <Container white>
      <Stack
        gutterVertical="s24"
        alignItems="center"
        justifyContent="center"
        fullWidth
        style={{ flex: 1 }}>
        <Spinner size="large" />
      </Stack>
    </Container>
  ) : isError ? (
    <ErrorContactSupportState
      title={'Woops!'}
      supportMessage={
        "Hi, I'm receiving an error when trying to view the mutual funds tab"
      }
      subtitle={errorMessage}
      retry={() => refetchAccountStatus()}
      supportAnalyticEvent={
        analytics.events.MUTUAL_FUNDS_ACCOUNT_SUPPORT_TAPPED
      }
      retryAnalyticEvent={analytics.events.MUTUAL_FUNDS_ACCOUNT_RETRY_TAPPED}
    />
  ) : null
}
