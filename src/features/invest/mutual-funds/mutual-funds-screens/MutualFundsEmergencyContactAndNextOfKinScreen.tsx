import React, { useCallback, useEffect, useState } from 'react'
import { ScrollView } from 'react-native'

import {
  Box,
  Button,
  Container,
  FixedFooter,
  ListItem,
  Text,
  defaultAnalyticEvents,
  normalize
} from '@chippercash/chipper-ui'

import { DynamicFields, ProgressBar } from '@shared/features/dynamic-form'
import { useDynamicForm } from '@shared/features/dynamic-form'

import { routeConst } from '@navigation'

import {
  EmergencyContactData,
  MutualFundsEmergencyContactScreenProps,
  NextOfKinData
} from '../mutual-funds-utils/mutualFundsTypes'

export const MutualFundsEmergencyContactAndNextOfKinScreen = ({
  route,
  navigation
}: MutualFundsEmergencyContactScreenProps) => {
  const { fields } = route.params
  const [isNextOfKinSame, setIsNextOfKinSame] = useState(false)

  const emergencyContactForm = useDynamicForm<EmergencyContactData>({
    formKey: 'emergency-contact-form'
  })

  const nextOfKinForm = useDynamicForm<NextOfKinData>({
    formKey: 'next-of-kin-form'
  })

  const handleSubmit = useCallback(() => {
    emergencyContactForm.handleSubmit((emergencyData: EmergencyContactData) => {
      if (isNextOfKinSame) {
        const nextOfKinData: NextOfKinData = {
          firstName: emergencyData.firstName,
          surName: emergencyData.surName,
          dateOfBirth: nextOfKinForm.getValue('dateOfBirth')
        }

        navigation.navigate(routeConst.MUTUAL_FUNDS_INVESTMENT_PROFILE, {
          emergencyContact: emergencyData,
          nextOfKin: nextOfKinData,
          isNextOfKinSame
        })
      } else {
        nextOfKinForm.handleSubmit((nextOfKinData: NextOfKinData) => {
          navigation.navigate(routeConst.MUTUAL_FUNDS_INVESTMENT_PROFILE, {
            emergencyContact: emergencyData,
            nextOfKin: nextOfKinData,
            isNextOfKinSame
          })
        })
      }
    })
  }, [emergencyContactForm, nextOfKinForm, isNextOfKinSame, navigation])

  useEffect(() => {
    if (isNextOfKinSame && emergencyContactForm.formState) {
      // Get values from form state
      const firstName = emergencyContactForm.getValue('firstName') as string
      const surName = emergencyContactForm.getValue('surName') as string

      nextOfKinForm.setValue('firstName', firstName)
      nextOfKinForm.setValue('surName', surName)
    }
  }, [
    isNextOfKinSame,
    emergencyContactForm.formState,
    nextOfKinForm,
    emergencyContactForm
  ])

  return (
    <Container white>
      <ProgressBar numSteps={3} currentStepNumber={1} />
      <ScrollView>
        <Box gutterHorizontal="s24" gutterTop="s16">
          <Text style={{ marginVertical: normalize(12) }} type="h3">
            Emergency Contact
          </Text>
          <DynamicFields
            fields={fields.emergencyContact}
            formKey={emergencyContactForm.key}
          />
        </Box>

        <ListItem
          title="Same as Next of Kin?"
          titleColor="textPrimary"
          titleProps={{ type: 'listMedium' }}
          onPress={() => setIsNextOfKinSame(!isNextOfKinSame)}
          analyticEventName={defaultAnalyticEvents.NONE}
          leadingElementType="icon"
          leadingElementSpacing="s8"
          leadingElementProps={{
            name: 'user',
            withContainer: true
          }}
          trailingElementType="switch"
          trailingElementProps={{
            checked: isNextOfKinSame
          }}
          hasDivider={false}
        />

        <Box gutterHorizontal="s24" gutterVertical="s16" gutterBottom={'s40'}>
          <Text
            style={{ marginVertical: normalize(12) }}
            type="h3"
            spacing="s16">
            Next of Kin
          </Text>
          <DynamicFields
            fields={fields.nextOfKin}
            formKey={nextOfKinForm.key}
          />
        </Box>
      </ScrollView>

      <FixedFooter>
        <Button
          title="Save"
          onPress={handleSubmit}
          disabled={
            !emergencyContactForm.isValid ||
            (!isNextOfKinSame && !nextOfKinForm.isValid)
          }
          analyticEventName={defaultAnalyticEvents.NONE}
        />
      </FixedFooter>
    </Container>
  )
}
