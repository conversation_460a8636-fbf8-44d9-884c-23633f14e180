import React, { useEffect, useState } from 'react'

import {
  AmountInput,
  Box,
  Button,
  Container,
  FixedFooter,
  KeyboardAvoid,
  Text
} from '@chippercash/chipper-ui'
import { useDispatch, useSelector } from 'react-redux'

import { analytics, coreService } from '@shared/services'
import { getCountryFromCurrency } from '@shared/services/countries/countries'
import { mutualFundSlice } from '@shared/store'
import { TransactionType } from '@shared/types'

import { routeConst } from '@navigation'

import { MutualFundsAmountInputScreenProps } from '../mutual-funds-utils'

export const MutualFundsAmountInputScreen = ({
  route,
  navigation
}: MutualFundsAmountInputScreenProps) => {
  const { transactionType } = route.params
  const [customAmount, setCustomAmount] = useState('')

  const dispatch = useDispatch()

  useEffect(() => {
    dispatch(mutualFundSlice.actions.setTransactionType(transactionType))
  })

  const investmentId = useSelector(
    mutualFundSlice.selectors.selectedMutualFundInvestmentId
  )
  const { data: balanceData, isLoading } = coreService.useBalanceQuery()

  const currency = balanceData?.currency || ''

  const handleNextTapped = () => {
    const { setInvestmentId, setAmount } = mutualFundSlice.actions
    dispatch(setInvestmentId(investmentId))
    dispatch(setAmount(Number(customAmount)))

    navigation.navigate(routeConst.MUTUAL_FUNDS_PAYMENT_METHOD_SELECTION_SCREEN)
  }

  const onChangeText = (amount: string) => {
    if (amount === '') {
      setCustomAmount('')
      return
    }
    setCustomAmount(amount)
  }

  const title =
    transactionType === TransactionType.DEPOSIT
      ? 'Top up your account'
      : 'Withdraw from your account'

  return (
    <Container>
      <KeyboardAvoid.Main mode={'sticky-bottom'} isSafeAreaPresent>
        <KeyboardAvoid.Top>
          <Box margin={'s24'}>
            <Box marginBottom={'s32'}>
              <Text type="h4SemiBold">{title}</Text>
              {transactionType === TransactionType.DEPOSIT && (
                <Text color="textSecondary" type="listSubTitle">
                  {
                    'To begin investing in Mutual Funds you must first top up to your account'
                  }
                </Text>
              )}
            </Box>

            <AmountInput
              currency={currency}
              label={'Amount'}
              country={getCountryFromCurrency(currency)}
              value={customAmount}
              keyboardType="numeric"
              placeholder={'0.00'}
              autoFocus={true}
              onChange={onChangeText}
              isLoading={isLoading}
            />
          </Box>
        </KeyboardAvoid.Top>
        <KeyboardAvoid.Bottom>
          <FixedFooter>
            <Button
              onPress={() => handleNextTapped()}
              title="Next"
              analyticEventName={
                analytics.events.MUTUAL_FUNDS_ACCOUNT_TOP_UP_TAPPED
              }
            />
          </FixedFooter>
        </KeyboardAvoid.Bottom>
      </KeyboardAvoid.Main>
    </Container>
  )
}
