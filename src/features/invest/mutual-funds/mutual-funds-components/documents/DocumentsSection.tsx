import React from 'react'

import { Box, ListItem, defaultAnalyticEvents } from '@chippercash/chipper-ui'

import { GetFundDocumentsApiResponse } from '@shared/services/api/core'

import { routeConst } from '@navigation'
import NavigationService from '@navigation/navigationService'

import { EmptyState } from './EmptyState'

type DocumentSectionProps = {
  fundDocumentsDetails: GetFundDocumentsApiResponse | undefined
}

export const DocumentsSection: React.FC<DocumentSectionProps> = ({
  fundDocumentsDetails
}) => {
  const handleItemPressed = (url: string) => {
    NavigationService.navigate(routeConst.WEB_VIEW_SCREEN, {
      url: url,
      title: 'Mutual Funds'
    })
  }

  return (
    <Box marginBottom="s40">
      {fundDocumentsDetails && fundDocumentsDetails.length > 0 ? (
        fundDocumentsDetails.map(section => (
          <Box
            key={`${section.id}-${section.title}`}
            backgroundColor="bgBase"
            borderRadius="medium"
            borderWidth="thin"
            borderColor="divider"
            marginHorizontal="s24"
            marginVertical="s8">
            <ListItem
              title={section.title}
              titleColor="textSecondary"
              analyticEventName={defaultAnalyticEvents.NONE}
              trailingElementType="none"
              hasDivider={false}
            />
            {section.item.map((item, index) => {
              const isLastItem = index < section.item.length - 1
              return (
                <ListItem
                  key={`${section.id}-${item.name}-${index}`}
                  title={item.name}
                  onPress={() => handleItemPressed(item.url)}
                  leadingElementType="none"
                  analyticEventName={defaultAnalyticEvents.NONE}
                  trailingElementType="icon"
                  trailingElementProps={{
                    name: 'file',
                    color: 'textSecondary'
                  }}
                  hasDivider={isLastItem}
                />
              )
            })}
            {section.item?.length === 0 && (
              <EmptyState message="No documents found" />
            )}
          </Box>
        ))
      ) : (
        <EmptyState message="No documents found" />
      )}
    </Box>
  )
}
