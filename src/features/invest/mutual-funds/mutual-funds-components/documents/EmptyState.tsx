import React from 'react'

import { Box, Image, Text, normalize } from '@chippercash/chipper-ui'

export const EmptyState = ({
  message = 'Nothing found',
  image = require('@images/dollar-illustration.png'),
  imageSize = { width: normalize(125), height: normalize(98) }
}) => (
  <Box
    backgroundColor="white"
    borderRadius={'medium'}
    borderWidth={'thin'}
    borderColor={'divider'}
    gutter="s16"
    marginVertical="s12"
    marginHorizontal="s24">
    <Box justifyContent="center" alignItems="center">
      <Image
        accessibilityIgnoresInvertColors
        source={image}
        style={{
          ...imageSize,
          marginBottom: normalize(16)
        }}
      />
      <Text type="bodySmall" color="textSecondary">
        {message}
      </Text>
    </Box>
  </Box>
)
