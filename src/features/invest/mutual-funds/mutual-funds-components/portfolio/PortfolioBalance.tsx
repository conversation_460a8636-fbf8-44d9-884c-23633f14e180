import React from 'react'
import { TouchableOpacity } from 'react-native'

import {
  Alert,
  Box,
  IconButton,
  Text,
  normalize
} from '@chippercash/chipper-ui'
import { formatRawAmountWithCurrencyPrefix } from '@chippercash/chipper-ui'

import { analytics } from '@shared/services'
import { GetAccountOverviewApiResponse } from '@shared/services/api/core'

type PortfolioBalanceProps = {
  overviewDetails: GetAccountOverviewApiResponse | undefined
}

export const PortfolioBalance: React.FC<PortfolioBalanceProps> = ({
  overviewDetails
}) => {
  const currency = overviewDetails?.activeInvestments[0]?.currency

  const formattedTotalBalance = formatRawAmountWithCurrencyPrefix(
    overviewDetails?.totalBalance || 0,
    currency || ''
  )
  const handleInfoPressed = () => {
    return Alert.alert({
      title: 'Mutual Funds balance',
      message:
        "The displayed amount reflects your portfolio's total and updates with each transaction.",
      options: {
        cancelable: false
      },
      buttons: [
        {
          text: 'OKAY'
        }
      ]
    })
  }

  return (
    <Box
      backgroundColor="white"
      borderRadius={'medium'}
      borderWidth={'thin'}
      borderColor={'divider'}
      gutter="s16"
      margin="s24">
      <Box
        alignItems="flex-start"
        flexDirection="row"
        justifyContent="space-between">
        <Text type="bodySmall" color="textSecondary">
          Total Mutual funds portfolio balance
        </Text>

        <TouchableOpacity onPress={() => handleInfoPressed()}>
          <IconButton
            name={'info'}
            color="systemBlue"
            onPress={() => handleInfoPressed()}
            analyticEventName={
              analytics.events.MUTUAL_FUNDS_ACCOUNT_BALANCE_INFO_TAPPED
            }
          />
        </TouchableOpacity>
      </Box>

      <Text type="h2" color="textPrimary" style={{ marginTop: normalize(12) }}>
        {formattedTotalBalance}
      </Text>
    </Box>
  )
}
