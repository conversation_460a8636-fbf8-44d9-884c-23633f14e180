import React from 'react'

import { Box } from '@chippercash/chipper-ui'
import { SegmentedControls } from '@chippercash/chipper-ui'

type Segment = {
  label: string
  value: string
}

type PortfolioSegmentsProps = {
  activeSegment: string
  onSegmentChange: (segment: string) => void
  segments: Segment[]
}

export const PortfolioSegments: React.FC<PortfolioSegmentsProps> = ({
  activeSegment,
  onSegmentChange,
  segments
}) => {
  return (
    <Box gutter="s4">
      <SegmentedControls
        values={segments.map(segment => segment.label)}
        currentIndex={segments.findIndex(
          segment => segment.value === activeSegment
        )}
        onValueChange={index => onSegmentChange(segments[index].value)}
      />
    </Box>
  )
}
