import React from 'react'

import {
  Box,
  Icon,
  ListItem,
  Text,
  defaultAnalyticEvents,
  normalize
} from '@chippercash/chipper-ui'
import { useDispatch } from 'react-redux'

import { format } from '@shared/services'
import { GetAccountOverviewApiResponse } from '@shared/services/api/core'
import { mutualFundSlice } from '@shared/store'

import { NavigationService, routeConst } from '@navigation'

type PortfolioOverviewProps = {
  overviewDetails: GetAccountOverviewApiResponse | undefined
}

export const PortfolioOverview: React.FC<PortfolioOverviewProps> = (
  {
    overviewDetails
  }
) => {
  const dispatch = useDispatch()
  const investments = Array.isArray(overviewDetails?.activeInvestments)
    ? overviewDetails?.activeInvestments
    : []
  const products = overviewDetails?.availableInvestments

  const handleMutualFundProductTapped = (
    documentation_url: string,
    name: string,
    investmentId: string,
    symbol: string
  ) => {
    const { setDocumentUrl, setSymbol, setInvestmentId, setFundName } =
      mutualFundSlice.actions
    dispatch(setDocumentUrl(documentation_url))
    dispatch(setSymbol(symbol))
    dispatch(setFundName(name))
    dispatch(setInvestmentId(investmentId))
    NavigationService.navigate(
      routeConst.MUTUAL_FUND_DETAIL_SCREEN, {hasInvestedInMutualFund: false}
    )
  }

  const handleUserInvestmentProductTapped = (id: string, name: string) => {
    const { setInvestmentId, setFundName } =
      mutualFundSlice.actions
    dispatch(setInvestmentId(id))
    dispatch(setFundName(name))
    NavigationService.navigate(routeConst.MUTUAL_FUND_DETAIL_SCREEN,  {hasInvestedInMutualFund: true})
  }

  return (
    <>
      <Box
        backgroundColor="white"
        borderRadius={'medium'}
        borderWidth={'thin'}
        borderColor={'divider'}
        gutter="s16"
        marginVertical="s12"
        marginHorizontal="s24">
        <Text
          type="listMedium"
          color="textSecondary"
          style={{ marginBottom: normalize(12) }}>
          Portfolio
        </Text>
        {investments?.map((investment, idx) => {
          const {
            price,
            currency,
            symbol,
            profitLossRatio,
            name,
            positionStatus,
            investmentId
          } = investment || {}
          const profitLossPercentage = ` ${Number(profitLossRatio).toFixed(2)}%`
          const isLastItem = idx === investments?.length - 1
          const iconName =
            positionStatus === 'profit'
              ? 'triangle-up-small'
              : 'triangle-down-small'
          const iconColor =
            positionStatus === 'profit' ? 'textGreen' : 'textRed'
          return (
            <ListItem
              key={symbol || idx}
              title={name}
              leadingElementType="logo"
              leadingElementProps={{
                url: investment?.iconUrl
              }}
              trailingElementType="doubleValueIcon"
              trailingElementProps={{
                icon: {
                  name: 'chevron-right'
                },
                firstValue: {
                  type: 'listMedium',
                  children: format.fiat.getFormattedValue(price, currency, {
                    useCurrencyAsSymbol: true
                  })
                },
                secondValue: {
                  type: 'listSubTitle',
                  children: (
                    <Box
                      alignItems="flex-start"
                      justifyContent="space-between"
                      flexDirection="row"
                      gap={'s4'}>
                      <Icon name={iconName} color={iconColor} size="small" />
                      <Text type={'listMedium'} color={iconColor}>
                        {profitLossPercentage}
                      </Text>
                    </Box>
                  ),
                  color: 'systemGreen'
                }
              }}
              onPress={() => handleUserInvestmentProductTapped(investmentId, name)}
              analyticEventName={defaultAnalyticEvents.NONE}
              hasDivider={!isLastItem}
            />
          )
        })}
        {investments?.length === 0 && (
          <Text type="bodySmall" color="textSecondary">
            No investments found
          </Text>
        )}
      </Box>

      <Box
        backgroundColor="white"
        borderRadius={'medium'}
        borderWidth={'thin'}
        borderColor={'divider'}
        gutter="s16"
        marginTop={'s2'}
        marginHorizontal="s24">
        <Text
          type="listMedium"
          color="textSecondary"
          style={{ marginBottom: normalize(16) }}>
          Explore
        </Text>
        {(products ?? []).map((product, index) => {
          const { documentation_url, name, investmentId, symbol, iconUrl } = product
          const isLastItem = index === (products?.length ?? 0) - 1
          return (
            <ListItem
              leadingElementType="logo"
              leadingElementProps={{ url: iconUrl }}
              key={name}
              title={symbol}
              hasDivider={!isLastItem}
              analyticEventName={defaultAnalyticEvents.NONE}
              onPress={() =>
                handleMutualFundProductTapped(documentation_url, name, investmentId, symbol)
              }
            />
          )
        })}
        {(products?.length ?? 0) === 0 && (
          <Text type="bodySmall" color="textSecondary">
            No available products found
          </Text>
        )}
      </Box>
    </>
  )
}
