import React from 'react'
import { ScrollView } from 'react-native'

import {
  TransactionItem,
  TransactionSectionList,
  TransactionStatus
} from '@shared/components/activity-list-v2'
import {
  GetTransactionHistoryApiResponse,
  useGetTransactionHistoryQuery
} from '@shared/services/api/core'
import { TransactionType } from '@shared/types'

import { EmptyState } from '../documents/EmptyState'

export const TransactionsSection: React.FC = () => {
  const [day, month, year] = new Intl.DateTimeFormat('en-GB', {
    day: '2-digit',
    month: '2-digit',
    year: 'numeric'
  }).format(new Date()).split('/')

  const todaysDate = `${day}-${month}-${year}`

  const {
    data: transactions,
    isLoading,
    refetch,
    isFetching,
    isError
  } = useGetTransactionHistoryQuery({
    endDate: todaysDate,
    page: 0,
    size: 3
  })

  if (!transactions?.content  || isError) {
    return (
      <ScrollView>
        <EmptyState message="You currently have no transactions" />
      </ScrollView>
    )
  }

  function mapToTransactionItems(
    response: GetTransactionHistoryApiResponse
  ): TransactionItem[] {
    const titleMap: Record<string, string> = {
      WITHDRAWAL: 'You withdrew',
      TOP_UP: 'You topped up'
    }

    const statusMap: Record<string, string> = {
      EXECUTED: TransactionStatus.Pending,
      ORDER_PENDING: TransactionStatus.Pending,
      COMPLETED: TransactionStatus.Successful,
      FAILED: TransactionStatus.Failed
    }


    return response.content.map(txn => ({
      id: txn.reference,
      title: titleMap[txn.transactionType] ?? txn.transactionType?.replace(/_/g, ' '),
      subtitle: txn.amount ?? undefined,
      amount: txn.amount,
      currency: txn.currency.code,
      status: statusMap[txn.status] ?? txn.status?.replace(/_/g, ' '),
      debit: txn.transactionType === TransactionType.WITHDRAWAL,
      timestamp: txn.orderDate,
      strikethrough: txn.status === 'FAILED',
      type: txn.transactionType
    }))
  }

  const items: TransactionItem[] = mapToTransactionItems(transactions)

  return (
    <TransactionSectionList
      items={items}
      onRefresh={refetch}
      isLoadingFirst={isLoading}
      isRefreshing={isLoading}
      isFetching={isFetching}
    />
  )
}
