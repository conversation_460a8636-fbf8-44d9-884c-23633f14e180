import React, { useMemo } from 'react'

import { Box, ListItem, Text, normalize } from '@chippercash/chipper-ui'

import { format } from '@shared/services'
import {
  GetInvestmentProductByIdApiResponse,
  GetUserInvestmentApiResponse
} from '@shared/services/api/core'

import { AboutSection } from './AboutSection'
import { MutualFundGraph } from './ProductChart'

type ProductOverviewProps = {
  product: GetInvestmentProductByIdApiResponse | undefined
  userInvestment: GetUserInvestmentApiResponse | undefined
  hasInvestedInMutualFund: boolean
}

export const ProductOverview: React.FC<ProductOverviewProps> = ({
  product,
  userInvestment,
  hasInvestedInMutualFund
}) => {
  const portfolio = useMemo(() => {
    if (hasInvestedInMutualFund) {
      return userInvestment?.portfolio
    }
    return product?.portfolio
  }, [hasInvestedInMutualFund, product?.portfolio, userInvestment?.portfolio])

  const { fundInfo, investment } = portfolio || {}
  const { totalCashAdded, totalWithdrawn, totalGain, percentGain } =
    userInvestment?.portfolio.userPosition || {}

  const { currency, description, name } = fundInfo || {}
  const totalGainAmount = totalGain
    ? format.fiat.getFormattedValue(totalGain, currency, {
        useCurrencyAsSymbol: true
      })
    : ''
  const percentageGain = ` ${Number(percentGain).toFixed(2)}%`
  const totalGainAmountAndPercentage = `${totalGainAmount} (${percentageGain})`
  const formattedTotalCashAdded = format.fiat.getFormattedValue(
    totalCashAdded,
    currency,
    {
      useCurrencyAsSymbol: true
    }
  )
  const formattedTotalWithdrawn = format.fiat.getFormattedValue(
    totalWithdrawn,
    currency,
    {
      useCurrencyAsSymbol: true
    }
  )

  const overViewTitle = hasInvestedInMutualFund ? 'Your position' : 'About'

  return (
    <>
      {/* Graph section */}
      {portfolio && <MutualFundGraph />}

      {/* Position section */}

      <Box
        backgroundColor="white"
        borderRadius={'medium'}
        borderWidth={'thin'}
        borderColor={'divider'}
        marginTop={normalize(-25)}
        marginBottom={'s40'}
        marginHorizontal="s24">
        <ListItem
          size="short"
          trailingElementType="none"
          title={''}
          customSubTitle={
            <Text type="listMedium" color="textSecondary">
              {overViewTitle}
            </Text>
          }
          hasDivider={false}
        />

        {hasInvestedInMutualFund ? (
          <Box marginVertical={'s12'}>
            <ListItem
              size="short"
              trailingElementType="value"
              trailingElementProps={{
                children: formattedTotalCashAdded,
                type: 'h4SemiBold'
              }}
              title={'Total cash added'}
            />
            <ListItem
              size="short"
              trailingElementType="value"
              trailingElementProps={{
                children: formattedTotalWithdrawn,
                type: 'h4SemiBold'
              }}
              title={'Total withdrawal'}
            />
            <ListItem
              size="short"
              title={'Total Gain'}
              hasDivider={false}
              trailingElementType="value"
              trailingElementProps={{
                children: totalGainAmountAndPercentage,
                color: 'textGreen',
                type: 'h4SemiBold'
              }}
            />
          </Box>
        ) : (
          <Box marginHorizontal={'s24'}>
            <AboutSection
              description={description}
              fundName={name}
              url={investment?.documentation_url}
            />
          </Box>
        )}
      </Box>
    </>
  )
}
