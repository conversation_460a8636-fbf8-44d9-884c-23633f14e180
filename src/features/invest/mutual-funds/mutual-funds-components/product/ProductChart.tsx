import React, { useEffect, useMemo, useState } from 'react'
import { ActivityIndicator, TouchableOpacity } from 'react-native'

import {
  AnimatedBox,
  Box,
  Text,
  triggerLightHaptic,
  useChipperUITheme
} from '@chippercash/chipper-ui'
import { NavigationProp } from '@react-navigation/core'
import { useNavigation } from '@react-navigation/native'
import moment from 'moment'
import { GestureHandlerRootView } from 'react-native-gesture-handler'
import { LineGraph } from 'react-native-graph'
import { FadeIn, FadeOut } from 'react-native-reanimated'
import { useSelector } from 'react-redux'
import styled from 'styled-components/native'

import { coreService } from '@shared/services'
import { mutualFundSlice } from '@shared/store'

import { MutualFundsParamList } from '../../mutual-funds-utils'

const xLabels = [
  { label: '1H' },
  { label: '1D' },
  { label: '1W' },
  { label: '1M' },
  { label: '1Y' },
  { label: 'ALL' }
]

export const MutualFundGraph = () => {
  const productId = useSelector(
    mutualFundSlice.selectors.selectedMutualFundInvestmentId
  )
  const theme = useChipperUITheme()
  const [selectedPoint, setSelectedPoint] = useState<{
    date: Date
    value: number
  } | null>(null)
  const [selectedRange, setSelectedRange] = useState<string>('ALL')

  const {
    refetch: refetchHistoricalPrices,
    data: historicalPricesData,
    isLoading: isLoadingHistoricalPrices
  } = coreService.chipperMutualFundsApi.endpoints.getHistoricalPrices.useQuery({
    window: selectedRange,
    asset: productId
  })

  const prices = useMemo(() => {
    return Array.isArray(historicalPricesData?.prices)
      ? historicalPricesData?.prices
      : []
  }, [historicalPricesData?.prices])

  const navigation = useNavigation<NavigationProp<MutualFundsParamList>>()

  useEffect(() => {
    return navigation.addListener('focus', async () => {
      refetchHistoricalPrices()
    })
  }, [navigation, refetchHistoricalPrices])

  const parseDate = (dateStr: string): Date => {
    const [d, m, y] = dateStr.split('-')
    return new Date(`${y}-${m}-${d}`)
  }

  const rawChartData = useMemo(() => {
    const parsed =
      prices?.map(item => ({
        date: item.date,
        value: parseFloat(String(item.price))
      })) ?? []
    return parsed
      .filter(p => !isNaN(p.value))
      .map(p => ({ ...p, date: parseDate(p.date) }))
      .sort((a, b) => a.date.getTime() - b.date.getTime())
  }, [prices])

  const filteredData = useMemo(() => {
    const now = new Date()
    const cutoff = new Date(now)

    const rangeMap = {
      '1H': () => cutoff.setHours(now.getHours() - 1),
      '1D': () => cutoff.setDate(now.getDate() - 1),
      '1W': () => cutoff.setDate(now.getDate() - 7),
      '1M': () => cutoff.setMonth(now.getMonth() - 1),
      '1Y': () => cutoff.setFullYear(now.getFullYear() - 1)
    } as const

    if (!(selectedRange in rangeMap)) {
      return rawChartData
    }

    rangeMap[selectedRange as keyof typeof rangeMap]()

    const filtered = rawChartData.filter(item => item.date >= cutoff)
    return filtered.length >= 2 ? filtered : rawChartData.slice(0, 2)
  }, [selectedRange, rawChartData])

  const lastPoint = filteredData[filteredData.length - 1]

  const graphPoints = useMemo(() => {
    return filteredData.map(item => ({
      timestamp: item.date.getTime(),
      value: item.value
    }))
  }, [filteredData])

  // This is to ensure we show a straight line chart even with all same values
  const yRange = useMemo(() => {
    const values = graphPoints.map(p => p.value)
    const allSame = values.every(v => v === values[0])
    const min = allSame ? values[0] - 0.01 : Math.min(...values)
    const max = allSame ? values[0] + 0.01 : Math.max(...values)
    return { min, max }
  }, [graphPoints])

  return (
    <Box
      backgroundColor="white"
      borderRadius={'medium'}
      borderWidth={'thin'}
      borderColor={'divider'}
      marginTop={'s12'}
      marginBottom={'s40'}
      gutterVertical="s16"
      marginHorizontal="s24">
      <Box
        flexDirection="row"
        justifyContent="space-between"
        alignItems="flex-end"
        gutterHorizontal="s16">
        <Box flex={1}>
          <Text spacing="s4" color="textSecondary" type="bodySmall">
            Price
          </Text>
          <Text type="h4SemiBold">
            ¢{(selectedPoint?.value ?? lastPoint?.value ?? 0).toFixed(2)}
          </Text>
        </Box>

        <Box alignItems="flex-end" gutterVertical="s4" flex={1}>
          <Text spacing="s4" color="textSecondary" type="bodySmall">
            {selectedPoint?.date
              ? moment(selectedPoint.date).format('DD MMM YYYY')
              : moment(lastPoint?.date).format('DD MMM YYYY')}
          </Text>
        </Box>
      </Box>

      <GestureHandlerRootView style={{ height: 180, width: '100%' }}>
        {filteredData.length < 2 && !isLoadingHistoricalPrices ? (
          <Box justifyContent="center" alignItems="center" height={180}>
            <Text type="bodySmall">No chart data available</Text>
          </Box>
        ) : (
          <LineGraph
            style={{ flex: 1 }}
            animated
            enablePanGesture
            points={filteredData}
            color={theme.colors.brandPrimary100}
            gradientFillColors={[
              `${theme.colors.brandPrimary100}88`,
              `${theme.colors.brandPrimary100}00`
            ]}
            onGestureStart={triggerLightHaptic}
            onPointSelected={point => {
              setSelectedPoint(point)
            }}
            onGestureEnd={() => setSelectedPoint(null)}
            enableIndicator={true}
            indicatorPulsating={true}
            range={{ y: { min: yRange.min, max: yRange.max } }}
          />
        )}
      </GestureHandlerRootView>

      <Box
        width="100%"
        gutterHorizontal="s16"
        flexDirection="row"
        justifyContent="space-between">
        {xLabels.map(({ label }) => {
          const isActive = selectedRange === label
          return (
            <LabelButton
              key={label}
              onPress={() => {
                triggerLightHaptic()
                setSelectedRange(label)
              }}
              isActive={isActive}>
              <Text
                color={isActive ? 'textPrimary' : 'textSecondary'}
                type="hint"
                spacing="s0"
                style={{ fontSize: 10 }}>
                {label}
              </Text>
            </LabelButton>
          )
        })}
      </Box>

      {isLoadingHistoricalPrices ? (
        <AnimatedBox
          exiting={FadeOut}
          entering={FadeIn}
          borderRadius={'medium'}
          position={'absolute'}
          width={'100%'}
          height={'100%'}
          style={{
            backgroundColor: 'rgba(0,0,0,0.1)'
          }}
          alignItems={'center'}
          justifyContent={'center'}>
          <ActivityIndicator />
        </AnimatedBox>
      ) : null}
    </Box>
  )
}

const LabelButton = styled(TouchableOpacity)<{ isActive?: boolean }>`
  width: 38px;
  align-items: center;
  justify-content: center;
  padding: ${({ theme }) => theme.spacing.s4 ?? '8px'};
  border-radius: ${({ theme }) => theme.roundedCorners?.rc8 ?? '8px'};
  background-color: ${({ theme, isActive }) =>
    isActive
      ? `${theme.colors?.brandPrimary100 ?? '#800080'}33`
      : 'transparent'};
`
