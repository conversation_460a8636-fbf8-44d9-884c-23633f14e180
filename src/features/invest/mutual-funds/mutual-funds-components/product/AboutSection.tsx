import React from 'react'

import { Box, Text, normalize } from '@chippercash/chipper-ui'

import { NavigationService, routeConst } from '@navigation'

type AboutSectionProps = {
  description?: string
  fundName?: string
  url?: string
}

export function AboutSection({
  description,
  fundName,
  url
}: AboutSectionProps) {
  const handleReadMorePressed = () => {
    NavigationService.navigate(routeConst.WEB_VIEW_SCREEN, {
      url,
      title: fundName
    })
  }
  return (
    <Box marginVertical={'s2'}>
      <Text
        style={{ marginTop: normalize(10) }}
        type="bodySmall"
        color="textPrimary">
        {description}
      </Text>
      <Text
        color="brandPrimary100"
        weightOverride="primarySemiBold"
        style={{ marginVertical: normalize(-6) }}
        onPress={() => handleReadMorePressed()}
        type="bodySmall">
        {'Read More'}
      </Text>
    </Box>
  )
}
