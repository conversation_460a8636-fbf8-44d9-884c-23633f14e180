import React, { useMemo } from 'react'

import { Box, ListItem, Text, normalize } from '@chippercash/chipper-ui'

import {
  GetInvestmentProductByIdApiResponse,
  GetUserInvestmentApiResponse
} from '@shared/services/api/core'

type ProductOverviewProps = {
  product: GetInvestmentProductByIdApiResponse | undefined
  userInvestment: GetUserInvestmentApiResponse | undefined
  hasInvestedInMutualFund: boolean
}

export const ProductStatistics: React.FC<ProductOverviewProps> = ({
  product,
  userInvestment,
  hasInvestedInMutualFund
}) => {
  const data = useMemo(() => {
    if (hasInvestedInMutualFund) {
      return userInvestment?.portfolio
    }
    return product?.portfolio
  }, [hasInvestedInMutualFund, product?.portfolio, userInvestment?.portfolio])

  const { statistics } = data || {}
  const { ytdReturn, dailyChange, sharePrice } = statistics || {}

  const ytdDescription =
    'This is the interest earned on your investment resulting from holding your investment from January 1 of the current calendar year up to the current reporting date.'
  const dailyChangeDescription =
    'It is the gain or loss on your investment balance resulting from holding your investment over the past 24 hours.'
  const sharePriceDescription =
    'This refers to the value of a single share of the fund today. It is the price at which you can buy or sell one share of the fund.'

  return (
    <>
      {/* Statistics section */}
      <Box
        backgroundColor="white"
        borderRadius={'medium'}
        borderWidth={'thin'}
        borderColor={'divider'}
        marginTop={'s4'}
        marginBottom={'s40'}
        marginHorizontal="s24">
        <ListItem
          size="short"
          trailingElementType="none"
          title={''}
          customSubTitle={
            <Text type="listMedium" color="textSecondary">
              Statistics
            </Text>
          }
          hasDivider={false}
        />

        <Box marginVertical={'s12'}>
          <ListItem
            size="short"
            trailingElementType="value"
            trailingElementProps={{
              children: ytdReturn?.formatted,
              type: 'h4SemiBold'
            }}
            title={'YTD Return'}
          />
          <ListItem
            size="short"
            trailingElementType="value"
            trailingElementProps={{
              children: dailyChange?.formatted,
              type: 'h4SemiBold'
            }}
            title={'Daily change'}
          />
          <ListItem
            size="short"
            title={'Share price'}
            hasDivider={false}
            trailingElementType="value"
            trailingElementProps={{
              children: sharePrice?.formatted,
              type: 'h4SemiBold'
            }}
          />
        </Box>
      </Box>

      {/* Statistics descriptions section */}

      <Box
        backgroundColor="white"
        borderRadius={'medium'}
        borderWidth={'thin'}
        borderColor={'divider'}
        marginTop={normalize(-25)}
        marginBottom={'s40'}
        marginHorizontal="s24">
        <Box marginVertical={'s12'}>
          <ListItem
            trailingElementType="none"
            title={'YTD (Year to Date)'}
            subTitle={ytdDescription}
          />
          <ListItem
            trailingElementType="none"
            title={'Daily change'}
            subTitle={dailyChangeDescription}
          />
          <ListItem
            trailingElementType="none"
            title={'Share price'}
            subTitle={sharePriceDescription}
            hasDivider={false}
          />
        </Box>
      </Box>
    </>
  )
}
