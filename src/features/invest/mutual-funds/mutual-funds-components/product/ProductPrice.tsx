import React, { useMemo } from 'react'

import {
  Box,
  Icon,
  ListItem,
  Text,
  ThemeColorName,
  defaultAnalyticEvents,
  normalize
} from '@chippercash/chipper-ui'
import { formatRawAmountWithCurrencyPrefix } from '@chippercash/chipper-ui'

import { format } from '@shared/services'
import {
  GetInvestmentProductByIdApiResponse,
  GetUserInvestmentApiResponse
} from '@shared/services/api/core'
import { smartTruncate } from '@shared/utils'

import { AboutSection } from './AboutSection'

type PortfolioBalanceProps = {
  product: GetInvestmentProductByIdApiResponse | undefined
  userInvestment: GetUserInvestmentApiResponse | undefined
  hasInvestedInMutualFund: boolean
}

export const ProductPrice: React.FC<PortfolioBalanceProps> = ({
  product,
  userInvestment,
  hasInvestedInMutualFund
}) => {
  const data = useMemo(() => {
    if (hasInvestedInMutualFund) {
      return userInvestment?.portfolio
    }
    return product?.portfolio
  }, [hasInvestedInMutualFund, product?.portfolio, userInvestment?.portfolio])

  const { fundInfo, investment } = data || {}
  const { currency } = fundInfo || {}
  const price = hasInvestedInMutualFund ? Number(fundInfo?.price) : 0
  const description = fundInfo?.description
    ? smartTruncate(fundInfo.description, 60)
    : ''

  const formattedTotalBalance = formatRawAmountWithCurrencyPrefix(
    price || 0,
    currency || ''
  )

  const iconName =
    fundInfo?.positionStatus === 'profit'
      ? 'triangle-up-small'
      : 'triangle-down-small'
  const iconColor: ThemeColorName =
    fundInfo?.positionStatus === 'profit' ? 'textGreen' : 'textRed'
  const profitLoss = format.fiat.getFormattedValue(
    hasInvestedInMutualFund ? fundInfo?.profitLoss : 0,
    fundInfo?.currency,
    {
      useCurrencyAsSymbol: true
    }
  )
  const profitLossRatio = hasInvestedInMutualFund ? Number(fundInfo?.profitLossRatio).toFixed(2) : 0
  const pricePercentage =
    `${profitLoss} (${profitLossRatio}%)`
  const fundName = fundInfo?.name || ''
  const documentation_url = investment?.documentation_url

  return (
    <Box
      backgroundColor="white"
      borderRadius={'medium'}
      borderWidth={'thin'}
      borderColor={'divider'}
      gutter="s8"
      marginBottom={'s12'}
      margin="s24">
      <ListItem
        title={fundInfo?.symbol || investment?.symbol || ''}
        leadingElementType={'logo'}
        leadingElementProps={{
          url: fundInfo?.iconUrl
        }}
        analyticEventName={defaultAnalyticEvents.NONE}
        trailingElementType={'none'}
        hasDivider={false}
      />

      <Box marginHorizontal={'s20'}>
        <Text type="h2" color="textPrimary" style={{ marginTop: normalize(8) }}>
          {formattedTotalBalance}
        </Text>
        <Box marginTop={normalize(-15)} flexDirection="row" gap={'s8'}>
          <Icon name={iconName} color={iconColor} size="small" />
          <Text type={'listMedium'} color={iconColor}>
            {pricePercentage}
          </Text>
        </Box>

        {hasInvestedInMutualFund && (
          <AboutSection
            description={description}
            fundName={fundName}
            url={documentation_url}
          />
        )}
      </Box>
    </Box>
  )
}
