import React from 'react'

import { createStackNavigator } from '@react-navigation/stack'

import { coreService } from '@shared/services'

import { routeConst } from '@navigation'
import { stackScreenConfig } from '@navigation/config'

import { CryptoNavigator } from '../crypto'
import { StocksNavigator } from '../stocks'
import { MutualFundsNavigator } from '../mutual-funds/mutual-funds-navigation/MutualFundsNavigator'
import { InvestmentHome } from '../invest-home/InvestHomeScreen'

const Stack = createStackNavigator()

export const InvestV2Navigator = () => {
  const { data: userAccountConfig } = coreService.useAccountConfigurationQuery()

  const { isCryptoTradingAvailable, isStocksTabAvailable, isMutualFundsProductAvailable } =
    userAccountConfig || {}

  return (
    <Stack.Navigator
      screenOptions={stackScreenConfig({ headerShadowVisible: false })}>
      <Stack.Screen
        name={routeConst.INVEST_HOME}
        component={InvestmentHome}
        options={stackScreenConfig({
          title: 'Invest',
          headerLeft: () => null,
          headerShadowVisible: false
        })}
      />

      {isMutualFundsProductAvailable && (
        <Stack.Screen
          name={routeConst.MUTUAL_FUNDS}
          component={MutualFundsNavigator}
          options={{
            headerShown: false
          }}
        />
       )}

      {isStocksTabAvailable && (
        <Stack.Screen
          name={routeConst.STOCKS}
          component={StocksNavigator}
          options={{
            headerShown: false
          }}
        />
       )}

      {isCryptoTradingAvailable && (
        <Stack.Screen
          name={routeConst.CRYPTO}
          component={CryptoNavigator}
          options={{
            headerShown: false
          }}
        />
      )}
    </Stack.Navigator>
  )
}
