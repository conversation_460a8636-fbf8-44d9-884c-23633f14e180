import React, {
  FunctionComponent,
  useCallback,
  useEffect,
  useState
} from 'react'

import {
  BottomSheet,
  Button,
  Container,
  Stack,
  Text
} from '@chippercash/chipper-ui'
import { StackScreenProps } from '@react-navigation/stack'
import moment from 'moment'
import { ScrollView } from 'react-native-gesture-handler'

import { PassThroughModal } from '@shared/components/deprecated'
import { FeeFields, Recurrence, useRecurrence } from '@shared/features'
import { useTransactionContext } from '@shared/features/transaction/transaction-context'
import {
  useFetchExchangeRates,
  useFetchPaymentMethodsConfig,
  useMountEffect,
  useShowExperimentFeature
} from '@shared/hooks'
import { CONST, analytics, coreService, errorService } from '@shared/services'
import { store, storeHelpers } from '@shared/store'
import { BalanceKind, PaymentMethodType, TransactionType } from '@shared/types'
import { FrequencyType } from '@shared/utils'

import {
  BillerItemWithAttributeValues,
  MarketplaceParamsList
} from '@features/marketplace/marketplace-utils'

import { routeConst } from '@navigation'

import { PaymentAmountInput, PaymentLookup } from '../bills-components'
import { types } from '../bills-utils'
import { BillMeterValidationFailed } from './BillMeterValidationFailed'
import { UmemeElectricityBiller } from '../bills-utils/billsTypes'

const MODE = {
  LOOKUP: 'LOOKUP',
  PAYMENT: 'PAYMENT',
  COMPLETE: 'COMPLETE',
  LOOKUPFAILED: 'LOOKUPFAILED'
} as const

const AddNewBillModal: FunctionComponent<{
  show: boolean
  onClose: () => void
  onAddBill: () => void
  billerName?: string
}> = ({ show, onClose, onAddBill, billerName }) => {
  return (
    <BottomSheet showCloseButton show={show} onClose={onClose}>
      <Stack gutterTop="s32" gutterHorizontal="s24">
        <Text weightOverride="primaryMedium">Currently unavailable</Text>
      </Stack>

      <Stack gutterVertical="s12" gutterHorizontal="s24">
        <Text>
          Sorry {billerName} is currently unavailable. Select “Add new bill” to
          explore other options.
        </Text>
      </Stack>

      <Stack gutterHorizontal="s24">
        <Button
          onPress={onAddBill}
          title="Add new bill"
          analyticEventName={analytics.events.BILLS_ADD_NEW_TAPPED_FROM_RECENT}
        />
      </Stack>
    </BottomSheet>
  )
}

/**
 * Allows users to pay bills.
 * Consists of 3 different modes:
 * 1. Look Up (user enters details to find the exact bill they want to pay for)
 * 2. Amount Input (user enters the amount they would like to pay for bill - if not fixed)
 * 3. Success (shown if bill payment succeeds)
 */
export const Payment = ({
  navigation,
  route
}: StackScreenProps<MarketplaceParamsList, 'BILLS_PAYMENT'>) => {
  const [showAddNewBillModal, setShowAddNewModal] = useState<boolean>(false)

  const billerItemParam = route.params.billerItem
  const performAutoLookup = route.params.performAutoLookup
  const billerIdParam = billerItemParam.billerId
  const countryParam = route.params.country
  const { meterNumber, phoneNumber, billerItemId, showSchedule, logo } =
    route.params

  const currency = route.params.currency || billerItemParam.currency

  const customerNameParam = route.params.customerName ?? ''
  const billBillerItem = String(billerItemParam.billerItemId || billerItemId)

  const [billerItem, setBillerItem] = useState<
    types.BillerItem | BillerItemWithAttributeValues
  >(billerItemParam)

  const { rates } = useFetchExchangeRates()
  const { paymentMethodCount } = useFetchPaymentMethodsConfig()

  const [billPayment, setBillPayment] = useState<types.BillPayment>({})

  const [mode, setMode] = useState<keyof typeof MODE>(MODE.LOOKUP)
  const [isProcessing, setIsProcessing] = useState(false)
  const [lookUpFailedMessage, setLookUpFailedMessage] = useState('')
  const [customerName, setCustomerName] = useState(customerNameParam)

  const [billDetails, setBillDetails] = useState<
    types.BillDetail | BillerItemWithAttributeValues
  >({})

  const [amount, setAmount] = useState<string>(
    String(billerItemParam?.amount) || ''
  )
  const [amountHint, setAmountHint] = useState<string>()
  const [feeFields, setFeeFields] = useState<FeeFields[]>([])
  const [recurrencePayment, setRecurrencePayment] = useState({} as Recurrence)
  const [showPassThrough, setShowPassThrough] = useState(false)

  // Same bet funding endpoints are used for look up and bill payment
  const [triggerLookupBillItem] = coreService.useValidateBetAccountMutation()

  const [calculateBillPaymentAmount, { isLoading, isError }] =
    coreService.useGetTemporaryQuoteMutation()

  const { data: balanceData } = coreService.useBalanceQuery()
  const userPrimaryCurrency = balanceData?.currency ?? ''
  const {
    initialize,
    spendableBalances,
    transactionSource,
    sourceBalance,
    momoPaymentMethod
  } = useTransactionContext()
  const [fetchBillerItems] =
    coreService.chipperBillersApi.endpoints.billerItems.useLazyQuery()

  useEffect(() => {
    initialize(TransactionType.BILL_PAYMENT, userPrimaryCurrency)
    if (meterNumber && customerName && phoneNumber) {
      setMode(MODE.PAYMENT)
    }
  }, [initialize, meterNumber, customerName, phoneNumber, userPrimaryCurrency])

  const isSameCurrency =
    transactionSource?.currency === currency ||
    momoPaymentMethod?.currency === currency

  const calculateBillPayAmount = useCallback(
    async (billAmount?: string, billCurrency?: string) => {
      try {
        if (amount !== '0' && amount !== '' && billCurrency) {
          const response = await calculateBillPaymentAmount({
            body: {
              amount: billAmount,
              country: countryParam,
              billerItem: billBillerItem,
              currency: momoPaymentMethod
                ? momoPaymentMethod.currency
                : transactionSource?.currency || userPrimaryCurrency,
              destinationCurrency: billCurrency,
              isDestinationTransfer: true, // isDestinationTransfer should always be true for all cases
              phoneNumber,
              serviceType: (billerItemParam as BillerItemWithAttributeValues)
                ?.billType
            }
          }).unwrap()

          if (response && response.fields) {
            const hintAmount = String(
              response.originAmount || response.destinationAmount
            )
            const fields = response.fields
            setAmountHint(hintAmount)
            setFeeFields(fields)
          }
        }
      } catch (err) {
        errorService.handleError(err, 'Failure in Fetching Bill Amount', {
          alertConfig: {
            buttons: [{ text: 'OK' }]
          }
        })
      }
    },
    [
      amount,
      billBillerItem,
      calculateBillPaymentAmount,
      countryParam,
      momoPaymentMethod,
      phoneNumber,
      billerItemParam,
      transactionSource?.currency,
      userPrimaryCurrency
    ]
  )

  useEffect(() => {
    if (!isSameCurrency && transactionSource && currency) {
      calculateBillPayAmount(amount, currency)
    }
  }, [
    amount,
    billerItem.isFixedAmount,
    calculateBillPayAmount,
    currency,
    isSameCurrency,
    transactionSource,
    userPrimaryCurrency
  ])

  const isBillPaymentBalanceSelectionEnabled = useShowExperimentFeature(
    CONST.EXPERIMENT_KEYS.BILL_PAYMENT_BALANCE_SELECTION
  )

  const isBillpaymentRecurringScheduleEnabled = useShowExperimentFeature(
    CONST.EXPERIMENT_KEYS.BILL_PAYMENT_RECURRING_BUY
  )

  const billPaymentRecurrence = useRecurrence(
    CONST.RECURRENCE_KEYS.BILL_PAYMENT,
    {
      options: {
        [FrequencyType.ONE_TIME]: {
          withDate: true,
          maxDate: moment().add(1, 'year').valueOf()
        }
      }
    }
  )

  const billBillerName = billerItem.name || billerItem.billerName
  const billBillerId = billerItem.billerId
  const waecOrWassceTransaction =
    billBillerName?.includes('WAEC') || billBillerName?.includes('WASSCE')
  // Transaction types other than these supports meter number input
  const requiresMeterNumber = !waecOrWassceTransaction

  // Transaction types other than showmax, smile and spectranet supports phone number input
  const showMaxSmileOrSpectranetTransction =
    billBillerId === '84' || billBillerId === '153' || billBillerId === '36'
  const requiresPhoneNumber = !showMaxSmileOrSpectranetTransction

  /** Handles adding the bill after the look up attributes have been entered */
  const handleAddBill = useCallback(
    async (customerRef?: string, mobile?: string) => {
      if (
        (requiresMeterNumber && !customerRef) ||
        (requiresPhoneNumber && !mobile)
      ) {
        return
      }
      setIsProcessing(true)
      const finalBillerItem: BillerItemWithAttributeValues = {
        customerRef,
        mobile,
        ...billerItem
      }
      setBillerItem(finalBillerItem)
      // WAEC and WASSCE transactions do not require look up. UMEME transactions requires amount during look up hence we await amount input before look up
      if (waecOrWassceTransaction || billBillerName === UmemeElectricityBiller.UMEME) {
        setMode(MODE.PAYMENT)
      }

      analytics.track(analytics.events.BILLS_LOOKUP_TAPPED, {
        'Biller Item': finalBillerItem.name,
        customerRef
      })

      try {
        // Perform the bill look up. Apparently, this creates a PENDING bill payment on the backend
        // so get that payment, and set it to current state to allow the user to process the payment
        // in the next step, when mode is PAYMENT
        if (waecOrWassceTransaction || billBillerName === UmemeElectricityBiller.UMEME) {
          setBillDetails({
            isComplete: false,
            mobile
          })
        } else if (!waecOrWassceTransaction ||  billBillerName !== UmemeElectricityBiller.UMEME) {
          const lookupResponse = await triggerLookupBillItem({
            body: {
              billerItem: billBillerItem,
              country: countryParam,
              customerRef: performAutoLookup
                ? (billerItemParam as BillerItemWithAttributeValues).meterNumber
                : customerRef,
              serviceType: (billerItemParam as BillerItemWithAttributeValues)
                ?.billType
            }
          }).unwrap()
          analytics.track(analytics.events.BILLS_LOOKUP_SUCCESS, {
            'Biller Item': finalBillerItem.name,
            'Bill Payment Id': lookupResponse.customerRef
          })
          // Trigger showing the payment form for user to input amount
          setMode(MODE.PAYMENT)
          // Update billDetails which shows a table of the bill details for the bill payment in question
          setBillDetails({
            isComplete: false,
            mobile,
            ...lookupResponse
          })
          // Set billPayment to be referenced when user pays bill
          setBillPayment(lookupResponse)
          setCustomerName(lookupResponse.customerName ?? '')
          // For fixed amount in GH we get this info after look up
          if (lookupResponse.amountDue && billerItem.country === 'GH') {
            setBillerItem({
              amountDue: lookupResponse.amountDue,
              ...finalBillerItem
            })
            setAmount(String(lookupResponse.amountDue))
          }
        }
      } catch (error) {
        const message = errorService.errorMessage(error as Error)
        analytics.track(analytics.events.BILLS_LOOKUP_FAILED, {
          'Biller Item': finalBillerItem.name,
          Message: message
        })
        setMode(MODE.LOOKUPFAILED)
        setLookUpFailedMessage(message)
      }

      setIsProcessing(false)
    },
    [
      requiresMeterNumber,
      requiresPhoneNumber,
      billerItem,
      waecOrWassceTransaction,
      billBillerName,
      triggerLookupBillItem,
      billBillerItem,
      countryParam,
      performAutoLookup,
      billerItemParam
    ]
  )

  // useMountEffect, instead of useEffect, to prevent side effects
  // from updates to handleAddBill logic.
  useMountEffect(() => {
    const loadDetails = async () => {
      try {
        if (performAutoLookup) {
          setIsProcessing(performAutoLookup)
          const billerItemsResponse = await fetchBillerItems({
            billerId: billerIdParam!,
            country: countryParam!
          }).unwrap()

          const billerItems = billerItemsResponse.items

          // Find the specific biller's item from the new billerItemResponse
          const updatedBillerItem = billerItems?.filter(
            (_item: any) =>
              _item?.billerItemId === Number(billerItemParam.billerItemId)
          )

          if (updatedBillerItem?.length) {
            // Update the current billerItem to make sure we have the latest
            setBillerItem(updatedBillerItem[0])
            await handleAddBill()
          } else {
            // show new bill modal if we don't have the updated billeritem
            setShowAddNewModal(true)
          }
        }
      } catch (error) {
        errorService.handleError(error, 'Bill Payments - loadDetails')
      }
      setIsProcessing(false)
    }

    loadDetails()
  })

  const handlePayBill = useCallback(async () => {
    let lookupResponse
    if (!amount) {
      return
    }

    if (billBillerName === UmemeElectricityBiller.UMEME) {
      setIsProcessing(true)
      try {
         lookupResponse = await triggerLookupBillItem({
          body: {
            amount,
            billerItem: billBillerItem,
            country: countryParam,
            customerRef: (billerItem as BillerItemWithAttributeValues)
              .customerRef,
            serviceType: (billerItemParam as BillerItemWithAttributeValues)
              ?.billType
          }
        }).unwrap()
        analytics.track(analytics.events.BILLS_LOOKUP_SUCCESS, {
          'Biller Item': 'UMEME',
          'Bill Payment Id': lookupResponse.customerRef
        })
        // Trigger showing the payment form for user to input amount
        setMode(MODE.PAYMENT)
        // Update billDetails which shows a table of the bill details for the bill payment in question
        setBillDetails({
          isComplete: false,
          mobile: (billDetails as BillerItemWithAttributeValues).mobile,
          ...lookupResponse
        })
        // Set billPayment to be referenced when user pays bill
        setBillPayment(lookupResponse)
        setCustomerName(lookupResponse.customerName ?? '')
      } catch (error) {
        errorService.handleError(error, 'Bill Payments - look up')
        setIsProcessing(false)
        return
      }
    }

    if (momoPaymentMethod?.type !== PaymentMethodType.MOBILE_MONEY) {
      const checkerAmount = isSameCurrency ? amount : amountHint

      setIsProcessing(true)
      const checker = storeHelpers.hasInsufficientBalance(
        checkerAmount,
        store.currency,
        rates,
        paymentMethodCount,
        null,
        'Bill Payment',
        () => null,
        false,
        isBillPaymentBalanceSelectionEnabled
          ? sourceBalance?.availableBalance
          : store.availableBalance
      )

      setShowPassThrough(!!checker.showPassThrough)

      if (checker.hasInsufficientBalance) {
        setIsProcessing(false)
        return
      }
    }

    analytics.track(analytics.events.BILLS_PAYMENT_TAPPED, {
      'Bill Payment Id': billPayment.id || '',
      Amount: amount
    })

    if (
      (spendableBalances.isLoading ||
        !sourceBalance?.availableBalance ||
        !transactionSource?.currency) &&
      momoPaymentMethod?.type !== PaymentMethodType.MOBILE_MONEY
    ) {
      return
    }
    setIsProcessing(false)
    navigation.navigate(routeConst.TRANSACTION_QUOTE_SCREEN, {
      type: TransactionType.BILL_PAYMENT,
      amount: Number(amount),
      country: countryParam,
      serviceType: (billerItemParam as BillerItemWithAttributeValues)?.billType,
      sessionId: (billDetails as BillerItemWithAttributeValues).sessionId,
      phoneNumber:
        (billDetails as BillerItemWithAttributeValues).mobile || phoneNumber,
      currency: momoPaymentMethod
        ? momoPaymentMethod.currency
        : transactionSource?.currency,
      customerRef:
        (billDetails as BillerItemWithAttributeValues).customerRef ||
        meterNumber ||
        (billerItem as BillerItemWithAttributeValues).customerRef,
      customerName:
        (billDetails as BillerItemWithAttributeValues).customerName ||
        customerName || lookupResponse?.customerName,
      billerName: billerItem.name,
      destinationCurrency: currency || userPrimaryCurrency,
      billerItem: billBillerItem,
      isDestinationTransfer: true, // isDestinationTransfer should always be true for all cases
      frequency: recurrencePayment.frequency,
      date: billPaymentRecurrence.date,
      logo: logo,
      redirect: {
        route: routeConst.HOME
      },
      paymentMethod: momoPaymentMethod,
      source: {
        type: momoPaymentMethod
          ? BalanceKind.LinkedAccount
          : BalanceKind.Balance,
        linkedAccountId: momoPaymentMethod?.linked_account_id
      },
      isPassthroughTransaction: momoPaymentMethod ? true : false,
      linkedAccountId: momoPaymentMethod?.linked_account_id
    })
  }, [
    amount,
    billBillerName,
    momoPaymentMethod,
    billPayment.id,
    spendableBalances.isLoading,
    sourceBalance?.availableBalance,
    transactionSource?.currency,
    navigation,
    countryParam,
    billerItemParam,
    billDetails,
    phoneNumber,
    meterNumber,
    customerName,
    billerItem,
    currency,
    userPrimaryCurrency,
    billBillerItem,
    recurrencePayment.frequency,
    billPaymentRecurrence.date,
    logo,
    triggerLookupBillItem,
    isSameCurrency,
    amountHint,
    rates,
    paymentMethodCount,
    isBillPaymentBalanceSelectionEnabled
  ])

  const handleActionAfterDepositCompletion = () => {
    setShowPassThrough(false)
  }

  const addNewBill = () => {
    setShowAddNewModal(false)
    navigation.navigate(routeConst.BILLS_BILLERS_LIST, {
      showSchedule
    })
  }

  return (
    <Container white justifyContent="flex-start">
      <AddNewBillModal
        onClose={() => setShowAddNewModal(false)}
        show={showAddNewBillModal}
        onAddBill={addNewBill}
        billerName={billerItemParam.billerName}
      />
      <ScrollView
        keyboardShouldPersistTaps="always"
        keyboardDismissMode="on-drag">
        <Container white innerScreenContainer>
          {mode === MODE.LOOKUP && (
            <PaymentLookup
              item={billerItem}
              isProcessing={isProcessing}
              onAddBillTapped={handleAddBill}
              meterNumber={meterNumber}
              requiresMeterNumber={requiresMeterNumber}
              requiresPhoneNumber={requiresPhoneNumber}
            />
          )}

          {mode === MODE.PAYMENT && (
            <>
              <PaymentAmountInput
                item={billerItem}
                onPayBillTapped={handlePayBill}
                loading={isProcessing}
                onAmountChange={setAmount}
                setRecurrencePayment={setRecurrencePayment}
                isBillPaymentBalanceSelectionEnabled={
                  isBillPaymentBalanceSelectionEnabled
                }
                isBillpaymentRecurringScheduleEnabled={
                  isBillpaymentRecurringScheduleEnabled
                }
                showSchedule={showSchedule}
                billPaymentRecurrence={billPaymentRecurrence}
                amountHint={amountHint}
                isLoadingAmountHint={isLoading}
                isAmountHintError={isError}
                destinationCurrency={currency}
                feeFields={feeFields}
                isSameCurrency={isSameCurrency}
              />
            </>
          )}

          {mode === MODE.LOOKUPFAILED && (
            <BillMeterValidationFailed message={lookUpFailedMessage} />
          )}
        </Container>
      </ScrollView>

      <PassThroughModal
        amount={amount}
        show={showPassThrough}
        completion={handleActionAfterDepositCompletion}
        context={'Bill Payment'}
        dismiss={() => {
          setShowPassThrough(false)
        }}
      />
    </Container>
  )
}

Payment.navigationOptions = {
  title: 'Enter details'
}
