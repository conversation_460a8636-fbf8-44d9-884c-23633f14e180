import { StackScreenProps } from '@react-navigation/stack'

import { coreService } from '@shared/services'
import { ArrayElement } from '@shared/types'

import { MarketplaceParamsList } from '@features/marketplace/marketplace-utils'

import { routeConst } from '@navigation'

// Bills API response and request type
export type RecentBillPayments =
  coreService.RecentBillPaymentsForUserApiResponse['items']
// same endpoint is used for bill payment
export type BillPayment = coreService.FundBetAccountApiResponse

export enum BillerAttributeTypes {
  STRING = 'STRING',
  NUMBER = 'NUMBER'
}

export type Biller = ArrayElement<
  Required<coreService.AllBillersForCountryApiResponse>['billers']
>

export type BillerItem = ArrayElement<
  Required<coreService.BillerItemsApiResponse>['items']
>

export type BillPaymentHistoryItem = ArrayElement<
  Required<coreService.RecentBillPaymentsForUserApiResponse>['items']
>

export type BettingBillerItem = ArrayElement<
  Required<coreService.GetAllBettingBillerItemApiResponse>['items']
>

export type BetHistoryItem = ArrayElement<
  Required<coreService.BetHistoryApiResponse>['items']
>

export type BillDetail = ArrayElement<
  // same endpoint is used for bill payment
  Required<coreService.FundBetAccountApiResponse>['paymentResponseAttributes']
>

export type BetFundDetailsInputScreenProps = StackScreenProps<
  MarketplaceParamsList,
  typeof routeConst.BET_FUND_DETAIL_INPUT_SCREEN
>

export type BetFundRecurringSchedulesScreenProps = StackScreenProps<
  MarketplaceParamsList,
  typeof routeConst.BET_FUND_RECURRING_SCHEDULES_SCREEN
>

export type BetFundRecurringScheduleDetailScreenProps = StackScreenProps<
  MarketplaceParamsList,
  typeof routeConst.BET_FUND_RECURRING_SCHEDULE_DETAIL
>

export enum BillPaymentType {
  CABLE_TV = 'CABLE_TV',
  ELECTRICITY = 'ELECTRICITY',
  WATER = 'WATER',
  EDUCATION = 'EDUCATION',
  INSURANCE = 'INSURANCE',
  INTERNET = 'INTERNET',
  BETTING = 'BETTING'
}

export enum UmemeElectricityBiller {
  UMEME = 'UMEME'
}
