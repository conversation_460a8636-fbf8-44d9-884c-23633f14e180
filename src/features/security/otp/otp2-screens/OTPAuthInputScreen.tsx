import React, { useCallback, useEffect, useRef, useState } from 'react'

import {
  <PERSON>ert,
  ButtonGroup,
  Checkbox,
  Container,
  FixedFooter,
  KeyboardAvoid,
  Stack,
  buttonAppearance,
  defaultAnalyticEvents
} from '@chippercash/chipper-ui'
import { AxiosError } from 'axios'
import { useSelector } from 'react-redux'
import styled from 'styled-components/native'

import { phoneNumberHelpers } from '@packages/chipper-utils'

import { PhoneInput } from '@shared/components'
import {
  CONST,
  analytics,
  authService,
  coreService,
  errorService,
  remoteConfig
} from '@shared/services'
import { chipperPublicApi } from '@shared/services/api/core'
import {
  localUserData,
  otpV2VerificationSlice,
  useAppDispatch
} from '@shared/store'
import { RTKQueryError } from '@shared/types'
import {
  AsyncStorageIdenfierKeys,
  AuthModes,
  OTPTypes,
  OtpMethod,
  getIdentifierFromAsyncStorage,
  removeIdentifierFromAsyncStorage,
  saveIdentifierInAsyncStorage
} from '@shared/utils'

import {
  NavigationService,
  OTPAuthInputScreenProps,
  routeConst
} from '@navigation'

import { EmailInputV2 } from '../otp-components'
import { OTPOtherWaysModal } from './otp-modals/OTPOtherWaysModal'
import { OTPussdModal } from './otp-modals/OTPussdModal'
import { sendOTPToEmailUtil, sendOTPToPhoneNumberUtil } from './utils/sendOTP'
import { useOTPFunctions } from './utils/useOTPFunctions'
import { useVerificationHandler } from './utils/useVerificationHandler'

export const OTPAuthInputScreen = ({
  route,
  navigation
}: OTPAuthInputScreenProps) => {
  const dispatch = useAppDispatch()
  const { otpMethod, authMode, sourceScreen, sourceScreenParams } =
    route.params ?? {}

  const isEmail = otpMethod === OtpMethod.EMAIL
  const isPhoneNumber = otpMethod === OtpMethod.PHONE
  const simCountry = useSelector(localUserData.selectors.getSimCountry) || ''
  const { data: ipData } = coreService.useGetIpQuery()

  const ipCountry = ipData?.country || ''
  const [userSelectedCountry, setUserSelectedCountry] = useState('')

  const [phoneNumber, setPhone] = useState('')
  const [phoneValid, setPhoneValid] = useState(false)

  const [emailAddress, setEmail] = useState('')
  const [emailValid, setEmailValid] = useState(false)

  const country =
    (userSelectedCountry && userSelectedCountry.trim()) ||
    (ipCountry && ipCountry.trim()) ||
    (simCountry && simCountry.trim()) ||
    'US'
  const {
    configValues: {
      featureFlags: { SMS_OTP_ENABLED_COUNTRIES }
    }
  } = remoteConfig.useRemoteConfig()
  let isSmsAllowedForCountry = false
  if (Array.isArray(SMS_OTP_ENABLED_COUNTRIES)) {
    isSmsAllowedForCountry = SMS_OTP_ENABLED_COUNTRIES.includes(country)
  }

  const [emailErrorMessage, setEmailErrorMessage] = useState('')
  const [phoneErrorMessage, setPhoneErrorMessage] = useState('')

  const [showOtherWayModal, setShowOtherWayModal] = useState(false)

  const [showUSSDpopUpModal, setShowUSSDpopUpModal] = useState(false)
  const [dialingCode, setDialingCode] = useState('')
  const [USSDOTP, setUSSDOTP] = useState(-1)
  const [shouldPollViaUSSD, setShouldPollViaUSSD] = useState(false)
  const [handleVerificationLoading, setHandleVerificationLoading] =
    useState(false)

  const [checkUssdStatus] =
    authService.api.endpoints.checkUssdStatus.useLazyQuery()
  const [getJwt] = authService.useCreateAJwtMutation()

  const [checkAuthUser] =
    authService.api.endpoints.checkIfAccountExists.useLazyQuery()

  const [checkCoreUser] =
    chipperPublicApi.endpoints.publicUserById.useLazyQuery()

  const [authMethodsQuery] =
    authService.api.endpoints.getAllAuthMethods.useLazyQuery()

  // this data is needed not during signup/login, but when a user wants to add a new auth method
  // so it would already have been fetched by the time we get here
  const { data: allAuthMethodsData } =
    authService.api.endpoints.getAllAuthMethods.useQueryState()
  const [addPhoneAuthMethod] = authService.useAddPhoneAuthMethodMutation()

  const [smsOTPSelected, setSmsOTPSelected] = useState(false)

  const isOpeningNewAccount = authMode === AuthModes.SIGNUP
  const addNewIdentifier = authMode === AuthModes.ADD_NEW_IDENTIFIER
  const isLogin = authMode === AuthModes.LOGIN
  const isLoginFromUS = isLogin && country === 'US'
  const isLoginOrSignup = isLogin || isOpeningNewAccount
  const { ADD_AUTH_OTP_SENT, ONBOARDING_OTP_SENT } = analytics.events

  const [ussdPollAttempts, setUssdPollAttempts] = useState(0)
  const ussdPollAttemptsRef = useRef(ussdPollAttempts)
  const shouldPollViaUSSDRef = useRef(shouldPollViaUSSD)
  const MAX_POLLING_ATTEMPTS = 10

  const [rememberIdentifier, setRememberIdentifier] = useState(false)
  const [asyncIdentifier, setAsyncIdentifier] = useState({
    type: '',
    value: ''
  })
  const { EMAIL_AUTH_IDENTIFIER, PHONE_AUTH_IDENTIFIER } =
    AsyncStorageIdenfierKeys

  const { onAuthMethodAlreadyLinked, onCompleteAuthMethodAdded } =
    useOTPFunctions(sourceScreen)

  // Update the ref whenever ussdPollAttempts/shouldPollViaUSSD changes so that it's still accessible by the setTimeout closure in USSDPolling function
  useEffect(() => {
    ussdPollAttemptsRef.current = ussdPollAttempts
    shouldPollViaUSSDRef.current = shouldPollViaUSSD
  }, [ussdPollAttempts, shouldPollViaUSSD])

  useEffect(() => {
    const fetchIdentifier = async () => {
      const identifier = await getIdentifierFromAsyncStorage(
        isEmail ? EMAIL_AUTH_IDENTIFIER : PHONE_AUTH_IDENTIFIER
      )

      if (!identifier) {
        return
      }

      if (isEmail && isLoginOrSignup) {
        setAsyncIdentifier({ type: OTPTypes.EMAIL, value: identifier })
        onValidEmail(identifier)
      } else if (isPhoneNumber && isLoginOrSignup) {
        const parsedPhone =
          phoneNumberHelpers.parseCountryFromPhoneNumber(identifier)
        if (parsedPhone) {
          setAsyncIdentifier({
            type: OTPTypes.SMS,
            value: parsedPhone?.nationalNumber || ''
          })
          setUserSelectedCountry(parsedPhone?.country || '')
          onValidPhoneNumber(identifier)
        }
      }
    }

    fetchIdentifier()
  }, [
    isEmail,
    isLoginOrSignup,
    isPhoneNumber,
    EMAIL_AUTH_IDENTIFIER,
    PHONE_AUTH_IDENTIFIER
  ])

  dispatch(
    otpV2VerificationSlice.actions.setVerificationLoading(
      handleVerificationLoading
    )
  )

  const { handleVerificationComplete } = useVerificationHandler()

  const onValidEmail = (value: string) => {
    setEmail(value)
    setEmailValid(true)
  }

  const onInvalidEmail = () => {
    setEmail('')
    setEmailValid(false)
  }

  const onValidPhoneNumber = (value: string) => {
    setPhone(value)
    setPhoneValid(true)
  }

  const onInvalidPhoneNumber = () => {
    setPhoneValid(false)
  }

  const handleOnPhoneChange = (value: string) => {
    setPhone(value)
    setPhoneErrorMessage('')
  }

  const handleFlagTapped = () => {
    NavigationService.navigate(routeConst.ONBOARDING_COUNTRY_PICKER, {
      onCountrySelected: (countryTapped: string) => {
        setUserSelectedCountry(countryTapped)
      }
    })
  }

  /**
   * method to call while doing USSD polling
   */
  const handleUSSDPolling = useCallback(
    async (otp: number, identifier?: string) => {
      return getJwt({
        otp: otp.toString(),
        body: {
          otpType: 'USSD',
          identifier
        }
      }).unwrap()
    },
    [getJwt]
  )

  /*
   * attempt to add a new auth method through ussd
   */
  const addAuthMethodWithUSSD = useCallback(
    async (otp: number, identifier?: string) => {
      try {
        await addPhoneAuthMethod({
          otp: otp.toString(),
          body: {
            otpType: 'USSD',
            identifier
          }
        }).unwrap()
        analytics.track(analytics.events.ADD_AUTH_ADDED, {
          Type: 'USSD',
          Identifier: identifier
        })
      } catch (err) {
        const error = err as RTKQueryError
        const code = errorService.errorCode(error)

        // 2001 is the expected error code to be returned until the user completes the USSD verification flow
        if (code === '2001') {
          throw error
        } else if (code === '3000') {
          // phone already linke to another account, stop the polling
          if (onAuthMethodAlreadyLinked) {
            onAuthMethodAlreadyLinked(CONST.AUTH_TYPE_PHONE)
          }
        } else {
          // unexpected error that should stop the polling
          let errMessage = 'Unable to add phone number.'
          if (error.data?.error?.message) {
            errMessage = error?.data.error.message
          }

          setTimeout(() => {
            Alert.alert({
              title: 'Something went wrong...',
              message: errMessage,
              options: {
                cancelable: true
              }
            })
          }, 500)
        }
      }
    },
    [addPhoneAuthMethod, onAuthMethodAlreadyLinked]
  )

  const handleAddOTPAuthComplete = useCallback(async () => {
    await authMethodsQuery()
    if (onCompleteAuthMethodAdded) {
      onCompleteAuthMethodAdded({
        route: sourceScreen,
        params: sourceScreenParams
      })
    } else {
      navigation.goBack()
    }
  }, [
    authMethodsQuery,
    navigation,
    onCompleteAuthMethodAdded,
    sourceScreen,
    sourceScreenParams
  ])

  /**
   * attempt to create jwt using USSD method
   * if success, send to verification complete
   */
  const USSDPolling = useCallback(async () => {
    if (phoneNumber) {
      try {
        const formattedPhone = phoneNumberHelpers.formatNumber(
          phoneNumber,
          phoneNumberHelpers.NumberFormat.E164
        )

        if (addNewIdentifier) {
          await addAuthMethodWithUSSD(USSDOTP, formattedPhone)
          setShouldPollViaUSSD(false)
          setShowUSSDpopUpModal(false)
          setUssdPollAttempts(0)
          await handleAddOTPAuthComplete()
          return
        }

        const response = await handleUSSDPolling(USSDOTP, formattedPhone)

        if (response.token) {
          setShouldPollViaUSSD(false)
          setShowUSSDpopUpModal(false)
          setUssdPollAttempts(0) // reset attempt on success
          analytics.track(analytics.events.USSD_JWT_POLL_SUCCESS, {
            'USSD OTP': USSDOTP,
            'Phone Number': phoneNumber
          })
          try {
            setHandleVerificationLoading(true)
            await handleVerificationComplete('USSD', response.token)
            setHandleVerificationLoading(false)
          } catch (err) {
            setHandleVerificationLoading(false)
          }
          return
        }
      } catch (error) {
        console.log(
          `Polling: not yet verified, [Attempts]: ${ussdPollAttemptsRef.current}`,
          error
        )
        if (ussdPollAttemptsRef.current === 0) {
          analytics.track(analytics.events.USSD_POLLING, {
            'USSD OTP': USSDOTP,
            'Phone Number': phoneNumber,
            Attempt: ussdPollAttemptsRef.current
          })
        }
        setUssdPollAttempts(prevAttempt => {
          if (prevAttempt + 1 >= MAX_POLLING_ATTEMPTS) {
            setShouldPollViaUSSD(false) // stop polling after reaching max attempts
            setShowUSSDpopUpModal(false)
            analytics.track(analytics.events.USSD_JWT_POLL_FAILED, {
              'USSD OTP': USSDOTP,
              'Phone Number': phoneNumber
            })
            Alert.alert({
              title: 'Something went wrong',
              message: 'Unable to verify your number, please try again'
            })
            return 0 // reset poll attempts
          }
          return prevAttempt + 1
        })
      } finally {
        if (shouldPollViaUSSDRef.current) {
          const initialInterval = 1000 // starting interval of 1s
          const fixedIncrement = 2000 // increment by 2s
          const stagger = Math.random() * 1000 //to avoid synchronised retries
          const backOffTime =
            initialInterval +
            ussdPollAttemptsRef.current * fixedIncrement +
            stagger
          // using a recursive setTimeout here to prevent stacking of requests since the next
          // request is only scheduled after the previous one finishes. The use of `useRef` for shouldPollViaUSSDRef and ussdPollAttemptsRef ensures we're always referencing the latest state values.
          // setInterval won't have factored in the request-response times, and could end up stacking requests if one takes longer than our interval.
          setTimeout(USSDPolling, backOffTime)
        }
      }
    }
  }, [
    USSDOTP,
    handleUSSDPolling,
    handleVerificationComplete,
    phoneNumber,
    addAuthMethodWithUSSD,
    handleAddOTPAuthComplete,
    addNewIdentifier
  ])

  useEffect(() => {
    if (shouldPollViaUSSD) {
      USSDPolling()
    }
  }, [shouldPollViaUSSD, USSDPolling])

  const ussdAlertError = () => {
    const options = [
      {
        text: 'Receive code another way',
        onPress: () => setShowOtherWayModal(true)
      },
      {
        text: 'Cancel',
        onPress: () => setShowUSSDpopUpModal(false)
      }
    ]
    Alert.alert({
      title: 'USSD Unavailable',
      message: 'Please click on the button below to receive code another way',
      buttons: options,
      options: { cancelable: true }
    })
  }

  /**
   * Checks if an account exists with the given auth type and identifier
   * @param authTypeVal
   * @param identifier
   * @returns
   */
  const checkAccountExists = async (
    authTypeVal: 'phone' | 'email',
    identifier: string
  ) => {
    console.log('[checkAccountExistence] Checking account existence')

    const authUserData = await checkAuthUser({
      [authTypeVal]: identifier
    }).unwrap()

    const accountId = authUserData?.accountId

    // Account does not exist on Auth
    if (!accountId) {
      console.log('[checkAccountExistence] Auth account not found')
      return false
    }

    const coreUserResult = await checkCoreUser({
      id: accountId
    })

    // We expect a 404 error if the account does not exist on Core
    const isNon404Error =
      !!coreUserResult.error &&
      (coreUserResult.error as RTKQueryError).status !== 404

    if (isNon404Error) {
      // Only rethrow non-404 errors
      throw coreUserResult.error
    }

    // Account does not exist on Core
    if (!coreUserResult.data?.user) {
      console.log('[checkAccountExistence] Core user not found')
      return false
    }

    return true
  }

  /**
   * Checks if an account exists with the given auth type and identifier and prompts the user if necessary
   *
   * NB: this function must not throw an error
   * @param authTypeVal
   * @param identifier
   * @returns
   */
  const checkShouldProceedForAccountExistenceAndPrompt = async (
    authTypeVal: 'phone' | 'email',
    identifier: string
  ) => {
    let shouldProceed = true
    try {
      const isExistingAccount = await checkAccountExists(
        authTypeVal,
        identifier
      )
      const methodName = authTypeVal === 'phone' ? 'phone number' : 'email'
      const AnalyticsAuthMethod = authTypeVal === 'phone' ? 'SMS' : 'Email'

      if (isExistingAccount && isOpeningNewAccount) {
        Alert.alert({
          title: 'Account already exists',
          message: `This ${methodName} is already linked to an account. Please log in or sign up with a different ${methodName}.`
        })
        analytics.track(analytics.events.OTP_ALREADY_EXISTING_ACCOUNT_SIGN_UP, {
          Reason: 'Account already exists for sign up attempt',
          [AnalyticsAuthMethod]: identifier,
          Method: authTypeVal === 'phone' ? 'SMS' : 'Email'
        })

        shouldProceed = false
      }

      if (!isExistingAccount && isLogin) {
        Alert.alert({
          title: 'Account not found',
          message: `This ${methodName} isn't linked to an account. Please sign up or log in with an existing ${methodName}.`
        })
        analytics.track(analytics.events.OTP_NON_EXISTING_ACCOUNT_SIGNIN, {
          Reason: 'Account does not exist for login attempt',
          [AnalyticsAuthMethod]: identifier,
          Method: authTypeVal === 'phone' ? 'SMS' : 'Email'
        })

        shouldProceed = false
      }
    } catch (e) {
      errorService.handleError(e, 'Send OTP - Account Existence')
      shouldProceed = false
    }

    return shouldProceed
  }

  const checkIfSelfAuthMethod = (type: string, identifier: string) => {
    const authMethods = allAuthMethodsData || {}
    const { phoneNumbers, emails } = authMethods

    if (type === CONST.AUTH_TYPE_PHONE) {
      if (phoneNumbers && phoneNumbers.length > 0) {
        const found = authMethods.phoneNumbers?.find(obj => {
          return obj.phoneNumber === identifier
        })

        if (found) {
          throw new Error(
            'This Phone Number is already associated with your account'
          )
        }
      }
    }

    if (type === CONST.AUTH_TYPE_EMAIL) {
      if (emails && emails.length > 0) {
        const found = authMethods.emails?.find(obj => {
          return obj.email === identifier
        })
        if (found) {
          throw new Error('This Email is already associated with your account')
        }
      }
    }
  }

  const handleContinueWithEmail = async (emailVal: string) => {
    analytics.track(analytics.events.ONBOARDING_EMAIL_ENTERED, {
      Email: emailVal
    })

    setHandleVerificationLoading(true)

    const shouldProceed = await checkShouldProceedForAccountExistenceAndPrompt(
      CONST.AUTH_TYPE_EMAIL,
      emailVal
    )

    if (!shouldProceed) {
      setHandleVerificationLoading(false)
      return
    }

    try {
      if (addNewIdentifier) {
        checkIfSelfAuthMethod(CONST.AUTH_TYPE_EMAIL, emailVal)
      }
      await sendOTPToEmailUtil(emailVal).unwrap()
      analytics.track(
        addNewIdentifier ? ADD_AUTH_OTP_SENT : ONBOARDING_OTP_SENT
      )

      setHandleVerificationLoading(false)

      handleNavigateToOtpConfirmation()
    } catch (error) {
      setHandleVerificationLoading(false)
      console.log('Error sending OTP code to email: ', JSON.stringify(error))

      // Also Log this to Amplitude
      const errorString = JSON.stringify(error)
      analytics.track(analytics.events.OTP_ERROR, {
        Error: errorString
      })

      errorService.handleError(error, 'Send OTP - Email')
      throw error
    }
  }

  const handleContinueWithPhoneSMS = async (phoneVal: string) => {
    analytics.track(analytics.events.ONBOARDING_PHONE_NUMBER_ENTERED, {
      'Phone Number': phoneVal
    })
    setHandleVerificationLoading(true)

    const shouldProceed = await checkShouldProceedForAccountExistenceAndPrompt(
      CONST.AUTH_TYPE_PHONE,
      phoneVal
    )

    if (!shouldProceed) {
      setHandleVerificationLoading(false)
      return
    }

    try {
      if (addNewIdentifier) {
        checkIfSelfAuthMethod(CONST.AUTH_TYPE_PHONE, phoneVal)
      }
      await sendOTPToPhoneNumberUtil(phoneVal).unwrap()

      setHandleVerificationLoading(false)
      handleNavigateToOtpConfirmation()
      analytics.track(
        addNewIdentifier ? ADD_AUTH_OTP_SENT : ONBOARDING_OTP_SENT
      )
    } catch (error) {
      setHandleVerificationLoading(false)
      console.log('Error sending OTP code: ', JSON.stringify(error))

      // Also Log this to Amplitude
      const errorString = JSON.stringify(error)
      analytics.track(analytics.events.OTP_ERROR, {
        Error: errorString
      })

      errorService.handleError(error, 'Send OTP - Phone Verification')
      throw error
    }
  }

  const handleNavigateToOtpConfirmation = useCallback(() => {
    navigation.navigate(routeConst.OTP_CONFIRMATION, {
      label: isEmail ? 'Verify your email' : 'Verify your phone number',
      otpMethod: isEmail ? OtpMethod.EMAIL : OtpMethod.PHONE,
      otpIdentifier: isEmail ? emailAddress : phoneNumber,
      authMode,
      country,
      sourceScreen
    })
  }, [
    navigation,
    isEmail,
    emailAddress,
    phoneNumber,
    authMode,
    country,
    sourceScreen
  ])

  const handleSMSandUSSD = async (phoneNum: string) => {
    try {
      const { data } = await checkUssdStatus({ phoneNumber: phoneNum })

      /*
        Conditions for SMS OTP
        1. If signup and user opts for sms instead of USSD
        2. If !login and no USSD is available for that Country/Jurisdiction
        3. If login attempt is from USA
        4. If user opts for SMS and country is allowed for SMS OTP
      */
      const shouldUseSMS =
        (smsOTPSelected && isOpeningNewAccount) ||
        (!data?.canVerifyViaUSSD && !isLogin) ||
        isLoginFromUS ||
        (smsOTPSelected && isSmsAllowedForCountry)

      if (shouldUseSMS) {
        setHandleVerificationLoading(false)
        setSmsOTPSelected(false)
        return handleContinueWithPhoneSMS(phoneNum)
      }

      // Continue with USSD

      // check if account already exist for login attempt
      const shouldProceed =
        await checkShouldProceedForAccountExistenceAndPrompt(
          CONST.AUTH_TYPE_PHONE,
          phoneNum
        )

      if (!shouldProceed) {
        setHandleVerificationLoading(false)
        return
      }

      // If USSD not available, show alert
      if (!data?.canVerifyViaUSSD) {
        setHandleVerificationLoading(false)
        analytics.track(analytics.events.USSD_UNAVAILABLE, {
          Reason: 'Account is not available for USSD OTP',
          'Phone Number': phoneNum
        })
        return ussdAlertError()
      }

      // Generate random 4 digit OTP
      setHandleVerificationLoading(false)
      const otp = Math.floor(1000 + Math.random() * 9000)
      setUSSDOTP(otp)
      setDialingCode(`${data.ussdVerificationBaseCode}${otp}#`)
      analytics.track(analytics.events.USSD_SEND_OTP, {
        'USSD Code and OTP': `${data.ussdVerificationBaseCode}${otp}#`,
        'Phone Number': phoneNum
      })
      return setShowUSSDpopUpModal(true)
    } catch (err) {
      setHandleVerificationLoading(false)
      const error = err as AxiosError
      errorService.handleError(error, 'USSD status check')
      const message = errorService.errorMessage(error)
      analytics.track(analytics.events.ONBOARDING_USSD_CHECK_FAILED, {
        Result: 'Error',
        Error: message
      })
      analytics.track(analytics.events.USSD_UNAVAILABLE, {
        Reason: 'Error while handling USSD or SMS',
        'Phone Number': phoneNum,
        Error: message
      })
      return ussdAlertError()
    }
  }

  const nextTapped = async () => {
    if (isPhoneNumber && !isLoginFromUS) {
      analytics.track(analytics.events.USSD_NEXT_TAPPED, {
        'Phone Number': phoneNumber
      })
    }

    if (isEmail && !emailValid) {
      return setEmailErrorMessage('Please enter a valid email address')
    }

    if (isEmail && emailAddress) {
      if (rememberIdentifier) {
        saveIdentifierInAsyncStorage(EMAIL_AUTH_IDENTIFIER, emailAddress)
      } else {
        // remove identifier if previously saved so that it doesn't get used in the next login attempt
        removeIdentifierFromAsyncStorage(EMAIL_AUTH_IDENTIFIER)
      }
      return handleContinueWithEmail(emailAddress)
    }

    if (isPhoneNumber && !phoneValid) {
      return setPhoneErrorMessage('Please enter a valid phone number')
    }

    if (isPhoneNumber && phoneNumber) {
      if (rememberIdentifier) {
        saveIdentifierInAsyncStorage(PHONE_AUTH_IDENTIFIER, phoneNumber)
      } else {
        removeIdentifierFromAsyncStorage(PHONE_AUTH_IDENTIFIER)
      }
      return await handleSMSandUSSD(phoneNumber)
    }
  }
  // Show the other way modal for new identifier if the user is from a country that supports SMS OTP
  const showOtherWayForNewIdentifier =
    addNewIdentifier &&
    isPhoneNumber &&
    country !== 'US' &&
    isSmsAllowedForCountry
  return (
    <Container white justifyContent="flex-start">
      <KeyboardAvoid.Main mode="sticky-bottom" isSafeAreaPresent>
        <KeyboardAvoid.Top>
          <Stack gutterHorizontal="s24" gutterTop="s24">
            {isEmail && (
              <EmailInputV2
                onValidEmail={onValidEmail}
                onInvalidEmail={onInvalidEmail}
                errorMessage={emailErrorMessage}
                dismissErrorMessage={() => setEmailErrorMessage('')}
                controlledEmail={
                  OTPTypes.EMAIL === asyncIdentifier.type
                    ? asyncIdentifier.value
                    : undefined
                }
              />
            )}

            {isPhoneNumber && (
              <PhoneInput
                country={country}
                onCountryChange={setUserSelectedCountry}
                onPhoneNumberChange={handleOnPhoneChange}
                onValidPhoneNumber={onValidPhoneNumber}
                onInvalidPhoneNumber={onInvalidPhoneNumber}
                onFlagTapped={handleFlagTapped}
                errorMessage={phoneErrorMessage}
                controlledPhone={
                  OTPTypes.SMS === asyncIdentifier.type
                    ? asyncIdentifier.value
                    : undefined
                }
              />
            )}
          </Stack>
          {isLoginOrSignup && (
            <CheckBoxContainer>
              <Checkbox
                text={`Remember ${isEmail ? 'email' : 'phone number'}`}
                checked={rememberIdentifier}
                onPress={() => setRememberIdentifier(!rememberIdentifier)}
                analyticEventName={defaultAnalyticEvents.NONE}
                backgroundColor="bgBase"
                style={{ borderColor: 'transparent', paddingTop: 0 }}
                size="small"
              />
            </CheckBoxContainer>
          )}
        </KeyboardAvoid.Top>
        <KeyboardAvoid.Bottom>
          <FixedFooter layoutMode="relative">
            <ButtonGroup
              buttons={[
                {
                  title: 'Next',
                  analyticEventName:
                    analytics.events.ONBOARDING_TOGGLE_AUTH_TAPPED,
                  onPress: nextTapped,
                  appearance: 'primary',
                  loading: handleVerificationLoading
                },
                // if !isFromOtherUsage the spread operator will spread the objects into the buttons array
                // else an empty array will be spread resulting in no change to the buttons array
                ...(showOtherWayForNewIdentifier || !addNewIdentifier
                  ? [
                      {
                        title: 'Receive code another way',
                        analyticEventName:
                          analytics.events.ONBOARDING_TOGGLE_AUTH_TAPPED,
                        onPress: () => setShowOtherWayModal(true),
                        appearance: 'secondary' as buttonAppearance,
                        disabled: handleVerificationLoading
                      }
                    ]
                  : [])
              ]}
            />

            {showOtherWayModal && (
              <OTPOtherWaysModal
                showModal={showOtherWayModal}
                setShowModal={setShowOtherWayModal}
                isComingFromEmail={isEmail}
                isComingFromPhoneNumber={isPhoneNumber}
                phoneNumber={phoneNumber}
                email={emailAddress}
                authMode={authMode}
                handleUSSD={handleSMSandUSSD}
                setSmsOTPSelected={setSmsOTPSelected}
                handleSMS={handleContinueWithPhoneSMS}
                isSmsAllowedForCountry={isSmsAllowedForCountry}
                isShowOtherWayForNewIdentifier={showOtherWayForNewIdentifier}
                smsOTPSelected={smsOTPSelected}
                country={country}
              />
            )}

            {showUSSDpopUpModal && (
              <OTPussdModal
                visible={showUSSDpopUpModal}
                setShowModal={setShowUSSDpopUpModal}
                dialingCode={dialingCode}
                setShouldStartPolling={setShouldPollViaUSSD}
                shouldStartPolling={shouldPollViaUSSD}
                ussdAlertError={ussdAlertError}
              />
            )}
          </FixedFooter>
        </KeyboardAvoid.Bottom>
      </KeyboardAvoid.Main>
    </Container>
  )
}

const CheckBoxContainer = styled.View`
  padding-horizontal: ${p => p.theme.spacing.s12};
`
