import React, { useCallback, useEffect, useLayoutEffect, useState } from 'react'
import {
  ActivityIndicator,
  Alert,
  StyleSheet,
  TouchableOpacity
} from 'react-native'

import { Container, Icon, theme } from '@chippercash/chipper-ui'
import PdfView from 'react-native-pdf'
import Share, { ShareOptions } from 'react-native-share'
import { WebView } from 'react-native-webview'

import { CONST, errorService } from '@shared/services'
import { ContentType } from '@shared/types'
import { downloadPDFFile } from '@shared/utils/pdfDownloader'

import { routeConst } from '@navigation'

export const WebviewScreen = ({ route, navigation }: any) => {
  const [isLoading, setIsLoading] = useState(true)
  const [loading, setLoading] = useState(false)

  const { url, contentType, amount, action, share } = route.params || {}
  const isPdf = contentType === ContentType.PDF

  // Add a timeout fallback for PDF loading
  useEffect(() => {
    if (isPdf) {
      const timer = setTimeout(() => {
        if (isLoading) {
          setIsLoading(false)
        }
      }, 10000) // 10 seconds timeout

      return () => clearTimeout(timer)
    }
  }, [isLoading, isPdf])

  const onMessage = (event: any) => {
    if (event.nativeEvent.title.toLowerCase().includes('moneygram')) {
      navigation.navigate(routeConst.CRYPTO_STELLAR_SUCCESS, {
        currency: CONST.CURRENCIES.USD.currency,
        amount,
        action,
        redirect: {
          route: routeConst.HOME
        }
      })
    }
  }

  const runWhenPageLoads = `
    window.addEventListener("message", (event) => {
      window.ReactNativeWebView.postMessage(JSON.stringify(event));
    });
    true; // note: this is required, or you'll sometimes get silent failures
  `

  const handleShare = useCallback(async () => {
    try {
      setLoading(true)
      const filePath = await downloadPDFFile(share as ShareOptions)
      setLoading(false)

      if (!filePath) {
        Alert.alert('Error', 'Could not download the file')
        return
      }

      await Share.open({
        ...share,
        url: filePath
      })
    } catch (err: any) {
      setLoading(false)
      if (
        !err.message.includes('User did not share') &&
        !err.message.includes('User cancelled') &&
        !err.message.includes('canceled')
      ) {
        errorService.handleError(err, 'Share failed')
      }
    }
  }, [share])

  useLayoutEffect(() => {
    navigation.setOptions({
      title: route.params?.navigationTitle || '',
      headerRight: () =>
        share ? (
          loading ? (
            <ActivityIndicator
              size="small"
              color={theme.colors.brandPrimary100}
              style={{ marginRight: 16 }}
            />
          ) : (
            <TouchableOpacity onPress={handleShare} style={{ marginRight: 16 }}>
              <Icon name="share" size={'default'} color={'black100'} />
            </TouchableOpacity>
          )
        ) : null
    })
  }, [handleShare, loading, navigation, route.params, share])

  const handlePdfLoadComplete = () => {
    setIsLoading(false)
  }

  const handlePdfError = (error: any) => {
    errorService.handleError(error, 'Chipper PDF View')
    setIsLoading(false)
  }

  return (
    <Container white>
      {isPdf ? (
        <PdfView
          trustAllCerts={false}
          onLoadComplete={handlePdfLoadComplete}
          onError={handlePdfError}
          style={{ flex: 1 }}
          source={{ uri: url, cache: true }}
        />
      ) : (
        <WebView
          onLoad={() => setIsLoading(false)}
          onError={() => setIsLoading(false)}
          source={{ uri: url }}
          onMessage={onMessage}
          injectedJavaScript={runWhenPageLoads}
        />
      )}
      {isLoading && (
        <ActivityIndicator
          style={{
            ...StyleSheet.absoluteFillObject,
            justifyContent: 'center',
            alignItems: 'center'
          }}
          size="large"
          color={theme.colors.brandPrimary100}
        />
      )}
    </Container>
  )
}
