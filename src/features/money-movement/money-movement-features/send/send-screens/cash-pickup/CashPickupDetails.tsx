import React, { useCallback, useEffect } from 'react'
import { ScrollView, View } from 'react-native'

import {
  <PERSON><PERSON>,
  But<PERSON>,
  Container,
  FixedFooter,
  ListLoader
} from '@chippercash/chipper-ui'
import styled from 'styled-components/native'

import { TransactionMode } from '@shared/features'
import {
  AddressFieldValue,
  DynamicFields,
  PhoneFieldValue,
  SelectFieldValue,
  ServiceProviderFieldValue,
  TextFieldValue,
  useDynamicForm
} from '@shared/features/dynamic-form'
import { usePaymentMethodProvider, usePrimaryCurrency } from '@shared/hooks'
import { analytics, errorService } from '@shared/services'
import { chipperLinkedAccountsApi } from '@shared/services/api/core'
import {
  LinkedAccountType,
  MoneyMovementAccountType,
  NonNullableFields,
  PaymentMethod,
  PaymentMethodType,
  SendType,
  TransactionType
} from '@shared/types'

import { SendCashPickupScreenProps } from '@features/money-movement/money-movement-features/utils/sendTypes'

import { routeConst } from '@navigation'

const CASH_PICKUP_FORM_KEY = 'cash-pickup-form'

export const CashPickupDetails = ({
  navigation,
  route
}: SendCashPickupScreenProps) => {
  const { data: providerData, isLoading } = usePaymentMethodProvider({
    type: PaymentMethodType.CASH_PICKUP,
    country: route.params?.country!
  })

  const provider = providerData as NonNullableFields<
    PaymentMethod & { fields: any[] }
  >

  const country = provider?.country!

  const { handleSubmit, reset } = useDynamicForm({
    formKey: CASH_PICKUP_FORM_KEY
  })

  const isDisabled = !isLoading && !provider?.fields?.length

  useEffect(() => {
    return navigation.addListener('beforeRemove', () => {
      reset()
    })
  }, [navigation, reset])

  const [resolveLinkedAccount, { isLoading: isResolvingAccount }] =
    chipperLinkedAccountsApi.endpoints.resolveLinkedAccount.useMutation()

  const primaryCurrency = usePrimaryCurrency()

  const gotoAmountScreen = useCallback(
    (
      resolvedAccount: any,
      extraParams: {
        serviceProvider?: ServiceProviderFieldValue
        sourceOfFunds?: string
      }
    ) => {
      const currency = primaryCurrency
      const showPromoCode =
        extraParams?.serviceProvider?.code ===
        MoneyMovementAccountType.WESTERN_UNION
      navigation.navigate(routeConst.MONEY_MOVEMENT_AMOUNT_SCREEN, {
        transactionType: TransactionType.SEND_TO_NON_CHIPPER,
        provider,
        mode: TransactionMode.SEND,
        sendType: SendType.CASH_PICKUP,
        serviceProvider: extraParams.serviceProvider,
        sourceOfFunds: extraParams.sourceOfFunds,
        showPromoCode,

        // Hack: only allow one currency so no possibility for conversion
        // TODO: the transaction amount screen should be more agnostic to origin/destination
        originCurrency: currency,
        destinationCurrencies: [provider?.currency!],

        recipient: {
          ...resolvedAccount
        }
      })
    },
    [navigation, primaryCurrency, provider]
  )

  const onSubmit = useCallback(() => {
    handleSubmit(
      async formData => {
        // Extract fields that need extra transformation
        const {
          recipientAddress: _recipientAddress,
          phoneNumber: _recipientPhoneNumber,
          pickUpOperator: _pickupOperator,
          sourceOfFunds: _sourceOfFunds,
          recipientFirstName: _recipientFirstName,
          recipientLastName: _recipientLastName,
          recipientMiddleName: _recipientMiddleName,
          ...rest
        } = formData

        const recipientAddress = _recipientAddress as AddressFieldValue
        const recipientPhoneNumber = _recipientPhoneNumber as PhoneFieldValue
        const pickUpOperator = _pickupOperator as SelectFieldValue
        const sourceOfFunds = _sourceOfFunds as TextFieldValue
        const recipientFirstName = _recipientFirstName as TextFieldValue
        const recipientLastName = _recipientLastName as TextFieldValue
        const recipientMiddleName = _recipientMiddleName as TextFieldValue

        try {
          const body = {
            ...rest,
            isExternal: true,
            country,
            type: LinkedAccountType.VOUCHER,
            recipientAddress: {
              address1: recipientAddress?.street,
              address2: recipientAddress?.additional,
              city: recipientAddress?.city,
              state: recipientAddress?.region,
              postalCode: recipientAddress?.postalCode
            },
            currency: provider?.currency,
            recipientPhoneNumber: recipientPhoneNumber?.phone,
            voucherOperator: pickUpOperator,
            recipientFirstName: recipientFirstName,
            recipientLastName: recipientLastName,
            recipientMiddleName: recipientMiddleName
          }
          const result = await resolveLinkedAccount({
            body
          }).unwrap()

          if (result) {
            const pseudoServiceProvider = { code: pickUpOperator }
            gotoAmountScreen(result, {
              serviceProvider: pseudoServiceProvider,
              sourceOfFunds
            })
          } else {
            Alert.alert({
              title: 'Error',
              message: 'Please ensure that all fields are filled correctly'
            })
          }

          // TODO: enforce the nameMatch and prompt user to force create new link or not
        } catch (e) {
          errorService.handleError(e, 'CashPickupDetails - onSubmit')
        }
      },
      () => {
        Alert.alert({
          title: 'Error',
          message: 'Please ensure that all fields are filled correctly'
        })
      }
    )
  }, [handleSubmit, country, provider, resolveLinkedAccount, gotoAmountScreen])

  return (
    <Container white>
      <ScrollView keyboardDismissMode="on-drag">
        {!isLoading ? (
          <FormSection>
            <DynamicFields
              formKey={CASH_PICKUP_FORM_KEY}
              fields={provider?.fields || []}
            />
          </FormSection>
        ) : (
          <ListLoader title="" subTitle="" number={3} />
        )}
      </ScrollView>
      <FixedFooter layoutMode={'relative'}>
        <Button
          title={'Next'}
          onPress={onSubmit}
          disabled={isResolvingAccount || isDisabled}
          analyticEventName={analytics.events.S2NC_RECIPIENT_DETAILS_SUBMITTED}
        />
      </FixedFooter>
    </Container>
  )
}

const FormSection = styled(View)`
  padding-top: ${p => p.theme.spacing.s16};
  padding-horizontal: ${p => p.theme.spacing.s24};
`
