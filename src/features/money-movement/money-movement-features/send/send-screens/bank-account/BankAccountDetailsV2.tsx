import React, { useCallback, useEffect, useMemo } from 'react'
import { ScrollView, View } from 'react-native'

import {
  But<PERSON>,
  Container,
  FixedFooter,
  InlineNotification
} from '@chippercash/chipper-ui'
import styled from 'styled-components/native'

import { TransactionMode } from '@shared/features'
import {
  AddressFieldValue,
  BankDetailsFieldNames,
  BankFieldValue,
  DynamicFields,
  LinkedAccountFieldValue,
  PhoneFieldValue,
  ServiceProviderFieldValue,
  useDynamicForm
} from '@shared/features/dynamic-form'
import { usePaymentMethodProvider } from '@shared/hooks'
import { analytics, errorService } from '@shared/services'
import {
  ResolveLinkedAccountApiResponse,
  chipperLinkedAccountsApi
} from '@shared/services/api/core'
import { BANK_DETAILS_FORM_KEY } from '@shared/services/constants/constants'
import {
  LinkedAccountType,
  MoneyMovementAccountType,
  NonNullableFields,
  PaymentMethod,
  SendType,
  TransactionType
} from '@shared/types'

import { BankDetailsUSScreenProps } from '@features/money-movement/money-movement-features/utils/sendTypes'

import { routeConst } from '@navigation'

type ResolvedAccount = ResolveLinkedAccountApiResponse & {
  accountNumber?: string
}

const bankDetailsFormKey = BANK_DETAILS_FORM_KEY

export const BankAccountDetailsV2 = ({
  navigation,
  route
}: BankDetailsUSScreenProps) => {
  const { data: providerData, isLoading } = usePaymentMethodProvider({
    type: LinkedAccountType.BankAccount,
    country: route.params?.country!
  })

  const provider = route.params?.provider || providerData

  const country = provider?.country ?? route.params?.country!

  const allFormFields = provider?.fields

  const formFieldsConfig = useMemo(() => {
    return allFormFields?.filter(
      field => field.name !== BankDetailsFieldNames.PURPOSE_OF_FUNDS
    )
  }, [allFormFields])

  // Ensure that the form fields are present on provider even if fields
  // were prepared on FE
  const finalProvider = useMemo(() => {
    return allFormFields?.length
      ? {
          ...provider,
          fields: allFormFields
        }
      : provider
  }, [provider, allFormFields])

  const hasFormFields = !!formFieldsConfig?.length

  useEffect(() => {
    if (!hasFormFields && !isLoading) {
      // Fallback to V1 flow if we somehow landed here without the
      // form fields being present
      // This is temporary until full migration to V2.
      navigation.replace(routeConst.MONEY_MOVEMENT_SEND_BANK_ACCOUNT_DETAILS, {
        provider: finalProvider as NonNullableFields<Required<PaymentMethod>>
      })
    }
  }, [formFieldsConfig, navigation, hasFormFields, isLoading, finalProvider])

  const { handleSubmit, reset } = useDynamicForm({
    formKey: bankDetailsFormKey
  })

  useEffect(() => {
    return navigation.addListener('beforeRemove', () => {
      reset()
    })
  }, [navigation, reset])

  // TODO: what endpoint will the form data be submitted to?
  const [resolveLinkedAccount, { isLoading: isResolvingAccount }] =
    chipperLinkedAccountsApi.endpoints.resolveLinkedAccount.useMutation()

  const gotoAmountScreen = useCallback(
    (resolvedAccount: ResolvedAccount, extraParams?: Record<string, any>) => {
      const showPromoCode =
        extraParams?.serviceProvider?.code ===
        MoneyMovementAccountType.WESTERN_UNION

      navigation.navigate(routeConst.MONEY_MOVEMENT_AMOUNT_SCREEN, {
        transactionType: TransactionType.SEND_TO_NON_CHIPPER,
        provider: finalProvider as NonNullableFields<Required<PaymentMethod>>,
        mode: TransactionMode.SEND,
        sendType: SendType.BANK,
        showPromoCode,
        ...extraParams,
        recipient: {
          ...resolvedAccount
        }
      })
    },
    [navigation, finalProvider]
  )

  const onSubmit = useCallback(async () => {
    handleSubmit(async formData => {
      // Extract fields that need extra transformation
      const {
        [BankDetailsFieldNames.RECIPIENT_ADDRESS]: _recipientAddress,
        [BankDetailsFieldNames.BANK_LOOKUP]: _bankLookup,

        // eslint-disable-next-line @typescript-eslint/no-unused-vars
        [BankDetailsFieldNames.TRANSFER_TYPE]: _, // TODO: backend does not yet support this
        // eslint-disable-next-line @typescript-eslint/no-unused-vars
        [BankDetailsFieldNames.EMAIL]: __, // TODO: backend does not yet support this

        [BankDetailsFieldNames.ACCOUNT_NAME]: _resolvedAccount,

        [BankDetailsFieldNames.SERVICE_PROVIDER]: _serviceProvider,

        [BankDetailsFieldNames.RELATIONSHIP]: relationship,

        [BankDetailsFieldNames.RECIPIENT_PHONE_NUMBER]: _recipientPhoneNumber,

        sourceOfFunds,

        ...rest
      } = formData

      const recipientAddress = _recipientAddress as AddressFieldValue
      const bankLookup = _bankLookup as BankFieldValue
      const resolvedAccount = _resolvedAccount as LinkedAccountFieldValue
      const serviceProvider = _serviceProvider as ServiceProviderFieldValue
      const recipientPhoneNumber = _recipientPhoneNumber as PhoneFieldValue

      const bankId = bankLookup ? Number(bankLookup.bankId) : undefined
      const tiltInstitution = bankLookup?.bankTiltInstitution

      const extraParams = {
        serviceProvider,
        // TODO: Temporary lugging these around to provide on the final
        // transfer request until backend is setup to store these details for
        // against the linked account bank.
        recipientPhoneNumber: recipientPhoneNumber?.phone,
        sourceOfFunds,
        relationship,
        address1: [recipientAddress?.houseNumber, recipientAddress?.street]
          .filter(Boolean)
          .join(', '),
        city: recipientAddress?.city,
        state: recipientAddress?.region,
        postalCode: recipientAddress?.postalCode
      }

      try {
        const body = {
          ...rest,
          relationship: extraParams.relationship,
          recipientPhoneNumber: extraParams.recipientPhoneNumber,
          isExternal: true,
          serviceProviderId: serviceProvider?.id,
          bankId,
          tiltInstitution,
          country,
          accountName: resolvedAccount?.name,
          type: finalProvider?.type,
          recipientAddress: {
            address1: extraParams.address1,
            city: extraParams.city,
            state: extraParams.state,
            postalCode: extraParams.postalCode
          },
          currency: finalProvider?.currency
        }
        const result = await resolveLinkedAccount({
          body
        }).unwrap()

        if (result) {
          gotoAmountScreen(result, extraParams)
        }
      } catch (e) {
        errorService.handleError(e, 'BankAccountDetailsV2 - onSubmit')
      }
    })
  }, [
    country,
    gotoAmountScreen,
    handleSubmit,
    finalProvider,
    resolveLinkedAccount
  ])

  return (
    <Container white>
      <ScrollView keyboardDismissMode="on-drag">
        <InlineNotification
          message="Ensure the account details are correct, Chipper will not be liable for funds sent to the wrong account"
          leadingElementType="info"
          hasDivider={false}
        />

        <AccountDetailsSection>
          <DynamicFields
            formKey={bankDetailsFormKey}
            fields={formFieldsConfig || []}
          />
        </AccountDetailsSection>
      </ScrollView>
      <FixedFooter layoutMode={'relative'}>
        <Button
          title={'Next'}
          disabled={isResolvingAccount}
          onPress={onSubmit}
          analyticEventName={analytics.events.S2NC_RECIPIENT_DETAILS_SUBMITTED}
        />
      </FixedFooter>
    </Container>
  )
}

const AccountDetailsSection = styled(View)`
  padding-top: ${p => p.theme.spacing.s16};
  padding-horizontal: ${p => p.theme.spacing.s24};
`
