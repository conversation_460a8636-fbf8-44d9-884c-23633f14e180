import React, { use<PERSON><PERSON>back, useEffect, useMemo } from 'react'
import { ScrollView, View } from 'react-native'

import {
  Button,
  Container,
  FixedFooter,
  InlineNotification
} from '@chippercash/chipper-ui'
import styled from 'styled-components/native'

import { TransactionMode } from '@shared/features'
import {
  AddressFieldValue,
  DynamicFields,
  LinkedAccountFieldValue,
  MobileMoneyDetailsFieldNames,
  MobileMoneyOperatorFieldValue,
  PhoneFieldValue,
  ServiceProviderFieldValue,
  useDynamicForm
} from '@shared/features/dynamic-form'
import { usePaymentMethodProvider } from '@shared/hooks'
import { analytics, errorService } from '@shared/services'
import {
  ResolveLinkedAccountApiResponse,
  chipperLinkedAccountsApi
} from '@shared/services/api/core'
import { MOBILE_MONEY_DETAILS_FORM_KEY } from '@shared/services/constants/constants'
import {
  LinkedAccountType,
  MoneyMovementAccountType,
  NonNullableFields,
  PaymentMethod,
  SendType,
  TransactionType
} from '@shared/types'

import { BankDetailsUSScreenProps } from '@features/money-movement/money-movement-features/utils/sendTypes'

import { routeConst } from '@navigation'

type ResolvedAccount = ResolveLinkedAccountApiResponse & {
  accountNumber?: string
}

const mobileMoneyDetailsFormKey = MOBILE_MONEY_DETAILS_FORM_KEY

export const MobileMoneyDetailsV2 = ({
  navigation,
  route
}: BankDetailsUSScreenProps) => {
  const { data: providerData, isLoading } = usePaymentMethodProvider({
    type: LinkedAccountType.MobileMoney,
    country: route.params?.country!
  })

  const provider = route.params?.provider || providerData

  const country = provider?.country ?? route.params?.country!

  const allFormFields = provider?.fields

  const formFieldsConfig = useMemo(() => {
    return allFormFields?.filter(
      field => field.name !== MobileMoneyDetailsFieldNames.PURPOSE_OF_FUNDS
    )
  }, [allFormFields])

  // Ensure that the form fields are present on provider even if fields
  // were prepared on FE
  const finalProvider = useMemo(() => {
    return allFormFields?.length
      ? {
          ...provider,
          fields: allFormFields
        }
      : provider
  }, [provider, allFormFields])

  const hasFormFields = !!formFieldsConfig?.length

  useEffect(() => {
    if (!hasFormFields && !isLoading) {
      // Fallback to V1 flow if we somehow landed here without the
      // form fields being present
      // This is temporary until full migration to V2.
      navigation.replace(routeConst.MONEY_MOVEMENT_SEND_MOBILE_MONEY_DETAILS, {
        country: route.params?.country
      })
    }
  }, [
    formFieldsConfig,
    navigation,
    hasFormFields,
    isLoading,
    finalProvider,
    route.params?.country
  ])

  const { handleSubmit, reset } = useDynamicForm({
    formKey: mobileMoneyDetailsFormKey
  })

  useEffect(() => {
    return navigation.addListener('beforeRemove', () => {
      reset()
    })
  }, [navigation, reset])

  // TODO: what endpoint will the form data be submitted to?
  const [resolveLinkedAccount, { isLoading: isResolvingAccount }] =
    chipperLinkedAccountsApi.endpoints.resolveLinkedAccount.useMutation()

  const gotoAmountScreen = useCallback(
    (resolvedAccount: ResolvedAccount, extraParams?: Record<string, any>) => {
      const showPromoCode =
        extraParams?.serviceProvider?.code ===
        MoneyMovementAccountType.WESTERN_UNION

      navigation.navigate(routeConst.MONEY_MOVEMENT_AMOUNT_SCREEN, {
        transactionType: TransactionType.SEND_TO_NON_CHIPPER,
        provider: finalProvider as NonNullableFields<Required<PaymentMethod>>,
        mode: TransactionMode.SEND,
        sendType: SendType.MOBILE_MONEY,
        showPromoCode,
        ...extraParams,
        recipient: {
          ...resolvedAccount
        }
      })
    },
    [navigation, finalProvider]
  )

  const onSubmit = useCallback(async () => {
    handleSubmit(async formData => {
      // Extract fields that need extra transformation
      const {
        [MobileMoneyDetailsFieldNames.RECIPIENT_ADDRESS]: _recipientAddress,
        [MobileMoneyDetailsFieldNames.SERVICE_PROVIDER]: _serviceProvider,
        [MobileMoneyDetailsFieldNames.MOBILE_MONEY_OPERATOR]:
          _mobileMoneyOperator,

        [MobileMoneyDetailsFieldNames.ACCOUNT_NAME]: _resolvedAccount,

        [MobileMoneyDetailsFieldNames.RELATIONSHIP]: relationship,

        [MobileMoneyDetailsFieldNames.RECIPIENT_PHONE_NUMBER]:
          _recipientPhoneNumber,

        sourceOfFunds,

        ...rest
      } = formData

      const recipientAddress = _recipientAddress as AddressFieldValue
      const resolvedAccount = _resolvedAccount as LinkedAccountFieldValue
      const serviceProvider = _serviceProvider as ServiceProviderFieldValue
      const recipientPhoneNumber = _recipientPhoneNumber as PhoneFieldValue
      const carrier = _mobileMoneyOperator as MobileMoneyOperatorFieldValue

      const extraParams = {
        serviceProvider,
        sourceOfFunds,
        // TODO: Temporary lugging these around to provide on the final
        // transfer request until backend is setup to store these details for
        // against the linked account bank.
        recipientPhoneNumber: recipientPhoneNumber?.phone,
        relationship,
        address1: [recipientAddress?.houseNumber, recipientAddress?.street]
          .filter(Boolean)
          .join(', '),
        city: recipientAddress?.city,
        state: recipientAddress?.region,
        postalCode: recipientAddress?.postalCode
      }

      try {
        const body = {
          ...rest,
          relationship: extraParams.relationship,
          country,
          carrier,
          isExternal: true,
          accountName: resolvedAccount?.name,
          recipientPhoneNumber: recipientPhoneNumber?.phone,
          accountNumber: recipientPhoneNumber?.phone,
          type: finalProvider?.type,
          serviceProviderId: serviceProvider?.id,
          recipientAddress: {
            address1: extraParams.address1,
            city: extraParams.city,
            state: extraParams.state,
            postalCode: extraParams.postalCode
          },
          currency: finalProvider?.currency
        }

        const result = await resolveLinkedAccount({
          body
        }).unwrap()

        if (result) {
          gotoAmountScreen(result, extraParams)
        }
      } catch (e) {
        errorService.handleError(e, 'MobileMoneyDetailsV2 - onSubmit')
      }
    })
  }, [
    country,
    gotoAmountScreen,
    handleSubmit,
    finalProvider,
    resolveLinkedAccount
  ])

  return (
    <Container white>
      <ScrollView keyboardDismissMode="on-drag">
        <InlineNotification
          message="Ensure the account details are correct, Chipper will not be liable for funds sent to the wrong account"
          leadingElementType="info"
          hasDivider={false}
        />

        <AccountDetailsSection>
          <DynamicFields
            formKey={mobileMoneyDetailsFormKey}
            fields={formFieldsConfig || []}
          />
        </AccountDetailsSection>
      </ScrollView>
      <FixedFooter layoutMode={'relative'}>
        <Button
          title={'Next'}
          disabled={isResolvingAccount}
          onPress={onSubmit}
          analyticEventName={analytics.events.S2NC_RECIPIENT_DETAILS_SUBMITTED}
        />
      </FixedFooter>
    </Container>
  )
}

const AccountDetailsSection = styled(View)`
  padding-top: ${p => p.theme.spacing.s16};
  padding-horizontal: ${p => p.theme.spacing.s24};
`
