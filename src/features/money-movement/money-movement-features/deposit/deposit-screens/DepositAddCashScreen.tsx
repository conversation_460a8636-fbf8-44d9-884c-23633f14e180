import React, { useCallback, useEffect } from 'react'

import { <PERSON><PERSON>, Container, Spinner } from '@chippercash/chipper-ui'
import { useFocusEffect } from '@react-navigation/native'

import { ChipperEUAccountNumber, ChipperNuban } from '@shared/components'
import { useCanAddCash } from '@shared/hooks'
import { CONST, coreService } from '@shared/services'
import {
  MoneyMovementAccountType,
  MoneyMovementOperation,
  TransactionType
} from '@shared/types'

import { routeConst } from '@navigation'

import { MoneyMovementForm } from '../../../money-movement-components'
import { queries, types } from '../../../money-movement-utils'

export function DepositAddCashScreen({
  route,
  navigation
}: types.WithdrawalCashOutScreenProps) {
  const { data: balanceData, isLoading } = coreService.useBalanceQuery()
  const userCurrency = balanceData?.currency || ''
  const currency = route.params?.currency || userCurrency
  const destinationCurrency = route.params?.destinationCurrency || currency
  const paymentMethodTypes = route.params?.paymentMethodTypes

  const config = queries.useDepositConfig(userCurrency)

  useEffect(() => {
    if (userCurrency && !config) {
      Alert.alert({
        title: 'Add Cash Unavailable',
        message: `Adding cash is not supported for your currency: ${userCurrency}`
      })
      navigation.goBack()
    }
  }, [config, navigation, userCurrency])

  const canAddCash = useCanAddCash()

  const verificationCheck = useCallback(() => {
    if (typeof canAddCash === 'undefined') {
      return
    }
    if (!canAddCash && navigation.canGoBack()) {
      navigation.goBack()
    }
  }, [canAddCash, navigation])

  useFocusEffect(verificationCheck)

  const handlePress = (amount: number, isDestinationTransfer?: boolean) => {
    navigation.navigate(routeConst.PAYMENT_METHOD_SELECT, {
      action: {
        type: TransactionType.DEPOSIT,
        payload: { amount, isDestinationTransfer, destinationCurrency }
      },
      paymentMethodCurrency: currency,
      paymentMethodTypes: paymentMethodTypes
        ? paymentMethodTypes
        : config?.supportedAccountTypes
    })
  }

  if (isLoading) {
    return (
      <Container justifyContent={'center'}>
        <Spinner size={'large'} />
      </Container>
    )
  }

  const supportedAccountTypes = config?.supportedAccountTypes || []

  if (
    supportedAccountTypes.length === 1 &&
    supportedAccountTypes[0] === MoneyMovementAccountType.ACCOUNT_NUMBER
  ) {
    if (currency === CONST.CURRENCIES.NGN.currency) {
      return (
        <Container white>
          <ChipperNuban />
        </Container>
      )
    } else if (
      currency === CONST.CURRENCIES.GBP.currency ||
      currency === CONST.CURRENCIES.EUR.currency
    ) {
      return (
        <Container white>
          <ChipperEUAccountNumber />
        </Container>
      )
    }
  }

  return (
    <MoneyMovementForm
      title={'Add cash with your preferred payment method.'}
      actionType={MoneyMovementOperation.ADDCASH}
      paymentMethodTypes={config?.supportedAccountTypes}
      currency={currency}
      destinationCurrency={destinationCurrency}
      onPress={handlePress}
    />
  )
}
