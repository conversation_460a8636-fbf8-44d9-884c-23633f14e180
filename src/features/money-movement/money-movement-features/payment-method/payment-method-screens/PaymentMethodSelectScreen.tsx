import React, { useCallback, useEffect, useState } from 'react'
import { RefreshControl } from 'react-native'

import {
  <PERSON><PERSON>,
  Container,
  ListItem,
  Spinner,
  useChipperUITheme
} from '@chippercash/chipper-ui'
import { useFocusEffect } from '@react-navigation/native'
import { FlashList } from '@shopify/flash-list'
import styled from 'styled-components/native'

import { TransactionQuoteScreenParams } from '@shared/features/transaction'
import { usePrimaryCurrency } from '@shared/hooks'
import { CONST, analytics, coreService, format } from '@shared/services'
import { TransactionType } from '@shared/types'

import { NavigationService, routeConst } from '@navigation'

import { PAYMENT_METHOD_TYPE_CONFIG } from '../../../money-movement-features/payment-method/payment-method-utils/paymentMethodConstants'
import {
  formatters,
  helpers,
  queries,
  types
} from '../../../money-movement-utils'
import { PaymentMethodTypePickerModal } from '../payment-method-components'
import { constants, types as paymentMethodTypes } from '../payment-method-utils'

export function PaymentMethodSelectScreen({
  route,
  navigation
}: types.PaymentMethodSelectScreenProps) {
  const {
    data: linkedAccountData,
    isLoading,
    refetch
  } = coreService.useLinkedAccountsQuery()
  const { data: balanceData, isLoading: isLoadingBalanceData } =
    coreService.useBalanceQuery()
  const { data: achAuthorizations, isLoading: isLoadingAuthorizations } =
    coreService.useGetAchAuthorizationsQuery()
  const {
    action,
    paymentMethodCurrency: currency,
    isPassthroughTransaction,
    eNaira
  } = route.params || {}

  const [defaultPaymentMethodType, setDefaultPaymentMethodType] =
    useState<typeof PAYMENT_METHOD_TYPE_CONFIG[0]>()

  const primaryCurrency = usePrimaryCurrency()
  const [isPaymentMethodTypeModalVisible, setPaymentMethodTypeModalVisibility] =
    useState(false)

  // Auth and admin is temporarily added to support isAdmin check
  const theme = useChipperUITheme()
  const depositConfig = queries.useDepositConfig(primaryCurrency)
  const { data: userAccountConfig } = coreService.useAccountConfigurationQuery()
  const isEnairaAvailable =
    eNaira && userAccountConfig?.isENairaPaymentsAvailable
  const updateLinkedAccountsData = useCallback(() => {
    refetch()
  }, [refetch])

  useFocusEffect(updateLinkedAccountsData)

  let { paymentMethodTypes: supportedPaymentMethodTypes } = route.params
  let paymentMethods =
    linkedAccountData?.accounts
      ?.filter(item => !item.is_external)
      .filter(item => {
        const paymentMethodCurrency =
          helpers.getPaymentMethodCurrency(item) || currency
        return paymentMethodCurrency === currency
      }) || []

  if (isPassthroughTransaction) {
    supportedPaymentMethodTypes = depositConfig?.supportedAccountTypes
  }

  if (supportedPaymentMethodTypes) {
    // Filtering for eNaira vs. Other Bank Accounts
    paymentMethods = paymentMethods.filter(item => {
      return (
        (isEnairaAvailable
          ? item.bank_name === 'eNaira'
          : item.bank_name !== 'eNaira') &&
        supportedPaymentMethodTypes.includes(item.type!)
      )
    })
  }

  const paymentMethodTypeConfig = constants.PAYMENT_METHOD_TYPE_CONFIG.filter(
    item =>
      supportedPaymentMethodTypes?.includes(item.type) &&
      (isEnairaAvailable ? item.title === 'eNaira' : item.title !== 'eNaira')
  )

  useEffect(() => {
    if (supportedPaymentMethodTypes?.length === 1) {
      const paymentMethodType = paymentMethodTypeConfig[0]
      setDefaultPaymentMethodType(paymentMethodType)
      navigation.setOptions({ title: paymentMethodType?.screenTitle })
    }
  }, [navigation, paymentMethodTypeConfig, supportedPaymentMethodTypes?.length])

  const handleNavigateToPPD = async (
    paymentMethod: paymentMethodTypes.PaymentMethod
  ) => {
    if (
      action.type === TransactionType.DEPOSIT &&
      paymentMethod?.isUssdDepositAvailable
    ) {
      NavigationService.navigate(routeConst.DEPOSIT_USSD_SCREEN, {
        linkedAccountId: action.payload.linkedAccountId ?? paymentMethod.id!,
        amount: action.payload.amount,
        currency: action.payload.currency ?? currency,
        bottomSheetProps: {
          snapPoints: ['30%'],
          enablePanDownToClose: true
        }
      })

      return
    }

    navigation.navigate(routeConst.TRANSACTION_QUOTE_SCREEN, {
      paymentMethod,
      isDestinationTransfer: action.payload.isDestinationTransfer,
      isPassthroughTransaction,
      currency: action.payload.currency ?? currency,
      destinationCurrency: action.payload.destinationCurrency,
      linkedAccountId: action.payload.linkedAccountId ?? paymentMethod.id!,
      type: action.type as TransactionQuoteScreenParams['type'],
      amount: action.payload.amount,
      destinationAmount: action.payload.destinationAmount,
      note: action.payload.note,
      isRecurring: action.payload.isRecurring,
      offset: action.payload.offset,
      frequency: action.payload.frequency,
      cardId: action.payload.cardId,
      phoneNumber: action.payload.phoneNumber,
      contactName: action.payload.contactName,
      optionId: action.payload.optionId,
      carrier: action.payload.carrier,
      bundleDescription: action.payload.bundleDescription,
      country: action.payload.country,
      serviceType: action.payload.serviceType,
      billerItem: action.payload.billerItem,
      customerRef: action.payload.customerRef,
      customerName: action.payload.customerName
    })
  }

  const handlePaymentMethodPress = async (
    paymentMethod: paymentMethodTypes.PaymentMethod
  ) => {
    if (
      action.type === TransactionType.DEPOSIT &&
      helpers.isVerificationRequired(paymentMethod)
    ) {
      const itemConfig = paymentMethodTypeConfig.find(
        ({ type }) => type === paymentMethod.type
      ) || { verificationScreen: '' }

      Alert.alert({
        title: 'One more thing...',
        message:
          'To protect you, we need to verify that you own this payment method. You’ll only do this once.',
        buttons: [
          { text: 'Cancel' },
          {
            text: 'Verify',
            onPress: () => {
              navigation.navigate(
                itemConfig.verificationScreen! as keyof types.MoneyMovementParamsList,
                {
                  paymentMethod
                }
              )
            }
          }
        ],
        options: { cancelable: true }
      })
      return
    } else if (
      primaryCurrency === CONST.CURRENCIES.USD.currency &&
      helpers.isAuthorizationRequired(paymentMethod, achAuthorizations)
    ) {
      if (paymentMethod) {
        navigation.navigate(routeConst.AUTHORIZATION_US_ACH_BANK_ACCOUNT, {
          account: paymentMethod,
          onSuccess: () => null
        })
      }
    }
    handleNavigateToPPD(paymentMethod)
  }

  const handleNewPaymentMethodTypePress = () => {
    if (defaultPaymentMethodType) {
      navigation.navigate(
        defaultPaymentMethodType.screen as keyof types.MoneyMovementParamsList,
        {
          currency,
          config: defaultPaymentMethodType
        }
      )
      return
    }
    setPaymentMethodTypeModalVisibility(true)
  }

  if (isLoading || isLoadingAuthorizations || isLoadingBalanceData) {
    return (
      <Container white justifyContent="center">
        <Spinner size="large" />
      </Container>
    )
  }
  return (
    <SafeAreaContainer>
      <FlashList
        estimatedItemSize={50}
        refreshControl={
          <RefreshControl
            refreshing={isLoading}
            onRefresh={refetch}
            tintColor={theme.colors.brandPrimary100}
          />
        }
        data={paymentMethods}
        ListHeaderComponent={() => (
          <>
            <ListItem
              title={
                defaultPaymentMethodType?.subtitle ?? 'Add new payment method'
              }
              leadingElementProps={{ name: 'plus', withContainer: true }}
              leadingElementType={'icon'}
              analyticEventName={
                analytics.events.PAYMENT_METHOD_SELECT_ADD_TAPPED
              }
              analyticEventOptions={{
                transactionType: action?.type
              }}
              onPress={handleNewPaymentMethodTypePress}
            />
            {isPassthroughTransaction && (
              <ListItem
                title="Balance"
                subTitle={format.fiat.getFormattedValue(
                  balanceData?.balance,
                  balanceData?.currency
                )}
                leadingElementProps={{ name: 'money', withContainer: true }}
                leadingElementType={'icon'}
                analyticEventName={
                  analytics.events.PAYMENT_METHOD_SELECT_BALANCE_TAPPED
                }
                analyticEventOptions={{
                  transactionType: action?.type
                }}
                onPress={() =>
                  handlePaymentMethodPress({
                    type: 'Balance',
                    id: action.payload.linkedAccountId
                  })
                }
              />
            )}
          </>
        )}
        renderItem={({ item }) => (
          <ListItem
            title={helpers.getPaymentMethodNetwork(item)}
            subTitle={formatters.formatPaymentMethod(item)}
            leadingElementProps={{
              name: paymentMethodTypeConfig.filter(
                ({ type }) => type === item.type
              )[0].icon,
              withContainer: true
            }}
            leadingElementType={'icon'}
            analyticEventName={
              analytics.events.PAYMENT_METHOD_SELECT_ITEM_TAPPED
            }
            analyticEventOptions={{
              Type: item.type,
              'Bank Name': item.bank_name,
              Carrier: item.mobile_money_carrier,
              transactionType: action?.type
            }}
            onPress={() => handlePaymentMethodPress(item)}
          />
        )}
      />

      <PaymentMethodTypePickerModal
        isVisible={isPaymentMethodTypeModalVisible}
        onClose={() => setPaymentMethodTypeModalVisibility(false)}
        paymentMethodTypeConfig={paymentMethodTypeConfig}
        onPress={item =>
          navigation.navigate(
            item.screen as keyof types.MoneyMovementParamsList,
            {
              currency,
              config: item
            }
          )
        }
      />
    </SafeAreaContainer>
  )
}

const SafeAreaContainer = styled.SafeAreaView`
  background-color: ${p => p.theme.colors.bgBase};
  flex: 1;
`
