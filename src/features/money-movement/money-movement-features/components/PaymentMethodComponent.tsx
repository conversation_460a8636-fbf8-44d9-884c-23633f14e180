import React from 'react'

import {
  LeadingElement,
  ListItem,
  TrailingElement
} from '@chippercash/chipper-ui'
import { useNavigation } from '@react-navigation/native'

import { TransactionMode } from '@shared/features'
import { analytics } from '@shared/services'
import { CURRENCIES } from '@shared/services/constants/currencies'
import { PaymentMethod, TransactionType } from '@shared/types'
import { getTagColor } from '@shared/utils'

import { routeConst } from '@navigation'

import { useIsBankDetailsV2Enabled } from '../send/send-screens/bank-account/hooks'
import { useIsMobileMoneyDetailsV2Enabled } from '../send/send-screens/mobile-money/hooks/useIsMobileMoneyDetailsV2Enabled'

type LeadingElementConfig = LeadingElement
type TrailingElementConfig = TrailingElement

type PaymentMethodProds = {
  option: PaymentMethod
  isExternalWithdrawalEnabled?: boolean
  country?: string
  hasDivider?: boolean
}

export const PaymentMethodComponent = ({
  option,
  isExternalWithdrawalEnabled,
  country,
  hasDivider
}: PaymentMethodProds) => {
  const navigation = useNavigation<any>()

  const {
    isEnabled: isBankDetailsV2Enabled,
    isLoading: isBankDetailsV2EnabledLoading
  } = useIsBankDetailsV2Enabled({
    country: country!,
    provider: option
  })

  const {
    isEnabled: isMobileMoneyDetailsV2Enabled,
    isLoading: isMobileMoneyDetailsV2EnabledLoading
  } = useIsMobileMoneyDetailsV2Enabled({
    country: country!,
    provider: option
  })

  const getRouteConfigByType = (
    type: string
  ): {
    route: keyof typeof routeConst
    params?: { [key: string]: string }
  } | null => {
    switch (type) {
      case 'BANK':
        if (isBankDetailsV2Enabled) {
          // Head of the V2 flow for bank send
          return {
            route: routeConst.MONEY_MOVEMENT_SEND_BANK_ACCOUNT_RECENTS
          }
        }
        return {
          route: routeConst.MONEY_MOVEMENT_SEND_BANK_ACCOUNT_DETAILS
        }
      case 'CHIPPER_TAG':
        return {
          route: routeConst.MONEY_MOVEMENT_SEND_CHIPPER_TAG
        }

      case 'CASH_PICKUP':
        return {
          route: routeConst.MONEY_MOVEMENT_SEND_CASH_PICKUP
        }
      case 'MOBILE_MONEY':
        if (isMobileMoneyDetailsV2Enabled) {
          // Head of the V2 flow for mobile money send
          return {
            route: routeConst.MONEY_MOVEMENT_SEND_MOBILE_MONEY_DETAILS_V2
          }
        }
        return {
          route: routeConst.MONEY_MOVEMENT_SEND_MOBILE_MONEY_DETAILS
        }
      case 'WITHDRAWAL_CODE':
        return {
          route: routeConst.MONEY_MOVEMENT_AMOUNT_SCREEN,
          params: {
            mode: TransactionMode.WITHDRAWAL_CODE,
            transactionType: TransactionType.SEND_TO_NON_CHIPPER
          }
        }
      default:
        return null
    }
  }

  const getTrailingItemConfig = (): TrailingElementConfig => {
    if (option.tag) {
      return {
        trailingElementType: 'singleValueIcon',
        trailingElementProps: {
          value: {
            children: option.tag,
            color: getTagColor(option.tagStyle),
            type: 'listMedium'
          },
          icon: {
            name: 'chevron-right'
          }
        }
      }
    }

    return {
      trailingElementType: 'icon',
      trailingElementProps: {
        name: 'chevron-right'
      }
    }
  }

  const getLeadingItemConfig = (): LeadingElementConfig => {
    if (option.currency === CURRENCIES.ZMW.currency) {
      if (option.name === 'Agent') {
        return {
          leadingElementType: 'logo',
          leadingElementProps: {
            hasBackground: true,
            source: require('@images/agent_logo.png')
          }
        }
      }

      if (option.name === 'ATM') {
        return {
          leadingElementType: 'icon',
          leadingElementProps: {
            name: 'money',
            withContainer: true
          }
        }
      }
    }

    if (option.type === 'CASH_PICKUP') {
      return {
        leadingElementType: 'icon',
        leadingElementProps: {
          name: 'coins',
          withContainer: true,
          isFeatherIcon: false
        }
      }
    }

    if (option.logo) {
      return {
        leadingElementType: 'logo',
        leadingElementProps: {
          url: option.logo
        }
      }
    }
    switch (option.type) {
      case 'BANK':
        return {
          leadingElementType: 'icon',
          leadingElementProps: {
            name: 'bank',
            withContainer: true
          }
        }
      case 'CHIPPER_TAG':
        return {
          leadingElementType: 'icon',
          leadingElementProps: {
            name: 'chipper',
            withContainer: true
          }
        }
      case 'MOBILE_MONEY':
        return {
          leadingElementType: 'icon',
          leadingElementProps: {
            name: 'tablet',
            withContainer: true
          }
        }
      default:
        return {
          leadingElementType: 'logo',
          leadingElementProps: {
            url: option.logo
          }
        }
    }
  }

  const routeConfig = getRouteConfigByType(option.type!)

  const isLoading =
    isBankDetailsV2EnabledLoading || isMobileMoneyDetailsV2EnabledLoading

  switch (option?.type) {
    case 'CHIPPER_TAG':
      return (
        <ListItem
          isLoading={isLoading}
          title={option?.name || ''}
          subTitle={option.subtitle}
          onPress={() => {
            if (option?.type) {
              navigation.navigate(routeConfig?.route, {
                provider: option,
                country,
                ...routeConfig?.params
              })
            }
          }}
          analyticEventName={analytics.events.SEND_PAYMENT_METHOD_TYPE_TAPPED}
          analyticEventOptions={{
            Type: option.type,
            Name: option.name,
            Country: country
          }}
          hasDivider={hasDivider}
          {...getLeadingItemConfig()}
          {...getTrailingItemConfig()}
        />
      )
    default:
      return isExternalWithdrawalEnabled ? (
        <ListItem
          isLoading={isLoading}
          title={option?.name || ''}
          subTitle={option.subtitle}
          onPress={() => {
            if (option?.type) {
              navigation.navigate(routeConfig?.route, {
                provider: option,
                country,
                ...routeConfig?.params
              })
            }
          }}
          analyticEventName={analytics.events.SEND_PAYMENT_METHOD_TYPE_TAPPED}
          analyticEventOptions={{
            Type: option.type,
            Name: option.name,
            Country: country
          }}
          hasDivider={hasDivider}
          {...getLeadingItemConfig()}
          {...getTrailingItemConfig()}
        />
      ) : null
  }
}
