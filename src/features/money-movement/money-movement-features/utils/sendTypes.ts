import { StackScreenProps } from '@react-navigation/stack'

import {
  TransactionAmountScreenParams,
  TransactionParamList
} from '@shared/features'
import { FieldConfiguration } from '@shared/features/dynamic-form'
import { RecurrenceParamsList } from '@shared/features/recurrence/recurrence-utils/types'
import { coreService } from '@shared/services'
import {
  FeedRecentsApiArg,
  GetSinglePaymentLinkApiResponse
} from '@shared/services/api/core'
import { GetRecentSendsForCountryAndLinkedAccountTypeApiResponse } from '@shared/services/api/core/api/chipperSendApi.generated'
import {
  ArrayElement,
  Carrier,
  MoneyMovementSendChipperTagAmountParams,
  NonNullableFields,
  PaymentLinkStatus,
  PaymentMethod,
  SearchableUser,
  SendSubscriptionType
} from '@shared/types'

import { routeConst } from '@navigation'

export type Bank = ArrayElement<coreService.GetBanksApiResponse>

export type ProviderWithFields = NonNullableFields<Required<PaymentMethod>> & {
  fields?: FieldConfiguration[]
}

export type RecentBeneficiary =
  Required<GetRecentSendsForCountryAndLinkedAccountTypeApiResponse>['recents'][number]

export type SendParamsList = {
  [routeConst.MONEY_MOVEMENT_SEND]: {
    currency?: string
  }
  [routeConst.MONEY_MOVEMENT_EXCHANGE_RATE_PREVIEW]: undefined
  [routeConst.MONEY_MOVEMENT_SEND_CHIPPER_TAG]: {
    country?: string
    provider: NonNullableFields<Required<PaymentMethod>> | Carrier
  }
  [routeConst.MONEY_MOVEMENT_SEND_CHIPPER_TAG_AMOUNT]: MoneyMovementSendChipperTagAmountParams
  [routeConst.MONEY_MOVEMENT_SEND_BANK_ACCOUNT_DETAILS]: {
    provider: ProviderWithFields,
    contextCurrency?: string
  }
  [routeConst.MONEY_MOVEMENT_SEND_BANK_ACCOUNT_DETAILS_V2]?: {
    country?: string
    provider?: ProviderWithFields
  }
  [routeConst.MONEY_MOVEMENT_SEND_BANK_ACCOUNT_RECENTS]: {
    country?: string
    provider?: ProviderWithFields
  }
  [routeConst.MONEY_MOVEMENT_SEND_MOBILE_MONEY_DETAILS]: {
    country?: string
  }
  [routeConst.MONEY_MOVEMENT_SEND_MOBILE_MONEY_DETAILS_V2]?: {
    country?: string
    provider?: ProviderWithFields
  }
  [routeConst.MONEY_MOVEMENT_SEND_MOBILE_MONEY_RECENTS]: {
    country?: string
    provider?: ProviderWithFields
  }
  [routeConst.MONEY_MOVEMENT_SELECT_MOBILE_MONEY_OPERATOR]: {
    country?: string
    onOperatorSelected: (carrier: Carrier) => void
  }
  [routeConst.MONEY_MOVEMENT_SELECT_BANK]: {
    currency: string
    country: string
    onBankSelected: (bank: Bank) => void
    isExternal?: boolean
  }
  [routeConst.MONEY_MOVEMENT_SELECT_PHONE_CONTACT]: {
    onContactSelected: (contact: SearchableUser) => void
  }
  [routeConst.MONEY_MOVEMENT_ACCOUNT_NAME_SCREEN]: {
    accountName?: string
    provider: Carrier & Bank
    accountNumber: string
    onNextClicked: (accountName: string) => void
  }
  [routeConst.MONEY_MOVEMENT_AMOUNT_SCREEN]: TransactionAmountScreenParams
  [routeConst.MONEY_MOVEMENT_SEND_ALL_RECENT]: {
    onUserClicked: (user: SearchableUser) => void
  } & FeedRecentsApiArg
  [routeConst.MONEY_MOVEMENT_SELECT_COUNTRY]: undefined
  [routeConst.MONEY_MOVEMENT_SELECT_PAYMENT_METHOD]: {
    country: {
      name: string
      code: string
      emoji: string
    }
  }
  [routeConst.CRYPTO]: undefined
  [routeConst.MONEY_MOVEMENT_SEND_PAYMENT_LINK]?: {
    status?: PaymentLinkStatus
  }
  [routeConst.MONEY_MOVEMENT_SEND_PAYMENT_LINK_CREATE]: undefined
  [routeConst.MONEY_MOVEMENT_SEND_PAYMENT_LINK_DETAIL]: {
    paymentLinkDetail?: GetSinglePaymentLinkApiResponse
  }
  [routeConst.MONEY_MOVEMENT_SEND_PAYMENT_LINK_EDIT]: {
    paymentLinkDetail?: GetSinglePaymentLinkApiResponse
  }
  [routeConst.MONEY_MOVEMENT_SEND_PAYMENT_LINK_REDIRECT]: {
    user: { id: string }
    ref: string
    amount: string
  }
  [routeConst.MONEY_MOVEMENT_RECURRING_SCHEDULES_SCREEN]: undefined
  [routeConst.MONEY_MOVEMENT_RECURRING_SCHEDULE_DETAIL]: {
    subscriptionId: string
    sendSubscriptionType?: SendSubscriptionType
  }
  [routeConst.MONEY_MOVEMENT_STABLEPAY_TRANSACTION_DETAILS]: {
    recipient: string
    amount: number
    reference: string
    acquirer?: string
    merchantReference?: string
  }
  [routeConst.MONEY_MOVEMENT_SEND_CASH_PICKUP]: {
    country?: string
  }
} & TransactionParamList &
  RecurrenceParamsList

export type SendChipperTagInputAmountScreenProps = StackScreenProps<
  SendParamsList,
  typeof routeConst.MONEY_MOVEMENT_SEND_CHIPPER_TAG_AMOUNT
>
export type SendExchangeRatesPreviewScreenProps = StackScreenProps<
  SendParamsList,
  typeof routeConst.MONEY_MOVEMENT_EXCHANGE_RATE_PREVIEW
>

export type AllRecentsScreenProps = StackScreenProps<
  SendParamsList,
  typeof routeConst.MONEY_MOVEMENT_SEND_ALL_RECENT
>

export type SelectBankScreenProps = StackScreenProps<
  SendParamsList,
  typeof routeConst.MONEY_MOVEMENT_SELECT_BANK
>

export type SelectOperatorScreenProps = StackScreenProps<
  SendParamsList,
  typeof routeConst.MONEY_MOVEMENT_SELECT_MOBILE_MONEY_OPERATOR
>

export type MobileMoneyDetailScreenProps = StackScreenProps<
  SendParamsList,
  typeof routeConst.MONEY_MOVEMENT_SEND_MOBILE_MONEY_DETAILS
>

export type BankDetailScreenProps = StackScreenProps<
  SendParamsList,
  typeof routeConst.MONEY_MOVEMENT_SEND_BANK_ACCOUNT_DETAILS
>
export type BankDetailsUSScreenProps = StackScreenProps<
  SendParamsList,
  typeof routeConst.MONEY_MOVEMENT_SEND_BANK_ACCOUNT_DETAILS_V2
>

export type BankDetailsRecentsProps = StackScreenProps<
  SendParamsList,
  typeof routeConst.MONEY_MOVEMENT_SEND_BANK_ACCOUNT_RECENTS
>

export type AccountNameScreenProps = StackScreenProps<
  SendParamsList,
  typeof routeConst.MONEY_MOVEMENT_ACCOUNT_NAME_SCREEN
>

export type AmountScreenScreenProps = StackScreenProps<
  SendParamsList,
  typeof routeConst.MONEY_MOVEMENT_AMOUNT_SCREEN
>

export type SelectPhoneContactScreenProps = StackScreenProps<
  SendParamsList,
  typeof routeConst.MONEY_MOVEMENT_SELECT_PHONE_CONTACT
>

export type SelectCountryScreenProps = StackScreenProps<
  SendParamsList,
  typeof routeConst.MONEY_MOVEMENT_SELECT_COUNTRY
>

export type SelectPaymentMethodScreenProps = StackScreenProps<
  SendParamsList,
  typeof routeConst.MONEY_MOVEMENT_SELECT_PAYMENT_METHOD
>

export type ChipperTagScreenProps = StackScreenProps<
  SendParamsList,
  typeof routeConst.MONEY_MOVEMENT_SEND_CHIPPER_TAG
>

export type PaymentLinkFormScreenProps = StackScreenProps<
  SendParamsList,
  typeof routeConst.MONEY_MOVEMENT_SEND_PAYMENT_LINK_EDIT
>

export type PaymentLinkListScreenProps = StackScreenProps<
  SendParamsList,
  typeof routeConst.MONEY_MOVEMENT_SEND_PAYMENT_LINK
>

export type PaymentLinkDetailScreenProps = StackScreenProps<
  SendParamsList,
  typeof routeConst.MONEY_MOVEMENT_SEND_PAYMENT_LINK_DETAIL
>

export type SendPaymentLinkRedirectScreenProps = StackScreenProps<
  SendParamsList,
  typeof routeConst.MONEY_MOVEMENT_SEND_PAYMENT_LINK_REDIRECT
>

export type SendRecurringSchedulesScreenProps = StackScreenProps<
  SendParamsList,
  typeof routeConst.MONEY_MOVEMENT_RECURRING_SCHEDULES_SCREEN
>

export type SendRecurringScheduleDetailScreenProps = StackScreenProps<
  SendParamsList,
  typeof routeConst.MONEY_MOVEMENT_RECURRING_SCHEDULE_DETAIL
>

export type StablepayTransactionDetailsProps = StackScreenProps<
  SendParamsList,
  typeof routeConst.MONEY_MOVEMENT_STABLEPAY_TRANSACTION_DETAILS
>

export type SendCashPickupScreenProps = StackScreenProps<
  SendParamsList,
  typeof routeConst.MONEY_MOVEMENT_SEND_CASH_PICKUP
>

export type MobileMoneyRecentsScreenProps = StackScreenProps<
  SendParamsList,
  typeof routeConst.MONEY_MOVEMENT_SEND_MOBILE_MONEY_RECENTS
>

export type MobileMoneyDetailsV2ScreenProps = StackScreenProps<
  SendParamsList,
  typeof routeConst.MONEY_MOVEMENT_SEND_MOBILE_MONEY_DETAILS_V2
>
