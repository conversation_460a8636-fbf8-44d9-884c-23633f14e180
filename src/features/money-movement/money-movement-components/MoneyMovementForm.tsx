import React, { useMemo, useState } from 'react'

import {
  Button,
  KeyboardAvoid,
  Stack,
  Text,
  defaultAnalyticEvents
} from '@chippercash/chipper-ui'
import styled from 'styled-components/native'

import { currencyHelpers } from '@packages/chipper-utils'

import { useDefaultUserBalance } from '@shared/hooks/useDefaultUserBalance'
import { coreService } from '@shared/services'
import { MoneyMovementOperation } from '@shared/types'

import { MoneyMovementAmountInput } from './MoneyMovementAmountInput'
import { MoneyMovementCrossBorderInput } from './MoneyMovementCrossBorderInput'

interface MoneyMovementFormProps {
  title: string
  paymentMethodTypes: string[]
  actionType: MoneyMovementOperation
  currency: string
  destinationCurrency: string
  onPress: (amount: number, isDestinationTransfer?: boolean) => void
}

const MaxButton = ({ onPress }: { onPress: () => void }) => {
  return (
    <Button
      size="tiny"
      title="Max"
      appearance="secondary"
      onPress={onPress}
      analyticEventName={defaultAnalyticEvents.NONE}
    />
  )
}
export function MoneyMovementForm({
  title,
  actionType,
  currency,
  destinationCurrency,
  onPress
}: MoneyMovementFormProps) {
  const [amount, setAmount] = useState(0)
  const [maxBalance, setMaxBalance] = useState(0)
  const [isDestinationTransfer, setIsDestinationTransfer] = useState(false)
  const { data: rateDetails } = coreService.useExchangeRateDetailsQuery({
    from: currency,
    to: destinationCurrency
  })

  const { data: userAccountConfig } = coreService.useAccountConfigurationQuery()
  const isEnairaAvailable = userAccountConfig?.isENairaPaymentsAvailable

  const { defaultBalance } = useDefaultUserBalance()

  const handleContinue = () => {
    onPress(amount, isDestinationTransfer)
  }

  const displayRate = isEnairaAvailable
    ? rateDetails?.adjustedRate
    : rateDetails?.baseRate

  const crossBorderOriginTitle = actionType === MoneyMovementOperation.ADDCASH ? 'You deposit' : 'You cash out'
  const showMax = useMemo(() => {
    const handleMaxButton = () => {
      setAmount(defaultBalance.balance)
      setMaxBalance(defaultBalance.balance)
    }
    if (actionType === MoneyMovementOperation.CASHOUT) {
      return <MaxButton onPress={handleMaxButton} />
    }
  }, [actionType, defaultBalance.balance])

  return (
    <SafeAreaContainer>
      <KeyboardAvoid.Main>
        <ContentContainer>
          <Text spacing="s24" type="h4SemiBold">
            {title}
          </Text>
          {currency === destinationCurrency ? (
            <MoneyMovementAmountInput
              label="Amount"
              placeholder={`${currency} 0.00`}
              currencyPrefix={currencyHelpers.getCurrencyPrefix(currency)}
              onAmountChange={value => setAmount(value)}
              trailingElement={showMax}
              initialAmount={maxBalance}
            />
          ) : (
            <MoneyMovementCrossBorderInput
              origin={{ label: crossBorderOriginTitle, currency }}
              destination={{
                label: 'You receive',
                currency: destinationCurrency
              }}
              rate={displayRate || 0}
              onAmountChange={data => {
                setAmount(Number(data?.amount) || 0)
                setIsDestinationTransfer(!!data?.isDestinationTransfer)
              }}
            />
          )}
          <Stack gutterBottom="s32" />
          <Button
            title="Continue"
            onPress={handleContinue}
            disabled={!amount}
            analyticEventName={defaultAnalyticEvents.NONE}
          />
        </ContentContainer>
      </KeyboardAvoid.Main>
    </SafeAreaContainer>
  )
}

const SafeAreaContainer = styled.SafeAreaView`
  background-color: ${p => p.theme.colors.bgBase};
  flex: 1;
`

const ContentContainer = styled.View`
  padding: ${p => p.theme.spacing.s20};
`
