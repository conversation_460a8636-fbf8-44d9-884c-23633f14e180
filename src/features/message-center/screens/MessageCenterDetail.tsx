import React, { useCallback, useEffect, useMemo } from 'react'
import { StyleSheet } from 'react-native'

import {
  Button,
  Container,
  FixedFooter,
  Image,
  Stack,
  Text,
  defaultAnalyticEvents,
  theme
} from '@chippercash/chipper-ui'
import { NavigationProp, useNavigation } from '@react-navigation/native'
import { ScrollView } from 'react-native-gesture-handler'
import Markdown from 'react-native-markdown-display'
import { useTheme } from 'styled-components'

import { analytics, coreService } from '@shared/services'
import { getCallToActionRoute } from '@shared/utils'

import {
  AppNavigatorParamsList,
  MessageCenterDetailScreenProps,
  handleDeepLinkUrl,
  routeConst
} from '@navigation'

export const MessageCenterDetail = ({
  route
}: MessageCenterDetailScreenProps) => {
  const { currentData: currentMessages } =
    coreService.useGetMessageCenterMessagesQuery()

  const pageProps = useMemo(() => {
    if ('tag' in route.params) {
      // coming from home screen
      return route.params
    } else {
      // coming from a deep link
      return currentMessages?.find(message => message.id === route.params.id)
    }
  }, [currentMessages, route.params])

  const { title, content, tag, deepLinkUrl, deepLinkTitle, headerLink } =
    pageProps || {}

  const navigation = useNavigation<NavigationProp<AppNavigatorParamsList>>()
  const defaultImage = require('@images/message-center/header.png')
  const { mode } = useTheme()

  let headerImage = headerLink ? { uri: headerLink } : defaultImage
  const callToAction = tag ? getCallToActionRoute(tag) : { title: 'Done' }

  const styles = StyleSheet.create({
    body: {
      fontSize: theme.typography.bodyDefault.fontSize,
      color: mode === 'dark' ? theme.colors.white100 : theme.colors.textPrimary
    }
  })

  const [markMessageAsRead] = coreService.useReadMessageMutation()

  const isUnread = pageProps?.readAt === null
  const messageId = pageProps?.id
  const messageClass = pageProps?.messageClass

  const handleLoadOrFocus = useCallback(() => {
    if (isUnread) {
      markMessageAsRead({
        body: { id: messageId, messageClass: messageClass }
      })
    }

    analytics.track(analytics.events.MESSAGE_CENTER_DETAIL_VIEWED, {
      Tag: tag,
      Title: title,
      Id: messageId
    })
  }, [isUnread, markMessageAsRead, messageId, messageClass, tag, title])

  useEffect(() => {
    return navigation.addListener('focus', () => {
      handleLoadOrFocus()
    })
  }, [handleLoadOrFocus, navigation])

  return (
    <Container white>
      <ScrollView>
        <Image
          source={headerImage}
          accessibilityIgnoresInvertColors
          style={{ height: 200, width: '90%', alignSelf: 'center' }}
        />
        <Stack
          flexDirection="column"
          gutterVertical="s24"
          gutterHorizontal="s24"
          spacing="s12">
          <Text type="h3">{title}</Text>
          <Markdown style={styles}>{content}</Markdown>
        </Stack>
      </ScrollView>

      <FixedFooter layoutMode={'relative'}>
        <Button
          title={deepLinkTitle || callToAction.title}
          appearance="primary"
          onPress={() => {
            analytics.track(analytics.events.MESSAGE_CENTER_CTA_TAPPED, { tag })
            if (deepLinkUrl) {
              handleDeepLinkUrl(
                deepLinkUrl,
                { source: 'MESSAGE_CENTER' },
                { isUrlFromSafeSource: true }
              )
            } else {
              navigation.navigate(routeConst.MESSAGE_CENTER_HOME)
            }
          }}
          analyticEventName={defaultAnalyticEvents.NONE}
        />
      </FixedFooter>
    </Container>
  )
}
