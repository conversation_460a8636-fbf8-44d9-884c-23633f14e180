import React from 'react'

import { Image } from '@chippercash/chipper-ui'

import { CONST, format } from '@shared/services'
import { CardAllApiResponse } from '@shared/services/api/core'
import { getCountryFromCurrency } from '@shared/services/countries/countries'

type ImageProps = React.ComponentProps<typeof Image>

export type CardPerk = {
  title: string
  subtitle: string | string[]
  icon?: string
  customIcon?: ImageProps['source']
  multipleCards?: boolean
  isCashBack?: boolean
  isFeatherIcon?: boolean
}

export const SUCCESSFUL_SUBSCRIPTION_ACTION_RESPONSE_CODE = '00'
export const INVALID_PAN_CARD = 'invalid_pan_card'

export enum SubscriptionStatus {
  ACTIVE = 'ACTIVE',
  BLOCKED = 'BLOCKED'
}

const card = {
  title: 'Chipper Card',
  subtitle: 'Built for Your Digital Life',
  nameOnCard: 'YOUR NAME',
  youWillBeNotifiedText:
    'You will be notified as soon as the Chipper Card becomes available',
  tapNotifyText:
    'Tap “Notify Me” to be notified as soon as the Chipper Card becomes available in your country',
  termsText: 'Terms and Conditions ',
  balance: {
    header: 'CURRENT BALANCE',
    subText: 'Fund your Chipper Card below using funds from your Chipper Wallet'
  },
  options: {
    add: 'Top Up',
    cancel: 'Cancel',
    deleteCard: 'Delete card',
    freeze: 'Freeze card',
    refresh: 'Refresh Transactions (Alpha)',
    unfreeze: 'Unfreeze card',
    withdraw: 'Withdraw',
    refundsChargebacks: 'Refunds and chargebacks'
  },
  cancel: {
    confirmTitle: 'Are you sure you want to cancel this card?',
    confirmMessage:
      'Your Chipper Card number will be marked as REJECTED by any vendor trying to charge it.',
    cancelCardButton: 'Cancel Card',
    dismissButton: 'I changed my mind'
  },
  preActive: {
    explanation:
      'Activate your card by topping up with funds from your Chipper wallet'
  },
  freeze: {
    title: 'Freeze card is coming soon!',
    message:
      "In the meantime, you can withdraw your card's balance to your Chipper wallet.\n\nMerchants can only access funds in your Chipper Card balance",
    learnMoreButton: 'LEARN MORE',
    closeButton: 'OK',
    explanation:
      'This card is frozen and cannot be charged, topped up or withdrawn from'
  },
  declineFees: {
    title: 'Decline fees'
  },
  statement: {
    title: 'Card Statements'
  },
  toggleButton: {
    hide: 'Hide card details',
    show: 'Show card details'
  },
  error: {
    message: 'Please, try again later!'
  },
  secondaryCards: {
    title: 'Available Now',
    subtitle: 'This card offers additional benefits such as:'
  },
  create: {
    title: 'Claim Your Chipper Card',
    confirmText: 'Looks Good!',
    onboardingCardText: 'Jon Snow',
    onboardingCardSubtext: 'CREATE YOUR CARD BELOW',
    create: 'Let’s create your card',
    createTitle: 'Which card would you like to create?',
    createSubtitle:
      'You will be able to create another card at any time within the Cards tab.',
    createFooter: 'You will need to confirm your address and phone number',
    nextText: 'Create Card'
  },
  info: {
    title: 'When should I use which card?'
  },
  deposit: {
    title: 'Top up Card',
    topUpButton: 'Top up',
    addFundsTextStart: 'Add funds',
    addFundsTextEnd: (currency: string, userCurrency: string) =>
      `Add funds from your Chipper Wallet to your Chipper ${
        userCurrency !== CONST.CURRENCIES.USD.currency
          ? `Visa ${currency} Card`
          : 'Card'
      }`,
    fundsAvailableTextStart: 'You have ',
    fundsAvailableTextEnd: ' available in your Chipper Wallet',
    invalidDepositMessage: (amount: number, userCurrency: string) =>
      `You only have ${format.fiat.getFormattedValue(
        amount,
        userCurrency
      )} in your ${userCurrency} wallet.`,
    errorDepositingTitle: 'Error Adding Funds',
    errorDepositingMessage:
      'An error occurred when processing the deposit. Please try again shortly or reach out to support.'
  },
  withdraw: {
    title: 'Withdraw',
    withdrawFundsButton: 'Withdraw',
    withdrawFundsTextStart: 'Withdraw funds ',
    withdrawFundsTextEnd: (currency: string, userCurrency: string) =>
      `Withdraw funds from your Chipper ${
        userCurrency !== CONST.CURRENCIES.USD.currency
          ? `Visa ${currency} Card`
          : 'Card'
      } to your Chipper Wallet`,
    fundsAvailableTextStart: 'You have ',
    fundsAvailableTextEnd: ' available on your Chipper Visa Card',
    invalidMessage: (amount?: string, userCurrency?: string) =>
      `You can only withdraw up to ${format.fiat.getFormattedValue(
        amount,
        userCurrency
      )} you have on your Chipper ${
        userCurrency !== CONST.CURRENCIES.USD.currency ? 'Visa Card' : 'Card'
      }`,
    errorTitle: 'Error Withdrawing',
    errorMessage:
      'An error occurred when processing the withdrawal. Please try again shortly or reach out to support.'
  }
}

const primaryCardPerks: CardPerk[] = [
  {
    title: 'Shop Globally',
    subtitle:
      'Use your Chipper Card for online purchases anywhere Visa cards are accepted',
    icon: 'globe'
  }
]

const multipleCardsPerks: CardPerk[] = [
  {
    title: 'Higher Transaction Limits',
    subtitle:
      'Use your Chipper Card for online purchases anywhere Visa cards are accepted',
    icon: 'trending-up'
  },
  {
    title: 'Zero Fees',
    subtitle: 'Limit spending by only using the amount uploaded to your card',
    icon: 'slash'
  }
]

const getFeesPerk = (
  cardCurrency?: string,
  userPrimaryCurrency?: string,
  cardFees?: CardAllApiResponse['cardFees']
) => {
  const userCountry = getCountryFromCurrency(userPrimaryCurrency)
  let feeSubtitles: string[] = []

  const relevantFees = cardFees?.filter(
    fee =>
      userCountry &&
      cardCurrency &&
      fee.country === userCountry &&
      fee.currency === cardCurrency
  )

  relevantFees?.forEach(fee => {
    feeSubtitles.push(`${fee.text}`)
  })

  if (feeSubtitles.length) {
    return {
      title: `${cardCurrency} card fees`,
      subtitle: feeSubtitles,
      icon: 'dollar-sign',
      isFeatherIcon: true
    }
  }
}

const getCardPerks = (
  cardCurrency?: string,
  userPrimaryCurrency?: string,
  cardFees?: CardAllApiResponse['cardFees']
) => {
  let items: CardPerk[] = [
    {
      title: 'Shop Globally',
      subtitle:
        'Use your Chipper Card for online purchases anywhere Visa cards are accepted',
      icon: 'globe'
    }
  ]

  const feesPerk = getFeesPerk(cardCurrency, userPrimaryCurrency, cardFees)

  if (feesPerk) {
    items.push(feesPerk)
  }

  if (cardCurrency !== userPrimaryCurrency) {
    items.push({
      title: 'Higher Transaction Limits',
      subtitle: `Access to higher transaction limits than the ${userPrimaryCurrency} card`,
      icon: 'trending-up'
    })
  }

  items.push(
    {
      title: 'Budget Effectively',
      subtitle: 'Limit spending by only using the amount uploaded to your card',
      icon: 'pie-chart'
    },
    {
      title: 'Digitally Native',
      subtitle: 'A digital card for your digital life',
      icon: 'tablet'
    }
  )

  if (cardCurrency !== userPrimaryCurrency) {
    items.push({
      title: 'Currency Conversion',
      subtitle: `Convert from ${userPrimaryCurrency} to ${cardCurrency} at the lowest rates`,
      icon: 'repeat'
    })
  }

  return items
}

export const strings = {
  card,
  multipleCardsPerks,
  primaryCardPerks,
  getCardPerks,
  getFeesPerk
}
