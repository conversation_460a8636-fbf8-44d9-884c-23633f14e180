import React, { useLayoutEffect } from 'react'
import { ScrollView } from 'react-native'

import {
  Box,
  Container,
  ListItem,
  defaultAnalyticEvents
} from '@chippercash/chipper-ui'

import { SectionHeader } from '@shared/components'
import { SectionDivider } from '@shared/components/SectionDivider'
import {
  useExperimentFeature,
  useFetchAllCards,
  useFetchCard
} from '@shared/hooks'
import { useShowDeactivatedCards } from '@shared/hooks/useShowDeactivatedCards'
import {
  CONST,
  analytics,
  coreService,
  errorService,
  sca
} from '@shared/services'
import { getCountryFromCurrency } from '@shared/services/countries/countries'
import { Currency } from '@shared/store'

import { routeConst } from '@navigation'

import { types, virtualCardsUtils } from '../virtual-card-utils'

/**
 * Shows the user its cards settings
 */
export const CardSettings = ({
  route,
  navigation
}: types.VirtualCardSettingsScreenProps) => {
  const { cardId } = route.params
  const { data: issuedCard } = useFetchCard(cardId)

  const isCardSubscriptionsEnabled = useExperimentFeature(
    CONST.EXPERIMENT_KEYS.CARD_SUBSCRIPTIONS
  )?.enabled

  const [cardUnFreeze] = coreService.useCardUnfreezeMutation()
  const { refetch: refetchAllCards } = useFetchAllCards()
  const { data: cardStatsData } =
    coreService.chipperCardsApi.endpoints.cardsStats.useQuery(
      {
        cardId: cardId!
      },
      {
        skip: !cardId
      }
    )
  const numberOfDeclines = cardStatsData?.numberOfDeclines ?? 0

  const { data: balanceData } = coreService.useBalanceQuery()
  const userCountry = getCountryFromCurrency(balanceData?.currency) as Currency

  const showFreezeUnfreeze =
    issuedCard?.cardStatus !== CONST.CARD_STATUS.PREACTIVE &&
    issuedCard?.cardStatus !== CONST.CARD_STATUS.DELETED &&
    issuedCard?.cardStatus !== CONST.CARD_STATUS.REPLACED

  const showDelete =
    issuedCard?.cardStatus !== CONST.CARD_STATUS.DELETED &&
    issuedCard?.cardStatus !== CONST.CARD_STATUS.REPLACED

  useLayoutEffect(() => {
    if (issuedCard) {
      navigation.setOptions({
        title: `${issuedCard.currency}${
          issuedCard.lastFour ? ` ···· ${issuedCard.lastFour}` : ''
        } card settings`
      })
    }
  }, [issuedCard, navigation])

  const showCardLimits = issuedCard?.cardStatus === CONST.CARD_STATUS.ACTIVE

  const freezeCard = async () => {
    if (issuedCard?.id) {
      navigation.navigate(routeConst.VIRTUAL_CARD_FREEZE, {
        cardId: issuedCard.id
      })
    }
  }

  const unfreezeCard = async () => {
    await sca.checkPinRequiredBeforeAction(
      sca.actions.cardUnfreeze,
      async () => {
        if (issuedCard) {
          try {
            await cardUnFreeze({
              cardId: issuedCard.id as string
            })
          } catch (error) {
            return errorService.handleQueryError(error)
          }
          refetchAllCards()
          navigation.goBack()
        }
      }
    )
  }

  const getDeclineFeesListItemTrailingProps = () => {
    if (Number(numberOfDeclines) > 0) {
      return {
        trailingElementType: 'singleValueIcon',
        trailingElementProps: {
          value: {
            color: 'textYellow',
            children: 'Update',
            type: 'listMedium'
          },
          icon: {
            name: 'chevron-right'
          }
        }
      } as const
    } else {
      return {
        trailingElementType: 'icon',
        trailingElementProps: {
          name: 'chevron-right'
        }
      } as const
    }
  }

  const declineFeesListItemTrailingProps = getDeclineFeesListItemTrailingProps()

  const navigateToMoreInfo = (id: string) => {
    analytics.track(analytics.events.CARDS_TOGGLE_INFO_TAPPED)
    navigation.navigate(routeConst.VIRTUAL_CARD_INFO, {
      numberOfDeclines: numberOfDeclines,
      cardId: id,
      userCountry: userCountry
    })
  }

  const navigateToCardLimits = (id: string) => {
    navigation.navigate(routeConst.VIRTUAL_CARD_LIMITS, {
      cardId: id
    })
  }

  const navigateToCardSubscriptions = (id: string) => {
    navigation.navigate(routeConst.VIRTUAL_CARD_SUBSCRIPTIONS, {
      cardId: id
    })
  }

  const isCardFrozen = issuedCard?.cardStatus === CONST.CARD_STATUS.FROZEN
  const isCardActive = issuedCard?.cardStatus === CONST.CARD_STATUS.ACTIVE

  const [showDeactivatedCards, toggleShowDeactivatedCardsState] =
    useShowDeactivatedCards()

  const settingsItems = (() => {
    const items: React.ReactNode[] = []

    if (!cardId) {
      return items
    }

    if (__DEV__) {
      items.push(
        <ListItem
          titleColor={'textPrimary'}
          title={virtualCardsUtils.strings.card.options.refundsChargebacks}
          subTitle="Dispute an unauthorized charge and report a successful payment with no value delivered."
          onPress={() =>
            navigation.navigate(routeConst.VIRTUAL_CARD_DISPUTES, {
              cardId: issuedCard?.id!
            })
          }
          analyticEventName={defaultAnalyticEvents.NONE}
          hasDivider={true}
        />
      )
    }

    if (showCardLimits) {
      items.push(
        <ListItem
          title={'Card Limits'}
          subTitle="View your card limits."
          onPress={() => navigateToCardLimits(cardId)}
          analyticEventName={defaultAnalyticEvents.NONE}
          hasDivider={true}
        />
      )
    }

    if (showDelete) {
      items.push(
        <ListItem
          titleColor={'textPrimary'}
          title={virtualCardsUtils.strings.card.options.deleteCard}
          subTitle="Delete your card and return funds to your wallet. Deleted cards will not incur decline fees."
          onPress={() =>
            navigation.navigate(routeConst.VIRTUAL_CARD_DELETE, {
              cardId: issuedCard?.id!,
              title: `Delete ${issuedCard?.currency} card`
            })
          }
          analyticEventName={defaultAnalyticEvents.NONE}
          trailingElementType={'none'}
          hasDivider={true}
        />
      )
    }

    if (showFreezeUnfreeze) {
      items.push(
        <ListItem
          titleColor={'textPrimary'}
          title={
            isCardFrozen
              ? virtualCardsUtils.strings.card.options.unfreeze
              : virtualCardsUtils.strings.card.options.freeze
          }
          subTitle={
            isCardFrozen
              ? 'Unfreeze your card to allow transactions again.'
              : 'Freezing your card will result in all attempted transactions being declined. Frozen cards will still incur decline fees.'
          }
          onPress={isCardFrozen ? unfreezeCard : freezeCard}
          analyticEventName={defaultAnalyticEvents.NONE}
          trailingElementType={'none'}
          hasDivider={true}
        />
      )
    }

    items.push(
      <ListItem
        title={virtualCardsUtils.strings.card.declineFees.title}
        subTitle="Learn about decline fees and links to resources about your card."
        onPress={() => navigateToMoreInfo(cardId)}
        analyticEventName={defaultAnalyticEvents.NONE}
        hasDivider={true}
        {...declineFeesListItemTrailingProps}
      />
    )

    if (isCardSubscriptionsEnabled && isCardActive) {
      items.push(
        <ListItem
          title={'Card Subscriptions'}
          subTitle="View and manage your card subscriptions."
          onPress={() => navigateToCardSubscriptions(cardId)}
          analyticEventName={defaultAnalyticEvents.NONE}
          hasDivider={false}
        />
      )
    }

    return items
  })()

  return (
    <Container white>
      <ScrollView>
        <Box>
          {settingsItems}
          {settingsItems.length > 0 && <SectionDivider />}
          <SectionHeader title="General" />
          <ListItem
            title={'Hide deactivated cards'}
            subTitle="Hide deactivated cards from the cards screen"
            onPress={() => toggleShowDeactivatedCardsState()}
            analyticEventName={defaultAnalyticEvents.NONE}
            hasDivider={false}
            trailingElementType="switch"
            trailingElementProps={{
              checked: !showDeactivatedCards
            }}
          />
        </Box>
      </ScrollView>
    </Container>
  )
}
