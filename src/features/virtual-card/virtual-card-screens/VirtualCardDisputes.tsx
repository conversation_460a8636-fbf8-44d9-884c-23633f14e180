import React, { useMemo, useState } from 'react'
import { <PERSON><PERSON>, SafeAreaView, SectionList } from 'react-native'

import {
  Box,
  Button,
  Container,
  Image,
  ListItem,
  Text,
  defaultAnalyticEvents
} from '@chippercash/chipper-ui'
import { BottomSheetScrollView } from '@gorhom/bottom-sheet'
import moment from 'moment'

import { TransactionListItem } from '@shared/components/activity-list-v2'
import { BottomSheetContainer } from '@shared/components/modals/BottomSheetContainer'
import { analytics, devApi } from '@shared/services'

import { routeConst } from '@navigation'

import { types } from '../virtual-card-utils'
import { VirtualCardDisputeType } from '../virtual-card-utils/virtualCardTypes'

const searchImage = require('@images/search.png')

const mockData = [
  {
    title: 'Ride with Uber from SF to LA',
    timestamp: new Date().toISOString(),
    avatarUrl:
      'https://firebasestorage.googleapis.com/v0/b/chipper-cash.appspot.com/o/logos%2FUber.png?alt=media&token=964be97b-d401-4ba3-9029-6902f29040eb',
    status: 'pending',
    amount: 0,
    currency: 'USD'
  },
  {
    title: 'Ride with Uber from SF to LA',
    timestamp: new Date().toISOString(),
    avatarUrl:
      'https://firebasestorage.googleapis.com/v0/b/chipper-cash.appspot.com/o/logos%2FUber.png?alt=media&token=964be97b-d401-4ba3-9029-6902f29040eb',
    status: 'pending',
    amount: 0,
    currency: 'USD'
  },
  {
    title: 'Ride with Uber from SF to LA',
    timestamp: new Date().toISOString(),
    avatarUrl:
      'https://firebasestorage.googleapis.com/v0/b/chipper-cash.appspot.com/o/logos%2FUber.png?alt=media&token=964be97b-d401-4ba3-9029-6902f29040eb',
    status: 'pending',
    amount: 0,
    currency: 'USD'
  }
]

export const VirtualCardDisputes = ({
  navigation,
  route
}: types.VirtualCardDisputesScreenProps) => {
  const [isModalVisible, setIsModalVisible] = useState(false)

  const { data: { disputes } = {} } =
    disputeQuestionsDevApi.endpoints.getRecentDisputes.useQuery(
      {
        cardId: route.params?.cardId
      },
      {
        refetchOnFocus: true
      }
    )

  const monthlyRefundCount = useMemo(() => {
    return (
      disputes?.filter(item => {
        return moment(item.timestamp).isSame(moment(), 'month')
      }).length || 0
    )
  }, [disputes])

  return (
    <Container white>
      <SectionList
        sections={[
          {
            data: disputes || [],
            key: 'recent-disputes',
            title: 'Recents'
          }
        ]}
        renderItem={({ item }) => (
          <TransactionListItem
            gutterHorizontal={'s24'}
            item={{
              title: item.title,
              timestamp: item.timestamp,
              amount: item.amount,
              currency: item.currency,
              status: item.status,
              avatarUrl: item.avatarUrl
            }}
            dateFormat="MMM DD, YYYY"
          />
        )}
        renderSectionHeader={({ section }) => (
          <Box
            backgroundColor="bgBase"
            flexDirection="column"
            gap="s4"
            gutterHorizontal="s24"
            gutterTop="s24"
            gutterBottom="s8">
            <Text type="h4SemiBold">{section.title}</Text>
          </Box>
        )}
        ListEmptyComponent={
          <Box flex={1} justifyContent="center">
            <Image source={searchImage} accessibilityIgnoresInvertColors />
          </Box>
        }
        ListHeaderComponent={
          <Box
            backgroundColor="bgLayerOne"
            gutterHorizontal="s24"
            gap="s2"
            gutterVertical="s16">
            <Text type="h3SemiBold">{monthlyRefundCount}</Text>
            <Text type="hint" color="textSecondary">
              Monthly Refund Count
            </Text>
          </Box>
        }
      />

      <Box gutterHorizontal="s24">
        <Button
          appearance="secondary"
          title="Report an issue/dispute"
          onPress={() => {
            setIsModalVisible(true)
          }}
          analyticEventName={
            analytics.events.TAPPED_REPORT_CARD_TRANSACTION_DISPUTE
          }
        />
      </Box>

      <Modal
        visible={isModalVisible}
        transparent
        animationType="fade"
        onRequestClose={() => setIsModalVisible(false)}>
        <BottomSheetContainer
          onClose={() => setIsModalVisible(false)}
          enableDynamicSizing={true}>
          <BottomSheetScrollView>
            <SafeAreaView>
              <Box gutterHorizontal="s24" gutterBottom="s24">
                <Text type="h3SemiBold">Select dispute type</Text>
              </Box>
              <ListItem
                title="Refund"
                subTitle="Successful payment with no value delivered"
                onPress={() => {
                  navigation.navigate(routeConst.VIRTUAL_CARD_DISPUTE_FORM, {
                    disputeKind: VirtualCardDisputeType.REFUND,
                    cardId: route.params.cardId
                  })
                  setIsModalVisible(false)
                }}
                analyticEventName={defaultAnalyticEvents.NONE}
              />

              <ListItem
                title="Chargebacks"
                subTitle="Dispute an unauthorized charge"
                onPress={() => {
                  navigation.navigate(routeConst.VIRTUAL_CARD_DISPUTE_FORM, {
                    disputeKind: VirtualCardDisputeType.CHARGEBACK,
                    cardId: route.params.cardId
                  })
                  setIsModalVisible(false)
                }}
                analyticEventName={defaultAnalyticEvents.NONE}
                hasDivider={false}
              />
              <Box gutterBottom="s24" />
            </SafeAreaView>
          </BottomSheetScrollView>
        </BottomSheetContainer>
      </Modal>
    </Container>
  )
}

type GetRecentDisputesApiResponse = {
  disputes: typeof mockData
}

type GetRecentDisputesApiArg = {
  cardId: string
}

const disputeQuestionsDevApi = devApi.injectEndpoints({
  overrideExisting: true,
  endpoints: build => ({
    getRecentDisputes: build.query<
      GetRecentDisputesApiResponse,
      GetRecentDisputesApiArg
    >({
      // eslint-disable-next-line @typescript-eslint/no-unused-vars
      queryFn: ({ cardId }) => {
        if (!__DEV__) {
          return {
            data: {
              disputes: []
            }
          }
        }

        return {
          data: {
            disputes: mockData
          }
        }
      }
    })
  })
})
