import React, { useCallback, useMemo } from 'react'
import { <PERSON><PERSON>View } from 'react-native'

import {
  <PERSON><PERSON>,
  Box,
  Container,
  FlatListItems,
  <PERSON>tie,
  Text,
  normalize
} from '@chippercash/chipper-ui'
import moment from 'moment'

import {
  FieldConfiguration,
  FieldKind,
  SelectFieldPresentation
} from '@shared/features/dynamic-form'
import { SectionDynamicForm } from '@shared/features/dynamic-form/dynamic-form-components/SectionDynamicForm'
import { devApi, errorService } from '@shared/services'

import {
  VirtualCardDisputeFormScreenProps,
  VirtualCardDisputeType
} from '../virtual-card-utils/virtualCardTypes'

// For backfilling user information on app startup
export const VirtualCardDisputeForm = ({
  route
}: VirtualCardDisputeFormScreenProps) => {
  const { data: { sections } = {} } =
    disputeQuestionsDevApi.endpoints.getDisputeQuestions.useQuery(
      {
        cardId: route.params?.cardId,
        disputeKind: route.params?.disputeKind!
      },
      {
        refetchOnFocus: true
      }
    )

  const formKey = useMemo(() => {
    return 'dispute-form' + route.params.disputeKind
  }, [route.params.disputeKind])

  const [submitDisputeResponses, submitResult] =
    disputeQuestionsDevApi.endpoints.submitDisputeResponses.useMutation()

  const onSubmit = useCallback(
    async (values: Record<string, unknown>) => {
      const result = await submitDisputeResponses(values)

      const hasError = 'error' in result

      if (hasError) {
        const errorMessage = errorService.errorMessage(
          hasError ? result.error : null
        )
        Alert.alert({
          title: 'Failed to submit responses',
          message: errorMessage || 'Something went wrong.'
        })
        return
      }
    },
    [submitDisputeResponses]
  )

  const onErrors = useCallback((errors: Record<string, string>) => {
    Alert.alert({
      title: 'Unable to submit',
      message: 'Please answer all required questions correctly and try again.',
      buttons: [
        {
          text: 'OK'
        }
      ]
    })
    console.log('errors', errors)
  }, [])

  if (submitResult.isSuccess) {
    return (
      <Container white>
        <ScrollView>
          <Box
            gutterHorizontal="s24"
            gutterVertical="s32"
            gap="s12"
            alignItems="center"
            justifyContent="center">
            <Lottie
              source={require('@images/success.json')}
              style={{
                alignSelf: 'center',
                height: normalize(96),
                width: normalize(96)
              }}
              autoPlay
              loop={false}
            />

            <Text type="h4SemiBold" spacing="s0" alignment={'center'}>
              Dispute submitted successfully
            </Text>

            <Text spacing="s0" alignment={'center'}>
              The review timeline typically takes about 14-30 business days, but
              may take longer. Note that:
            </Text>

            <FlatListItems
              data={[
                {
                  customIcon: require('@images/earn/1.png'),
                  title:
                    'Refund is not guaranteed and will only be issued if remitted by the merchant.'
                },
                {
                  customIcon: require('@images/earn/1.png'),
                  title:
                    'The refunded amount depends on the value remitted by merchant, which may be less than the original amount. Chipper is not responsible for this difference.'
                },
                {
                  customIcon: require('@images/earn/1.png'),
                  title:
                    'If a refund request is found to be fraudulent or flagged as suspicious then it may result in the card and Chipper account being locked.'
                }
              ]}
            />
          </Box>
        </ScrollView>
      </Container>
    )
  }

  return (
    <SectionDynamicForm
      sections={sections || []}
      onFormSubmit={onSubmit}
      onFormErrors={onErrors}
      formKey={formKey}
      isSubmitting={submitResult.isLoading}
      resetOnRemove={true}
    />
  )
}

type GetDisputeQuestionsApiResponse = {
  sections: { title?: string; fields: FieldConfiguration[] }[]
}

type GetDisputeQuestionsApiArg = {
  cardId: string
  disputeKind: VirtualCardDisputeType
}

const disputeQuestionsDevApi = devApi.injectEndpoints({
  overrideExisting: true,
  endpoints: build => ({
    submitDisputeResponses: build.mutation({
      queryFn: () => {
        return {
          data: {}
        }
      }
    }),
    getDisputeQuestions: build.query<
      GetDisputeQuestionsApiResponse,
      GetDisputeQuestionsApiArg
    >({
      queryFn: ({ cardId, disputeKind }) => {
        return {
          data:
            disputeKind === VirtualCardDisputeType.REFUND
              ? {
                  sections: [
                    {
                      title:
                        'Ensure you have contacted the merchant and received confirmation of refund before opening a dispute with us.',
                      fields: [
                        {
                          kind: FieldKind.SELECT,
                          label: 'Have you contacted the merchant?',
                          name: 'contactedMerchant',
                          required: true,
                          presentation: SelectFieldPresentation.BOTTOM_SHEET,
                          options: [
                            {
                              label: 'Yes - merchant approved refund',
                              value: 'yes_approved'
                            },
                            {
                              label: 'Yes - awaiting merchant response',
                              value: 'yes_awaiting'
                            },
                            {
                              label: "No - haven't reached out yet",
                              value: 'no'
                            }
                          ]
                        },
                        {
                          kind: FieldKind.SELECT_TRANSACTION,
                          label: 'Select transaction (USD)',
                          name: 'transactionDetails',
                          title: 'Select transaction',
                          required: true,

                          params: {
                            transactionType: {
                              value: 'ISSUED_CARD_TRANSACTION'
                            },
                            cardId: {
                              value: cardId
                            },
                            afterDate: {
                              value: moment()
                                .subtract(120, 'days')
                                .toISOString()
                            }
                          }
                        },
                        {
                          kind: FieldKind.TEXT,
                          label: 'Describe the transaction in detail',
                          name: 'transactionDescription',
                          required: true
                        },
                        {
                          kind: FieldKind.TEXT,
                          label:
                            'What was the expected value/service from this merchant?',
                          name: 'expectedValue',
                          placeholder: 'Expected value or service',
                          required: true
                        }
                      ]
                    },
                    {
                      fields: [
                        {
                          kind: FieldKind.TEXT,
                          label: 'RRN/ARN number',
                          name: 'rrnArn',
                          placeholder: 'Enter number',
                          required: true
                        },
                        {
                          kind: FieldKind.FILE,
                          required: true,

                          label: 'Proof of refund from merchant/site',
                          title: 'Proof of refund from merchant/site',
                          name: 'proofOfRefund',
                          fieldProps: {
                            inActiveState: {
                              title: 'Proof of refund from merchant/site',
                              subTitle:
                                'Ask merchant for this refund and upload it here.'
                            }
                          }
                        },
                        {
                          kind: FieldKind.CHECKBOX,
                          label: 'Terms and conditions',
                          text: 'By selecting submit, I confirm that all information provided is accurate and legitimate.',
                          required:
                            'You must agree to the terms and conditions to submit this dispute.',
                          name: 'termsAndConditions'
                        }
                      ]
                    }
                  ]
                }
              : {
                  sections: [
                    {
                      title:
                        'Download, fill and sign the chargeback form. Then re-upload it and finally submit for approval.',
                      fields: [
                        {
                          name: 'download_form',
                          kind: FieldKind.FILE_DOWNLOAD,
                          label: 'Download chargeback form',
                          url: 'https://firebasestorage.googleapis.com/v0/b/chipper-cash.appspot.com/o/files%2Fform-question%2Fmerchant-service-dispute.pdf?alt=media&token=d6eff213-20e8-43bf-9e34-595120a8934a',
                          filename: 'chargeback_form'
                        }
                      ]
                    },
                    {
                      title:
                        'Select transaction and upload the filled chargeback form',
                      fields: [
                        {
                          kind: FieldKind.SELECT_TRANSACTION,
                          label: 'Select transaction',
                          name: 'transactionDetails',
                          required: true,
                          title: 'Select transaction',
                          params: {
                            transactionType: {
                              value: 'ISSUED_CARD_TRANSACTION'
                            },
                            cardId: {
                              value: cardId
                            },
                            afterDate: {
                              value: moment()
                                .subtract(120, 'days')
                                .toISOString()
                            }
                          }
                        },
                        {
                          kind: FieldKind.FILE,
                          label: 'Filled chargeback form',
                          name: 'chargebackForm',
                          required: true,
                          title: 'Chargeback form',
                          fieldProps: {
                            inActiveState: {
                              title: 'Filled chargeback form',
                              subTitle: 'Upload the filled and signed form'
                            },
                            activeState: {
                              title: 'Filled chargeback form',
                              subTitle: 'Upload the filled and signed form'
                            }
                          }
                        },
                        {
                          kind: FieldKind.CHECKBOX,
                          label: 'Terms and conditions',
                          text: 'By selecting submit, I confirm that all information provided is accurate and legitimate.',
                          required:
                            'You must agree to the terms and conditions to submit this dispute.',
                          name: 'termsAndConditions'
                        }
                      ]
                    }
                  ]
                }
        }
      }
    })
  })
})
