import React from 'react'

import { forFade } from '@chippercash/chipper-ui'
import { createStackNavigator } from '@react-navigation/stack'

import { withModals } from '@shared/components'
import { TransactionNavigatorGroup } from '@shared/features'
import { selectCardById } from '@shared/hooks'

import { routeConst } from '@navigation'
import {
  defaultHeaderOptions,
  modalScreenConfig,
  stackScreenConfig
} from '@navigation/config'

import * as screens from '../virtual-card-screens'
import { types } from '../virtual-card-utils'
import { VirtualCardDisputeType } from '../virtual-card-utils/virtualCardTypes'

const Stack = createStackNavigator<types.VirtualCardParamList>()

export const VirtualCardStackGroup = () => (
  <Stack.Group screenOptions={stackScreenConfig()}>
    <Stack.Screen
      name={routeConst.VIRTUAL_CARD_SETTINGS}
      component={screens.CardSettings}
      options={modalScreenConfig({ title: 'Card Settings' })}
    />
    <Stack.Screen
      name={routeConst.VIRTUAL_CARD_DELETE}
      component={screens.CardDelete}
      options={({ route }) =>
        modalScreenConfig({ title: route.params?.title || 'Delete Card' })
      }
    />

    <Stack.Screen
      name={routeConst.VIRTUAL_CARD_DISPUTES}
      component={screens.VirtualCardDisputes}
      options={modalScreenConfig({
        title: 'Refunds and chargebacks'
      })}
    />
    <Stack.Screen
      name={routeConst.VIRTUAL_CARD_DELETE_CONFIRMATION}
      component={screens.CardDeleteConfirmation}
    />

    <Stack.Screen
      name={routeConst.VIRTUAL_CARD_APPLICATION}
      component={screens.Application}
      options={modalScreenConfig({ title: 'Claim Your Chipper Card' })}
    />
    <Stack.Screen
      name={routeConst.VIRTUAL_CARD_CONFIRM_ADDRESS}
      component={screens.ConfirmAddress}
    />
    <Stack.Screen
      name={routeConst.VIRTUAL_CARD_CHOICE}
      component={screens.Choice}
      options={stackScreenConfig({
        title: 'Claim Your Chipper Card',
        headerShadowVisible: false
      })}
    />
    <Stack.Screen
      name={routeConst.VIRTUAL_CARD_CREATION_CONFIRMATION}
      component={screens.CreationConfirmation}
      options={() => ({
        headerShown: false
      })}
    />

    <Stack.Screen
      name={routeConst.VIRTUAL_CARD_WITHDRAW_V2}
      component={screens.VirtualCardWithdrawalV2}
      options={({ route }) => {
        const card = selectCardById(route.params.cardId)
        const currencyStr = card?.currency ? ` ${card.currency}` : ''
        return modalScreenConfig({
          title: `Withdrawal from${currencyStr} card
            }`
        })
      }}
    />

    <Stack.Screen
      name={routeConst.VIRTUAL_CARD_DEPOSIT_V2}
      component={screens.VirtualCardDepositV2}
      options={({ route }) => {
        const card = selectCardById(route.params.cardId)
        const currencyStr = card?.currency ? ` ${card.currency}` : ''
        const toActivateStr = route.params.isActivatingCard
          ? ' to activate'
          : ''
        return modalScreenConfig({
          title: `Top up${currencyStr} card${toActivateStr}`
        })
      }}
    />

    <Stack.Screen
      name={routeConst.VIRTUAL_CARD_SCHEDULED_DEPOSITS}
      component={screens.ScheduledDeposits}
      options={stackScreenConfig({
        title: 'Scheduled top-uop'
      })}
    />

    <Stack.Screen
      name={routeConst.VIRTUAL_CARD_LIMITS}
      component={screens.CardLimits}
      options={stackScreenConfig({
        title: 'Card Limits',
        headerShadowVisible: false
      })}
    />

    <Stack.Screen
      name={routeConst.VIRTUAL_CARD_SUBSCRIPTIONS}
      component={screens.VirtualCardSubscriptions}
      options={modalScreenConfig({
        title: 'Subscriptions',
        headerShadowVisible: false
      })}
    />

    <Stack.Screen
      name={routeConst.VIRTUAL_CARD_SUBSCRIPTION_DETAILS}
      component={screens.VirtualCardSubscriptionDetails}
      options={stackScreenConfig({
        title: '',
        headerShadowVisible: false
      })}
    />

    <Stack.Screen
      name={routeConst.VIRTUAL_CARD_DISPUTE_FORM}
      component={screens.VirtualCardDisputeForm}
      options={({ route }) =>
        modalScreenConfig({
          title: (() => {
            switch (route.params?.disputeKind) {
              case VirtualCardDisputeType.REFUND:
                return 'Refunds'
              case VirtualCardDisputeType.CHARGEBACK:
                return 'Chargeback'
              default:
                return 'Refunds and chargebacks'
            }
          })()
        })
      }
    />

    <Stack.Screen
      name={routeConst.VIRTUAL_CARD_SCHEDULED_DEPOSIT_DETAIL}
      component={withModals(screens.ScheduledDepositDetail)}
      options={{
        presentation: 'transparentModal',
        animationEnabled: true,
        headerShown: false
      }}
    />

    {TransactionNavigatorGroup()}
  </Stack.Group>
)

export const VirtualCardNavigator = () => {
  return (
    <Stack.Navigator
      screenOptions={{
        ...defaultHeaderOptions
      }}>
      <Stack.Screen
        name={routeConst.VIRTUAL_CARD_HOME}
        component={screens.HomeV2}
        options={stackScreenConfig({
          headerLeft: () => null,
          headerShown: true,
          cardStyleInterpolator: forFade,
          headerShadowVisible: false
        })}
      />

      <Stack.Screen
        name={routeConst.VIRTUAL_CARD_INFO}
        component={screens.CardInfo}
        options={() =>
          stackScreenConfig({
            title: 'More info',
            headerShadowVisible: false
          })
        }
      />

      <Stack.Screen
        name={routeConst.VIRTUAL_CARD_SPEND_CATEGORY}
        component={screens.VirtualCardSpendCategory}
        options={() =>
          stackScreenConfig({
            title: 'Spending',
            headerShadowVisible: false
          })
        }
      />
    </Stack.Navigator>
  )
}
