import { ConfigFile } from '@rtk-query/codegen-openapi'
import { generateEndpoints } from '@rtk-query/codegen-openapi'
import fs from 'fs'
import _ from 'lodash'
import { transpile as transpilePostmanToOpenapi } from 'postman2openapi'
import { Collection, Item, ItemGroup } from 'postman-collection'
import { hideBin } from 'yargs/helpers'
import yargs from 'yargs/yargs'

/**
 * Generate a filename for the generated files
 * @param name
 * @param suffix
 * @returns
 */
function getGeneratedFilename(name: string, suffix?: string) {
  const parsed = _.kebabCase(name)
  const s = [parsed, suffix].filter(Boolean).join('-')
  return `${s}.generated`
}

/**
 * Fetches the Postman collection from the Postman API
 * @returns
 */
async function getPostmanCollection() {
  console.log('Fetching Postman collection')
  // Our Internal Services Postman collection ID
  const CHIPPER_INTERNAL_SERVICES_COLLECTION_ID =
    '14807556-46040a01-21aa-4d7a-8c67-5dbfc1253436'

  const token = process.env.POSTMAN_TOKEN

  if (!token) {
    console.error(
      'ERROR: No Postman token found in environment variables\nPlease set POSTMAN_TOKEN in your environment variables to continue'
    )
    process.exit(1)
  }

  try {
    const response = await fetch(
      `https://api.getpostman.com/collections/${CHIPPER_INTERNAL_SERVICES_COLLECTION_ID}`,
      {
        headers: {
          'X-API-Key': token
        }
      }
    )

    console.log('Fetched Postman collection')

    return response.json().then(data => data.collection)
  } catch (e) {
    console.error(e)
    process.exit(1)
  }
}

/**
 * Generate a postman collection from an item or item group. Allows us to treat
 * sub folders as their own collection so we can generate OpenAPI specs for them.
 * @param item
 * @param collection
 * @param overrides
 * @returns
 */
const postmanCollectionFromItem = (
  item: Item | ItemGroup<Item>,
  collection?: Collection,
  overrides?: { info: { name: string } }
) => {
  return new Collection({
    info: {
      name: overrides?.info.name ?? item.name,
      version: collection?.version?.toString()
    },
    item: [item]
  })
}

/**
 * Generate OpenAPI spec from a Postman collection in the given directory
 * @param collection
 * @param dirName
 */
const genOpenApi = (collection: object, dirName: string) => {
  const collectionInstance = new Collection(collection)
  dirName = dirName.replace(/\/$/, '')
  const filename = getGeneratedFilename(collectionInstance.name, 'open-api')
  const filePath = `${dirName}/${filename}.json`

  console.log(`Generating OpenAPI spec for ${collectionInstance.name}`)

  const openapi = transpilePostmanToOpenapi(collectionInstance.toJSON())

  // Write the OpenAPI spec to a file
  fs.mkdirSync(dirName, { recursive: true })
  fs.writeFileSync(filePath, JSON.stringify(openapi, null, 2))

  return {
    path: filePath,
    openapi
  }
}

/**
 * Extract items from a collection based on a path.
 *
 * Allows us to starting from the root of the collection, extract items
 * based on it's path in the Postman collection.
 * @param group The Postman folder we are extracting items from as an ItemGroup class from the Postman SDK
 * @param filePath The path to the folder in the postman collection to generate the openapi spec, types and api for
 * @returns
 */
function extractPostmanItems(group: ItemGroup<Item>, filePath: string) {
  /**
   * Helper function to extract items at a given level
   * @param itemOrGroup
   * @param level
   * @returns
   */
  function extractItemsAtLevel(
    itemOrGroup: ItemGroup<Item> | Item,
    level: string
  ) {
    // Validate that the itemOrGroup is a folder (and not an
    // Item (a.k.a a request which is the lowest level in the Postman collection))
    if (!ItemGroup.isItemGroup(itemOrGroup)) {
      console.error(`'${itemOrGroup.name}' is not a folder`)
      process.exit(1)
    }

    let finalItemGroup = itemOrGroup as ItemGroup<Item>

    // Filter for matching items at the level. If the level is "*", return all items
    const result = finalItemGroup.items.filter(
      item => level === '*' || item.name.toLowerCase() === level.toLowerCase(),
      {}
    )

    return result
  }

  const levels = filePath.split('/')
  const [first, ...rest] = levels

  // Extract items at the first level
  let result = extractItemsAtLevel(group, first)

  // Go deeper into the folder structure and extract items at each level
  for (const level of rest) {
    result = result.flatMap(item => extractItemsAtLevel(item, level))
  }

  return result
}

/** A predefined configuration for known folder names such as our Core/v1 folder */
type PredefinedGenerateConfig = {
  /** Where the generated code will be stored. Each location has a default Postman folder it maps to */
  key: string

  /** The path to the folder in the postman collection to generate the openapi spec, types and api for */
  folder: string

  /** The name of the directory to store the openapi spec */
  openapiDirName?: string

  /** The name of the directory to store the types */
  typesDirName?: string
}

const baseFolders = {
  core: 'core/v1',
  qeta: 'core/v2',
  auth: 'auth',
  compliance: 'compliance'
}

const defaultGenerateConfig = {
  openapiDirName: 'backup-open-api'
}

const predefinedConfigs: PredefinedGenerateConfig[] = [
  // All items in the core/v1 folder as their own OpenAPI specs
  {
    folder: `${baseFolders.core}/*`,
    key: 'core'
  },
  // The core/v2 folder as its own OpenAPI spec
  {
    folder: `${baseFolders.qeta}/*`,
    key: 'qeta'
  },
  // The auth folder as its own OpenAPI spec
  {
    folder: `${baseFolders.auth}`,
    key: 'auth'
  },
  // The compliance folder as its own OpenAPI spec
  {
    folder: `${baseFolders.compliance}`,
    key: 'compliance'
  }
]

type OutConfig = {
  key: string
  postmanItems: (Item | ItemGroup<Item>)[]
  openapiDirName: string
}

/**
 * Run the API generation script.
 * @param inputKey
 * @param inputFolder
 * @param outDir
 */
async function run(inputKey?: string, inputFolder?: string) {
  const outDir = 'src/shared/services/api'
  inputKey = inputKey?.trim()
  inputFolder = inputFolder?.trim()

  const collection = new Collection(await getPostmanCollection())

  // The output configurations we will use to generate the API
  const outConfigs: OutConfig[] = []

  // Check if the user provided a key and folder that matches a predefined config
  // or if no key or folder was provided, generate from all the predefined configs
  const matchingPredefinedConfigs = predefinedConfigs.filter(config => {
    const { key } = config
    return key === inputKey?.toLowerCase() || (!inputKey && !inputFolder)
  })

  // If we have matching predefined configs, generate from them
  if (matchingPredefinedConfigs.length) {
    console.log('Generating from pre-defined configs')

    for (const config of matchingPredefinedConfigs) {
      const { key, folder, openapiDirName } = {
        ...defaultGenerateConfig,
        ...config
      }

      const baseFolder = baseFolders[key as keyof typeof baseFolders]
      // Normalize the input folder to remove the base folder in case it was already provided
      inputFolder = inputFolder?.replace(new RegExp(`^${baseFolder}/?`), '')

      // The final folder to generate from
      const finalFolder = inputFolder ? `${baseFolder}/${inputFolder}` : folder

      const postmanItems = extractPostmanItems(collection, finalFolder)
      outConfigs.push({ postmanItems, key, openapiDirName })
    }
  } else {
    // Else generate directly from the specified folder and for the given key
    console.log('Generating directly from the folder')
    if (!inputFolder) {
      console.error('ERROR: No folder provided')
      process.exit(1)
    }

    if (!inputKey) {
      console.error('ERROR: No key provided')
      process.exit(1)
    }

    // Extract the Postman items to generate from the collection
    const postmanItems = extractPostmanItems(collection, inputFolder)

    // Add the output configuration
    outConfigs.push({
      ...defaultGenerateConfig,
      postmanItems,
      key: inputKey
    })
  }

  // Generate the API files
  const promises = outConfigs.flatMap(
    async ({ postmanItems, openapiDirName, key }) => {
      return postmanItems.map(async item => {
        // Convert the item to a collection
        const postmanCollection = postmanCollectionFromItem(item)

        // Generate the OpenAPI spec from the collection
        const { path: openapiPath } = genOpenApi(
          postmanCollection.toJSON(),
          `${outDir}/${key}/${openapiDirName}`
        )

        const camelKey = _.camelCase(key)
        const camelName = _.camelCase(`chipper ${item.name}`)

        // Generate the RTK query api
        await genApi({
          dirName: `${outDir}/${key}/rtk-configs`,
          openApiSchemaFilePath: openapiPath,
          apiExportName: `${camelName}Api`,
          apiOutputDir: `${outDir}/${key}/api`,
          apiEndpointOverrides: API_ENDPOINT_OVERRIDES_MAP[key][item.name],
          baseApiPath: `@shared/services/api/${key}/${camelKey}EmptyApi.ts`,
          baseApiImportName: `${camelKey}EmptyApi`
        })
      })
    }
  )

  try {
    await Promise.all(promises)
  } catch (e) {
    console.error(e)
    process.exit(1)
  }
}

// Build the CLI and run the script
yargs(hideBin(process.argv)) // Removes the first two arguments (i.e node and the script file)
  // Register the `gen-api` command
  .command(
    'gen-api [key] [folder]',
    'Generate an HTTP Client API from the Chipper Postman collection',
    kargs => {
      kargs
        // Register the `key`, `folder` and `dir` arguments
        .positional('key', {
          alias: 'k',
          describe:
            'Where to store the generated API files. e.g core, qeta, auth, compliance',
          type: 'string'
        })
        .positional('folder', {
          alias: 'f',
          describe:
            'The path to the folder in the postman collection to generate the openapi spec, types and api for',
          type: 'string'
        })
    },
    argv => {
      // Run the script
      run(argv.key as string, argv.folder as string)
    }
  )
  .help()
  .demandCommand()
  .recommendCommands()
  .strict()
  .parse()

type EndpointOverrides = any

const API_ENDPOINT_OVERRIDES_MAP: Record<
  string,
  Record<string, EndpointOverrides[]>
> = {
  qeta: {
    sagas: [
      {
        pattern: /sagaStatus/.source,
        type: 'query'
      }
    ]
  },
  core: {},
  auth: {},
  compliance: {}
}

type GenRtkConfigOptions = {
  dirName: string
  openApiSchemaFilePath: string

  apiEndpointOverrides?: EndpointOverrides[]

  baseApiPath: string
  baseApiImportName?: string

  apiExportName: string
  apiOutputDir: string
}

const genApi = async ({
  openApiSchemaFilePath,
  baseApiPath,
  apiExportName,
  apiEndpointOverrides = [],
  baseApiImportName,
  apiOutputDir
}: GenRtkConfigOptions) => {
  const generatedApiOutputDest = `${apiOutputDir}/${apiExportName}.generated.ts`

  const config: ConfigFile = {
    schemaFile: openApiSchemaFilePath,
    exportName: apiExportName,
    outputFile: generatedApiOutputDest,
    endpointOverrides: apiEndpointOverrides,
    apiFile: baseApiPath,
    hooks: true,
    apiImport: baseApiImportName
  }

  // console.log(`Generating API client for ${openApiSchemaFilePath}`, config)
  const result = await generateEndpoints(config)
  console.log(`Generated API client for ${openApiSchemaFilePath}`)

  return {
    path: config.outputFile,
    config,
    result
  }
}
