<manifest
  xmlns:android='http://schemas.android.com/apk/res/android'
  xmlns:tools="http://schemas.android.com/tools"
  android:networkSecurityConfig="@xml/network_security_config"
>

  <uses-permission android:name="android.permission.INTERNET" />
  <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" />
  <uses-permission android:name="android.permission.READ_CONTACTS" />
  <uses-permission android:name="android.permission.READ_PROFILE" />
  <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
  <uses-permission android:name="android.permission.VIBRATE" />
  <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
  <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
  <uses-permission android:name="android.permission.CAMERA" />
  <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
  <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE"/>
  <uses-permission android:name="android.permission.USE_BIOMETRIC" />
  <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" android:maxSdkVersion="28"/>
  <uses-permission android:name="android.permission.USE_FINGERPRINT" />
  <uses-permission android:name="com.google.android.gms.permission.AD_ID" />
  <uses-permission android:name="android.permission.ACCESS_BACKGROUND_LOCATION" />
  <uses-permission android:name="android.permission.ACCESS_FOREGROUND_LOCATION" />
  <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
  <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
  <uses-permission android:name="android.permission.CALL_PHONE" />


  <application
    tools:replace="android:supportsRtl"
    android:supportsRtl="true"
    android:name="com.chippercash.MainApplication"
    android:allowBackup="false"
    android:label="@string/app_name"
    android:icon="@mipmap/ic_launcher"
    android:roundIcon="@mipmap/ic_launcher_round"
    xmlns:tools="http://schemas.android.com/tools"
    android:theme="@style/MainAppTheme"
    android:largeHeap="true"
    android:usesCleartextTraffic="true">

    <provider
      android:name="androidx.core.content.FileProvider"
      android:authorities="com.chippercash.IntercomFileProvider"
      android:exported="false"
      android:grantUriPermissions="true"
      tools:replace="android:authorities">

      <meta-data
        android:name="android.support.FILE_PROVIDER_PATHS"
        android:resource="@xml/intercom_file_paths"
        tools:replace="android:resource" />
    </provider>

    <service
      android:name=".MainMessagingService"
      android:enabled="true"
      android:exported="true">
      <intent-filter>
        <action android:name="com.google.firebase.MESSAGING_EVENT" />
      </intent-filter>
    </service>

    <receiver
      android:name="com.intercom.reactnative.RNIntercomPushBroadcastReceiver"
      tools:replace="android:exported"
      android:exported="true" />

    <meta-data android:name="com.bugsnag.android.REDACTED_KEYS"
      android:value="Authorization,advertising-id,device-notification-token" />

    <meta-data
      android:name="com.google.android.gms.ads.AD_MANAGER_APP"
      android:value="true" />

    <meta-data android:name="com.facebook.sdk.ApplicationId" android:value="@string/facebook_app_id" />

    <meta-data
      android:name="com.bugsnag.android.API_KEY"
      android:value="b854077fa52e6084527fe2ea9bc95c8f" />
    <meta-data
      tools:replace="android:value"
      android:name="com.google.mlkit.vision.DEPENDENCIES"
      android:value="face,ocr,barcode"
    />


    <activity
      android:name="net.openid.appauth.RedirectUriReceiverActivity"
      android:exported="true"
      tools:node="replace">
      <intent-filter android:autoVerify="true">
        <action android:name="android.intent.action.VIEW" />
        <category android:name="android.intent.category.DEFAULT" />
        <category android:name="android.intent.category.BROWSABLE" />
        <data android:scheme="https" android:host="iwademfbank.app.link" />
        <data android:scheme="https" android:host="iwademfbank-alternate.app.link" />
      </intent-filter>
    </activity>

    <activity
      android:name="com.chippercash.MainActivity"
      android:label="@string/app_name"
      android:configChanges="keyboard|keyboardHidden|orientation|screenLayout|screenSize|smallestScreenSize|uiMode"
      android:screenOrientation="portrait"
      android:windowSoftInputMode="adjustPan"
      android:launchMode="singleTask"
      android:theme="@style/AppTheme"
      android:exported="true"
      android:supportsRtl="true">
      <intent-filter>
        <action android:name="android.intent.action.MAIN" />
        <category android:name="android.intent.category.LAUNCHER" />
      </intent-filter>

      <!-- Branch URI Scheme -->
      <intent-filter>
        <data android:scheme="chippercash" android:host="open" />
        <action android:name="android.intent.action.VIEW" />
        <category android:name="android.intent.category.DEFAULT" />
        <category android:name="android.intent.category.BROWSABLE" />
      </intent-filter>


      <!-- Branch App Links (optional) -->
      <intent-filter android:autoVerify="true">
        <action android:name="android.intent.action.VIEW" />
        <category android:name="android.intent.category.DEFAULT" />
        <category android:name="android.intent.category.BROWSABLE" />
        <data android:scheme="https" android:host="chippercash.app.link" />
        <data android:scheme="https" android:host="chippercash-alternate.app.link" />
      </intent-filter>


      <intent-filter android:autoVerify="true">
        <action android:name="android.intent.action.VIEW" />
        <category android:name="android.intent.category.DEFAULT" />
        <category android:name="android.intent.category.BROWSABLE" />
        <data android:scheme="https" android:host="pay.chippercash.com" android:path="/link/.*" />
      </intent-filter>
    </activity>


    <meta-data
      android:name="com.google.firebase.messaging.default_notification_icon"
      android:resource="@drawable/ic_stat_ic_notification_small" />
    <!-- Set color used with incoming notification messages. This is used when no color is set for
    the incoming
             notification message. See README(https://goo.gl/6BKBk7) for more. -->

    <!-- Branch init -->
    <meta-data android:name="io.branch.sdk.BranchKey"
      android:value="key_live_amMdtwnuP3KvTcmLlROJwckbAvnYi2we" />
    <meta-data android:name="io.branch.sdk.BranchKey.test"
      android:value="key_test_ooUmvzcCM2QBKhoQmQXeTfcdBzd7c6Br" />

    <!-- Branch testing (TestMode "true" to simulate fresh installs on dev environment) -->
    <meta-data android:name="io.branch.sdk.TestMode" android:value="false" />


    <activity android:name="com.facebook.react.devsupport.DevSettingsActivity"
      android:exported="false" />

    <!--smileid
    consent activity - used for bvn consent-->
    <activity
      android:name="com.smileidentity.libsmileid.core.consent.ConsentActivity"
      android:exported="false"
      android:theme="@style/AppTheme.NoActionBar" />

    <activity
      android:name="com.smileidentity.libsmileid.core.consent.bvn.ui.activities.ConsentBVNNumberActivity"
      android:exported="false"
      android:theme="@style/AppTheme.NoActionBar" />

    <activity
      android:name="com.smileidentity.libsmileid.core.consent.bvn.ui.activities.ConsentBVNContactMethodActivity"
      android:exported="false"
      android:theme="@style/AppTheme.NoActionBar" />

    <activity
      android:name="com.smileidentity.libsmileid.core.consent.bvn.ui.activities.ConsentOTPVerificationActivity"
      android:exported="false"
      android:theme="@style/AppTheme.NoActionBar" />

    <!-- plaid link oauth redirect activity -->
    <activity android:label="LinkRedirectActivity"
      android:name="com.plaid.internal.LinkRedirectActivity" android:theme="@style/Theme.AppCompat"
      tools:replace="android:theme"></activity>

    <provider android:authorities="com.facebook.app.FacebookContentProvider950169235180173"
      android:name="com.facebook.FacebookContentProvider"
      android:exported="true" />

  </application>

</manifest>
