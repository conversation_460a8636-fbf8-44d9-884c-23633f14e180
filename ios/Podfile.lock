PODS:
  - amplitude-react-native (1.4.11):
    - React-Core
  - AppAuth (1.7.6):
    - AppAuth/Core (= 1.7.6)
    - AppAuth/ExternalUserAgent (= 1.7.6)
  - AppAuth/Core (1.7.6)
  - AppAuth/ExternalUserAgent (1.7.6):
    - AppAuth/Core
  - boost (1.84.0)
  - BranchSDK (2.1.0)
  - BugsnagReactNative (8.2.0):
    - DoubleConversion
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.01.01.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-ImageManager
    - React-NativeModulesApple
    - React-RCTFabric
    - React-rendererdebug
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - Yoga
  - BVLinearGradient (2.8.3):
    - React-Core
  - DoubleConversion (1.1.6)
  - experiment-react-native-client (1.5.1):
    - React-Core
  - FBLazyVector (0.76.7)
  - FBSDKCoreKit (8.2.0):
    - FBSDKCoreKit/Basics (= 8.2.0)
    - FBSDKCoreKit/Core (= 8.2.0)
  - FBSDKCoreKit/Basics (8.2.0)
  - FBSDKCoreKit/Core (8.2.0):
    - FBSDKCoreKit/Basics
  - FBSDKLoginKit (8.2.0):
    - FBSDKLoginKit/Login (= 8.2.0)
  - FBSDKLoginKit/Login (8.2.0):
    - FBSDKCoreKit (~> 8.2.0)
  - FBSDKShareKit (8.2.0):
    - FBSDKShareKit/Share (= 8.2.0)
  - FBSDKShareKit/Share (8.2.0):
    - FBSDKCoreKit (~> 8.2.0)
  - Firebase/CoreOnly (10.29.0):
    - FirebaseCore (= 10.29.0)
  - Firebase/Messaging (10.29.0):
    - Firebase/CoreOnly
    - FirebaseMessaging (~> 10.29.0)
  - Firebase/Performance (10.29.0):
    - Firebase/CoreOnly
    - FirebasePerformance (~> 10.29.0)
  - Firebase/RemoteConfig (10.29.0):
    - Firebase/CoreOnly
    - FirebaseRemoteConfig (~> 10.29.0)
  - Firebase/Storage (10.29.0):
    - Firebase/CoreOnly
    - FirebaseStorage (~> 10.29.0)
  - FirebaseABTesting (10.29.0):
    - FirebaseCore (~> 10.0)
  - FirebaseAppCheckInterop (10.29.0)
  - FirebaseAuthInterop (10.29.0)
  - FirebaseCore (10.29.0):
    - FirebaseCoreInternal (~> 10.0)
    - GoogleUtilities/Environment (~> 7.12)
    - GoogleUtilities/Logger (~> 7.12)
  - FirebaseCoreExtension (10.29.0):
    - FirebaseCore (~> 10.0)
  - FirebaseCoreInternal (10.29.0):
    - "GoogleUtilities/NSData+zlib (~> 7.8)"
  - FirebaseInstallations (10.29.0):
    - FirebaseCore (~> 10.0)
    - GoogleUtilities/Environment (~> 7.8)
    - GoogleUtilities/UserDefaults (~> 7.8)
    - PromisesObjC (~> 2.1)
  - FirebaseMessaging (10.29.0):
    - FirebaseCore (~> 10.0)
    - FirebaseInstallations (~> 10.0)
    - GoogleDataTransport (~> 9.3)
    - GoogleUtilities/AppDelegateSwizzler (~> 7.8)
    - GoogleUtilities/Environment (~> 7.8)
    - GoogleUtilities/Reachability (~> 7.8)
    - GoogleUtilities/UserDefaults (~> 7.8)
    - nanopb (< 2.30911.0, >= 2.30908.0)
  - FirebasePerformance (10.29.0):
    - FirebaseCore (~> 10.5)
    - FirebaseInstallations (~> 10.0)
    - FirebaseRemoteConfig (~> 10.0)
    - FirebaseSessions (~> 10.5)
    - GoogleDataTransport (~> 9.2)
    - GoogleUtilities/Environment (~> 7.13)
    - GoogleUtilities/ISASwizzler (~> 7.13)
    - GoogleUtilities/MethodSwizzler (~> 7.13)
    - GoogleUtilities/UserDefaults (~> 7.13)
    - nanopb (< 2.30911.0, >= 2.30908.0)
  - FirebaseRemoteConfig (10.29.0):
    - FirebaseABTesting (~> 10.0)
    - FirebaseCore (~> 10.0)
    - FirebaseInstallations (~> 10.0)
    - FirebaseRemoteConfigInterop (~> 10.23)
    - FirebaseSharedSwift (~> 10.0)
    - GoogleUtilities/Environment (~> 7.8)
    - "GoogleUtilities/NSData+zlib (~> 7.8)"
  - FirebaseRemoteConfigInterop (10.29.0)
  - FirebaseSessions (10.29.0):
    - FirebaseCore (~> 10.5)
    - FirebaseCoreExtension (~> 10.0)
    - FirebaseInstallations (~> 10.0)
    - GoogleDataTransport (~> 9.2)
    - GoogleUtilities/Environment (~> 7.13)
    - GoogleUtilities/UserDefaults (~> 7.13)
    - nanopb (< 2.30911.0, >= 2.30908.0)
    - PromisesSwift (~> 2.1)
  - FirebaseSharedSwift (10.29.0)
  - FirebaseStorage (10.29.0):
    - FirebaseAppCheckInterop (~> 10.0)
    - FirebaseAuthInterop (~> 10.25)
    - FirebaseCore (~> 10.0)
    - FirebaseCoreExtension (~> 10.0)
    - GoogleUtilities/Environment (~> 7.12)
    - GTMSessionFetcher/Core (< 4.0, >= 2.1)
  - fmt (9.1.0)
  - glog (0.3.5)
  - Google-Mobile-Ads-SDK (12.0.0):
    - GoogleUserMessagingPlatform (>= 1.1)
  - GoogleDataTransport (9.4.1):
    - GoogleUtilities/Environment (~> 7.7)
    - nanopb (< 2.30911.0, >= 2.30908.0)
    - PromisesObjC (< 3.0, >= 1.2)
  - GoogleMLKit/FaceDetection (6.0.0):
    - GoogleMLKit/MLKitCore
    - MLKitFaceDetection (~> 5.0.0)
  - GoogleMLKit/MLKitCore (6.0.0):
    - MLKitCommon (~> 11.0.0)
  - GoogleToolboxForMac/Defines (4.2.1)
  - GoogleToolboxForMac/Logger (4.2.1):
    - GoogleToolboxForMac/Defines (= 4.2.1)
  - "GoogleToolboxForMac/NSData+zlib (4.2.1)":
    - GoogleToolboxForMac/Defines (= 4.2.1)
  - GoogleUserMessagingPlatform (2.7.0)
  - GoogleUtilities/AppDelegateSwizzler (7.13.3):
    - GoogleUtilities/Environment
    - GoogleUtilities/Logger
    - GoogleUtilities/Network
    - GoogleUtilities/Privacy
  - GoogleUtilities/Environment (7.13.3):
    - GoogleUtilities/Privacy
    - PromisesObjC (< 3.0, >= 1.2)
  - GoogleUtilities/ISASwizzler (7.13.3):
    - GoogleUtilities/Privacy
  - GoogleUtilities/Logger (7.13.3):
    - GoogleUtilities/Environment
    - GoogleUtilities/Privacy
  - GoogleUtilities/MethodSwizzler (7.13.3):
    - GoogleUtilities/Logger
    - GoogleUtilities/Privacy
  - GoogleUtilities/Network (7.13.3):
    - GoogleUtilities/Logger
    - "GoogleUtilities/NSData+zlib"
    - GoogleUtilities/Privacy
    - GoogleUtilities/Reachability
  - "GoogleUtilities/NSData+zlib (7.13.3)":
    - GoogleUtilities/Privacy
  - GoogleUtilities/Privacy (7.13.3)
  - GoogleUtilities/Reachability (7.13.3):
    - GoogleUtilities/Logger
    - GoogleUtilities/Privacy
  - GoogleUtilities/UserDefaults (7.13.3):
    - GoogleUtilities/Logger
    - GoogleUtilities/Privacy
  - GoogleUtilitiesComponents (1.1.0):
    - GoogleUtilities/Logger
  - GTMSessionFetcher/Core (3.5.0)
  - hermes-engine (0.76.7):
    - hermes-engine/Pre-built (= 0.76.7)
  - hermes-engine/Pre-built (0.76.7)
  - Intercom (18.6.4)
  - intercom-react-native (8.3.0):
    - Intercom (~> 18.6.1)
    - React-Core
  - libwebp (1.5.0):
    - libwebp/demux (= 1.5.0)
    - libwebp/mux (= 1.5.0)
    - libwebp/sharpyuv (= 1.5.0)
    - libwebp/webp (= 1.5.0)
  - libwebp/demux (1.5.0):
    - libwebp/webp
  - libwebp/mux (1.5.0):
    - libwebp/demux
  - libwebp/sharpyuv (1.5.0)
  - libwebp/webp (1.5.0):
    - libwebp/sharpyuv
  - lottie-ios (4.5.0)
  - lottie-react-native (7.2.2):
    - DoubleConversion
    - glog
    - hermes-engine
    - lottie-ios (= 4.5.0)
    - RCT-Folly (= 2024.01.01.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-ImageManager
    - React-NativeModulesApple
    - React-RCTFabric
    - React-rendererdebug
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - Yoga
  - MLImage (1.0.0-beta5)
  - MLKitCommon (11.0.0):
    - GoogleDataTransport (< 10.0, >= 9.4.1)
    - GoogleToolboxForMac/Logger (< 5.0, >= 4.2.1)
    - "GoogleToolboxForMac/NSData+zlib (< 5.0, >= 4.2.1)"
    - GoogleUtilities/UserDefaults (< 8.0, >= 7.13.0)
    - GoogleUtilitiesComponents (~> 1.0)
    - GTMSessionFetcher/Core (< 4.0, >= 3.3.2)
  - MLKitFaceDetection (5.0.0):
    - MLKitCommon (~> 11.0)
    - MLKitVision (~> 7.0)
  - MLKitVision (7.0.0):
    - GoogleToolboxForMac/Logger (< 5.0, >= 4.2.1)
    - "GoogleToolboxForMac/NSData+zlib (< 5.0, >= 4.2.1)"
    - GTMSessionFetcher/Core (< 4.0, >= 3.3.2)
    - MLImage (= 1.0.0-beta5)
    - MLKitCommon (~> 11.0)
  - nanopb (2.30910.0):
    - nanopb/decode (= 2.30910.0)
    - nanopb/encode (= 2.30910.0)
  - nanopb/decode (2.30910.0)
  - nanopb/encode (2.30910.0)
  - OkHi (1.9.35)
  - Onfido (32.2.2)
  - onfido-react-native-sdk (14.3.0):
    - Onfido (~> 32.2.0)
    - React
  - Plaid (6.0.4)
  - PromisesObjC (2.4.0)
  - PromisesSwift (2.4.0):
    - PromisesObjC (= 2.4.0)
  - RCT-Folly (2024.01.01.00):
    - boost
    - DoubleConversion
    - fmt (= 9.1.0)
    - glog
    - RCT-Folly/Default (= 2024.01.01.00)
  - RCT-Folly/Default (2024.01.01.00):
    - boost
    - DoubleConversion
    - fmt (= 9.1.0)
    - glog
  - RCT-Folly/Fabric (2024.01.01.00):
    - boost
    - DoubleConversion
    - fmt (= 9.1.0)
    - glog
  - RCTDeprecation (0.76.7)
  - RCTRequired (0.76.7)
  - RCTTypeSafety (0.76.7):
    - FBLazyVector (= 0.76.7)
    - RCTRequired (= 0.76.7)
    - React-Core (= 0.76.7)
  - React (0.76.7):
    - React-Core (= 0.76.7)
    - React-Core/DevSupport (= 0.76.7)
    - React-Core/RCTWebSocket (= 0.76.7)
    - React-RCTActionSheet (= 0.76.7)
    - React-RCTAnimation (= 0.76.7)
    - React-RCTBlob (= 0.76.7)
    - React-RCTImage (= 0.76.7)
    - React-RCTLinking (= 0.76.7)
    - React-RCTNetwork (= 0.76.7)
    - React-RCTSettings (= 0.76.7)
    - React-RCTText (= 0.76.7)
    - React-RCTVibration (= 0.76.7)
  - React-callinvoker (0.76.7)
  - React-Codegen (0.1.0)
  - React-Core (0.76.7):
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.01.01.00)
    - RCTDeprecation
    - React-Core/Default (= 0.76.7)
    - React-cxxreact
    - React-featureflags
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket (= 0.7.1)
    - Yoga
  - React-Core/CoreModulesHeaders (0.76.7):
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.01.01.00)
    - RCTDeprecation
    - React-Core/Default
    - React-cxxreact
    - React-featureflags
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket (= 0.7.1)
    - Yoga
  - React-Core/Default (0.76.7):
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.01.01.00)
    - RCTDeprecation
    - React-cxxreact
    - React-featureflags
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket (= 0.7.1)
    - Yoga
  - React-Core/DevSupport (0.76.7):
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.01.01.00)
    - RCTDeprecation
    - React-Core/Default (= 0.76.7)
    - React-Core/RCTWebSocket (= 0.76.7)
    - React-cxxreact
    - React-featureflags
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket (= 0.7.1)
    - Yoga
  - React-Core/RCTActionSheetHeaders (0.76.7):
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.01.01.00)
    - RCTDeprecation
    - React-Core/Default
    - React-cxxreact
    - React-featureflags
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket (= 0.7.1)
    - Yoga
  - React-Core/RCTAnimationHeaders (0.76.7):
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.01.01.00)
    - RCTDeprecation
    - React-Core/Default
    - React-cxxreact
    - React-featureflags
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket (= 0.7.1)
    - Yoga
  - React-Core/RCTBlobHeaders (0.76.7):
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.01.01.00)
    - RCTDeprecation
    - React-Core/Default
    - React-cxxreact
    - React-featureflags
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket (= 0.7.1)
    - Yoga
  - React-Core/RCTImageHeaders (0.76.7):
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.01.01.00)
    - RCTDeprecation
    - React-Core/Default
    - React-cxxreact
    - React-featureflags
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket (= 0.7.1)
    - Yoga
  - React-Core/RCTLinkingHeaders (0.76.7):
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.01.01.00)
    - RCTDeprecation
    - React-Core/Default
    - React-cxxreact
    - React-featureflags
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket (= 0.7.1)
    - Yoga
  - React-Core/RCTNetworkHeaders (0.76.7):
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.01.01.00)
    - RCTDeprecation
    - React-Core/Default
    - React-cxxreact
    - React-featureflags
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket (= 0.7.1)
    - Yoga
  - React-Core/RCTSettingsHeaders (0.76.7):
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.01.01.00)
    - RCTDeprecation
    - React-Core/Default
    - React-cxxreact
    - React-featureflags
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket (= 0.7.1)
    - Yoga
  - React-Core/RCTTextHeaders (0.76.7):
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.01.01.00)
    - RCTDeprecation
    - React-Core/Default
    - React-cxxreact
    - React-featureflags
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket (= 0.7.1)
    - Yoga
  - React-Core/RCTVibrationHeaders (0.76.7):
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.01.01.00)
    - RCTDeprecation
    - React-Core/Default
    - React-cxxreact
    - React-featureflags
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket (= 0.7.1)
    - Yoga
  - React-Core/RCTWebSocket (0.76.7):
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.01.01.00)
    - RCTDeprecation
    - React-Core/Default (= 0.76.7)
    - React-cxxreact
    - React-featureflags
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket (= 0.7.1)
    - Yoga
  - React-CoreModules (0.76.7):
    - DoubleConversion
    - fmt (= 9.1.0)
    - RCT-Folly (= 2024.01.01.00)
    - RCTTypeSafety (= 0.76.7)
    - React-Core/CoreModulesHeaders (= 0.76.7)
    - React-jsi (= 0.76.7)
    - React-jsinspector
    - React-NativeModulesApple
    - React-RCTBlob
    - React-RCTImage (= 0.76.7)
    - ReactCodegen
    - ReactCommon
    - SocketRocket (= 0.7.1)
  - React-cxxreact (0.76.7):
    - boost
    - DoubleConversion
    - fmt (= 9.1.0)
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.01.01.00)
    - React-callinvoker (= 0.76.7)
    - React-debug (= 0.76.7)
    - React-jsi (= 0.76.7)
    - React-jsinspector
    - React-logger (= 0.76.7)
    - React-perflogger (= 0.76.7)
    - React-runtimeexecutor (= 0.76.7)
    - React-timing (= 0.76.7)
  - React-debug (0.76.7)
  - React-defaultsnativemodule (0.76.7):
    - DoubleConversion
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.01.01.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-debug
    - React-domnativemodule
    - React-Fabric
    - React-featureflags
    - React-featureflagsnativemodule
    - React-graphics
    - React-idlecallbacksnativemodule
    - React-ImageManager
    - React-microtasksnativemodule
    - React-NativeModulesApple
    - React-RCTFabric
    - React-rendererdebug
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - Yoga
  - React-domnativemodule (0.76.7):
    - DoubleConversion
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.01.01.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-debug
    - React-Fabric
    - React-FabricComponents
    - React-featureflags
    - React-graphics
    - React-ImageManager
    - React-NativeModulesApple
    - React-RCTFabric
    - React-rendererdebug
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - Yoga
  - React-Fabric (0.76.7):
    - DoubleConversion
    - fmt (= 9.1.0)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.01.01.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-Fabric/animations (= 0.76.7)
    - React-Fabric/attributedstring (= 0.76.7)
    - React-Fabric/componentregistry (= 0.76.7)
    - React-Fabric/componentregistrynative (= 0.76.7)
    - React-Fabric/components (= 0.76.7)
    - React-Fabric/core (= 0.76.7)
    - React-Fabric/dom (= 0.76.7)
    - React-Fabric/imagemanager (= 0.76.7)
    - React-Fabric/leakchecker (= 0.76.7)
    - React-Fabric/mounting (= 0.76.7)
    - React-Fabric/observers (= 0.76.7)
    - React-Fabric/scheduler (= 0.76.7)
    - React-Fabric/telemetry (= 0.76.7)
    - React-Fabric/templateprocessor (= 0.76.7)
    - React-Fabric/uimanager (= 0.76.7)
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/animations (0.76.7):
    - DoubleConversion
    - fmt (= 9.1.0)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.01.01.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/attributedstring (0.76.7):
    - DoubleConversion
    - fmt (= 9.1.0)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.01.01.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/componentregistry (0.76.7):
    - DoubleConversion
    - fmt (= 9.1.0)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.01.01.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/componentregistrynative (0.76.7):
    - DoubleConversion
    - fmt (= 9.1.0)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.01.01.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/components (0.76.7):
    - DoubleConversion
    - fmt (= 9.1.0)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.01.01.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-Fabric/components/legacyviewmanagerinterop (= 0.76.7)
    - React-Fabric/components/root (= 0.76.7)
    - React-Fabric/components/view (= 0.76.7)
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/components/legacyviewmanagerinterop (0.76.7):
    - DoubleConversion
    - fmt (= 9.1.0)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.01.01.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/components/root (0.76.7):
    - DoubleConversion
    - fmt (= 9.1.0)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.01.01.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/components/view (0.76.7):
    - DoubleConversion
    - fmt (= 9.1.0)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.01.01.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
    - Yoga
  - React-Fabric/core (0.76.7):
    - DoubleConversion
    - fmt (= 9.1.0)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.01.01.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/dom (0.76.7):
    - DoubleConversion
    - fmt (= 9.1.0)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.01.01.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/imagemanager (0.76.7):
    - DoubleConversion
    - fmt (= 9.1.0)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.01.01.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/leakchecker (0.76.7):
    - DoubleConversion
    - fmt (= 9.1.0)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.01.01.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/mounting (0.76.7):
    - DoubleConversion
    - fmt (= 9.1.0)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.01.01.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/observers (0.76.7):
    - DoubleConversion
    - fmt (= 9.1.0)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.01.01.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-Fabric/observers/events (= 0.76.7)
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/observers/events (0.76.7):
    - DoubleConversion
    - fmt (= 9.1.0)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.01.01.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/scheduler (0.76.7):
    - DoubleConversion
    - fmt (= 9.1.0)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.01.01.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-Fabric/observers/events
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-performancetimeline
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/telemetry (0.76.7):
    - DoubleConversion
    - fmt (= 9.1.0)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.01.01.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/templateprocessor (0.76.7):
    - DoubleConversion
    - fmt (= 9.1.0)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.01.01.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/uimanager (0.76.7):
    - DoubleConversion
    - fmt (= 9.1.0)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.01.01.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-Fabric/uimanager/consistency (= 0.76.7)
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererconsistency
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/uimanager/consistency (0.76.7):
    - DoubleConversion
    - fmt (= 9.1.0)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.01.01.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererconsistency
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-FabricComponents (0.76.7):
    - DoubleConversion
    - fmt (= 9.1.0)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.01.01.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-Fabric
    - React-FabricComponents/components (= 0.76.7)
    - React-FabricComponents/textlayoutmanager (= 0.76.7)
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/core
    - Yoga
  - React-FabricComponents/components (0.76.7):
    - DoubleConversion
    - fmt (= 9.1.0)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.01.01.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-Fabric
    - React-FabricComponents/components/inputaccessory (= 0.76.7)
    - React-FabricComponents/components/iostextinput (= 0.76.7)
    - React-FabricComponents/components/modal (= 0.76.7)
    - React-FabricComponents/components/rncore (= 0.76.7)
    - React-FabricComponents/components/safeareaview (= 0.76.7)
    - React-FabricComponents/components/scrollview (= 0.76.7)
    - React-FabricComponents/components/text (= 0.76.7)
    - React-FabricComponents/components/textinput (= 0.76.7)
    - React-FabricComponents/components/unimplementedview (= 0.76.7)
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/core
    - Yoga
  - React-FabricComponents/components/inputaccessory (0.76.7):
    - DoubleConversion
    - fmt (= 9.1.0)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.01.01.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/core
    - Yoga
  - React-FabricComponents/components/iostextinput (0.76.7):
    - DoubleConversion
    - fmt (= 9.1.0)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.01.01.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/core
    - Yoga
  - React-FabricComponents/components/modal (0.76.7):
    - DoubleConversion
    - fmt (= 9.1.0)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.01.01.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/core
    - Yoga
  - React-FabricComponents/components/rncore (0.76.7):
    - DoubleConversion
    - fmt (= 9.1.0)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.01.01.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/core
    - Yoga
  - React-FabricComponents/components/safeareaview (0.76.7):
    - DoubleConversion
    - fmt (= 9.1.0)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.01.01.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/core
    - Yoga
  - React-FabricComponents/components/scrollview (0.76.7):
    - DoubleConversion
    - fmt (= 9.1.0)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.01.01.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/core
    - Yoga
  - React-FabricComponents/components/text (0.76.7):
    - DoubleConversion
    - fmt (= 9.1.0)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.01.01.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/core
    - Yoga
  - React-FabricComponents/components/textinput (0.76.7):
    - DoubleConversion
    - fmt (= 9.1.0)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.01.01.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/core
    - Yoga
  - React-FabricComponents/components/unimplementedview (0.76.7):
    - DoubleConversion
    - fmt (= 9.1.0)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.01.01.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/core
    - Yoga
  - React-FabricComponents/textlayoutmanager (0.76.7):
    - DoubleConversion
    - fmt (= 9.1.0)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.01.01.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/core
    - Yoga
  - React-FabricImage (0.76.7):
    - DoubleConversion
    - fmt (= 9.1.0)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.01.01.00)
    - RCTRequired (= 0.76.7)
    - RCTTypeSafety (= 0.76.7)
    - React-Fabric
    - React-graphics
    - React-ImageManager
    - React-jsi
    - React-jsiexecutor (= 0.76.7)
    - React-logger
    - React-rendererdebug
    - React-utils
    - ReactCommon
    - Yoga
  - React-featureflags (0.76.7)
  - React-featureflagsnativemodule (0.76.7):
    - DoubleConversion
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.01.01.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-ImageManager
    - React-NativeModulesApple
    - React-RCTFabric
    - React-rendererdebug
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - Yoga
  - React-graphics (0.76.7):
    - DoubleConversion
    - fmt (= 9.1.0)
    - glog
    - RCT-Folly/Fabric (= 2024.01.01.00)
    - React-jsi
    - React-jsiexecutor
    - React-utils
  - React-hermes (0.76.7):
    - DoubleConversion
    - fmt (= 9.1.0)
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.01.01.00)
    - React-cxxreact (= 0.76.7)
    - React-jsi
    - React-jsiexecutor (= 0.76.7)
    - React-jsinspector
    - React-perflogger (= 0.76.7)
    - React-runtimeexecutor
  - React-idlecallbacksnativemodule (0.76.7):
    - DoubleConversion
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.01.01.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-ImageManager
    - React-NativeModulesApple
    - React-RCTFabric
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - Yoga
  - React-ImageManager (0.76.7):
    - glog
    - RCT-Folly/Fabric
    - React-Core/Default
    - React-debug
    - React-Fabric
    - React-graphics
    - React-rendererdebug
    - React-utils
  - React-jserrorhandler (0.76.7):
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.01.01.00)
    - React-cxxreact
    - React-debug
    - React-jsi
  - React-jsi (0.76.7):
    - boost
    - DoubleConversion
    - fmt (= 9.1.0)
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.01.01.00)
  - React-jsiexecutor (0.76.7):
    - DoubleConversion
    - fmt (= 9.1.0)
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.01.01.00)
    - React-cxxreact (= 0.76.7)
    - React-jsi (= 0.76.7)
    - React-jsinspector
    - React-perflogger (= 0.76.7)
  - React-jsinspector (0.76.7):
    - DoubleConversion
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.01.01.00)
    - React-featureflags
    - React-jsi
    - React-perflogger (= 0.76.7)
    - React-runtimeexecutor (= 0.76.7)
  - React-jsitracing (0.76.7):
    - React-jsi
  - React-logger (0.76.7):
    - glog
  - React-Mapbuffer (0.76.7):
    - glog
    - React-debug
  - React-microtasksnativemodule (0.76.7):
    - DoubleConversion
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.01.01.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-ImageManager
    - React-NativeModulesApple
    - React-RCTFabric
    - React-rendererdebug
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - Yoga
  - react-native-ad-id (0.2.3):
    - DoubleConversion
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.01.01.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-ImageManager
    - React-NativeModulesApple
    - React-RCTFabric
    - React-rendererdebug
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - Yoga
  - react-native-app-auth (8.0.2):
    - AppAuth (>= 1.7.6)
    - React-Core
  - react-native-background-timer (2.4.1):
    - React-Core
  - react-native-blob-util (0.16.2):
    - React-Core
  - react-native-blur (4.4.1):
    - DoubleConversion
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.01.01.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-ImageManager
    - React-NativeModulesApple
    - React-RCTFabric
    - React-rendererdebug
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - Yoga
  - react-native-branch (5.8.0):
    - BranchSDK (= 2.1.0)
    - React-Core
  - react-native-carrier-info (1.1.2):
    - React
  - react-native-contacts (8.0.3):
    - DoubleConversion
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.01.01.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Codegen
    - React-Core
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-ImageManager
    - React-NativeModulesApple
    - React-RCTFabric
    - React-rendererdebug
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - Yoga
  - react-native-document-picker (3.5.4):
    - React
  - react-native-encrypted-storage (4.0.3):
    - React-Core
  - react-native-fbsdk (3.0.0):
    - React
    - react-native-fbsdk/Core (= 3.0.0)
    - react-native-fbsdk/Login (= 3.0.0)
    - react-native-fbsdk/Share (= 3.0.0)
  - react-native-fbsdk/Core (3.0.0):
    - FBSDKCoreKit (~> 8.1)
    - React
  - react-native-fbsdk/Login (3.0.0):
    - FBSDKLoginKit (~> 8.1)
    - React
  - react-native-fbsdk/Share (3.0.0):
    - FBSDKShareKit (~> 8.1)
    - React
  - react-native-fingerprint-scanner (6.0.0):
    - React
  - react-native-geolocation-service (5.3.1):
    - React
  - react-native-get-random-values (1.8.0):
    - React-Core
  - react-native-image-resizer (3.0.11):
    - DoubleConversion
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.01.01.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Codegen
    - React-Core
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-ImageManager
    - React-NativeModulesApple
    - React-RCTFabric
    - React-rendererdebug
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - Yoga
  - react-native-mmkv (3.2.0):
    - DoubleConversion
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.01.01.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-ImageManager
    - React-NativeModulesApple
    - React-RCTFabric
    - React-rendererdebug
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - Yoga
  - react-native-netinfo (11.4.1):
    - React-Core
  - react-native-okhi (1.2.15):
    - OkHi (= 1.9.35)
    - RCT-Folly
    - RCTRequired
    - RCTTypeSafety
    - React-Codegen
    - React-Core
    - ReactCommon/turbomodule/core
  - react-native-pager-view (6.7.0):
    - DoubleConversion
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.01.01.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-ImageManager
    - React-NativeModulesApple
    - React-RCTFabric
    - React-rendererdebug
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - Yoga
  - react-native-pdf (6.7.7):
    - DoubleConversion
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.01.01.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-ImageManager
    - React-NativeModulesApple
    - React-RCTFabric
    - React-rendererdebug
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - Yoga
  - react-native-plaid-link-sdk (12.0.3):
    - DoubleConversion
    - glog
    - hermes-engine
    - Plaid (~> 6.0.4)
    - RCT-Folly (= 2024.01.01.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-ImageManager
    - React-NativeModulesApple
    - React-RCTFabric
    - React-rendererdebug
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - Yoga
  - react-native-safe-area-context (5.2.0):
    - DoubleConversion
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.01.01.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-ImageManager
    - react-native-safe-area-context/common (= 5.2.0)
    - react-native-safe-area-context/fabric (= 5.2.0)
    - React-NativeModulesApple
    - React-RCTFabric
    - React-rendererdebug
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - Yoga
  - react-native-safe-area-context/common (5.2.0):
    - DoubleConversion
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.01.01.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-ImageManager
    - React-NativeModulesApple
    - React-RCTFabric
    - React-rendererdebug
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - Yoga
  - react-native-safe-area-context/fabric (5.2.0):
    - DoubleConversion
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.01.01.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-ImageManager
    - react-native-safe-area-context/common
    - React-NativeModulesApple
    - React-RCTFabric
    - React-rendererdebug
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - Yoga
  - react-native-segmented-control (2.2.2):
    - React-Core
  - react-native-skia (1.11.8):
    - DoubleConversion
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.01.01.00)
    - RCTRequired
    - RCTTypeSafety
    - React
    - React-callinvoker
    - React-Core
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-ImageManager
    - React-NativeModulesApple
    - React-RCTFabric
    - React-rendererdebug
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - Yoga
  - react-native-spinkit (1.4.1):
    - React
  - react-native-tracking-transparency (0.1.2):
    - React
  - react-native-userleap (2.13.0):
    - React
    - UserLeapKit (= 4.12.1)
  - react-native-view-shot (3.8.0):
    - React-Core
  - react-native-webview (13.13.2):
    - DoubleConversion
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.01.01.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-ImageManager
    - React-NativeModulesApple
    - React-RCTFabric
    - React-rendererdebug
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - Yoga
  - react-native-worklets-core (1.5.0):
    - DoubleConversion
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.01.01.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-ImageManager
    - React-NativeModulesApple
    - React-RCTFabric
    - React-rendererdebug
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - Yoga
  - React-nativeconfig (0.76.7)
  - React-NativeModulesApple (0.76.7):
    - glog
    - hermes-engine
    - React-callinvoker
    - React-Core
    - React-cxxreact
    - React-jsi
    - React-jsinspector
    - React-runtimeexecutor
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
  - React-perflogger (0.76.7):
    - DoubleConversion
    - RCT-Folly (= 2024.01.01.00)
  - React-performancetimeline (0.76.7):
    - RCT-Folly (= 2024.01.01.00)
    - React-cxxreact
    - React-timing
  - React-RCTActionSheet (0.76.7):
    - React-Core/RCTActionSheetHeaders (= 0.76.7)
  - React-RCTAnimation (0.76.7):
    - RCT-Folly (= 2024.01.01.00)
    - RCTTypeSafety
    - React-Core/RCTAnimationHeaders
    - React-jsi
    - React-NativeModulesApple
    - ReactCodegen
    - ReactCommon
  - React-RCTAppDelegate (0.76.7):
    - RCT-Folly (= 2024.01.01.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-CoreModules
    - React-debug
    - React-defaultsnativemodule
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-hermes
    - React-nativeconfig
    - React-NativeModulesApple
    - React-RCTFabric
    - React-RCTImage
    - React-RCTNetwork
    - React-rendererdebug
    - React-RuntimeApple
    - React-RuntimeCore
    - React-RuntimeHermes
    - React-runtimescheduler
    - React-utils
    - ReactCodegen
    - ReactCommon
  - React-RCTBlob (0.76.7):
    - DoubleConversion
    - fmt (= 9.1.0)
    - hermes-engine
    - RCT-Folly (= 2024.01.01.00)
    - React-Core/RCTBlobHeaders
    - React-Core/RCTWebSocket
    - React-jsi
    - React-jsinspector
    - React-NativeModulesApple
    - React-RCTNetwork
    - ReactCodegen
    - ReactCommon
  - React-RCTFabric (0.76.7):
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.01.01.00)
    - React-Core
    - React-debug
    - React-Fabric
    - React-FabricComponents
    - React-FabricImage
    - React-featureflags
    - React-graphics
    - React-ImageManager
    - React-jsi
    - React-jsinspector
    - React-nativeconfig
    - React-performancetimeline
    - React-RCTImage
    - React-RCTText
    - React-rendererconsistency
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - Yoga
  - React-RCTImage (0.76.7):
    - RCT-Folly (= 2024.01.01.00)
    - RCTTypeSafety
    - React-Core/RCTImageHeaders
    - React-jsi
    - React-NativeModulesApple
    - React-RCTNetwork
    - ReactCodegen
    - ReactCommon
  - React-RCTLinking (0.76.7):
    - React-Core/RCTLinkingHeaders (= 0.76.7)
    - React-jsi (= 0.76.7)
    - React-NativeModulesApple
    - ReactCodegen
    - ReactCommon
    - ReactCommon/turbomodule/core (= 0.76.7)
  - React-RCTNetwork (0.76.7):
    - RCT-Folly (= 2024.01.01.00)
    - RCTTypeSafety
    - React-Core/RCTNetworkHeaders
    - React-jsi
    - React-NativeModulesApple
    - ReactCodegen
    - ReactCommon
  - React-RCTSettings (0.76.7):
    - RCT-Folly (= 2024.01.01.00)
    - RCTTypeSafety
    - React-Core/RCTSettingsHeaders
    - React-jsi
    - React-NativeModulesApple
    - ReactCodegen
    - ReactCommon
  - React-RCTText (0.76.7):
    - React-Core/RCTTextHeaders (= 0.76.7)
    - Yoga
  - React-RCTVibration (0.76.7):
    - RCT-Folly (= 2024.01.01.00)
    - React-Core/RCTVibrationHeaders
    - React-jsi
    - React-NativeModulesApple
    - ReactCodegen
    - ReactCommon
  - React-rendererconsistency (0.76.7)
  - React-rendererdebug (0.76.7):
    - DoubleConversion
    - fmt (= 9.1.0)
    - RCT-Folly (= 2024.01.01.00)
    - React-debug
  - React-rncore (0.76.7)
  - React-RuntimeApple (0.76.7):
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.01.01.00)
    - React-callinvoker
    - React-Core/Default
    - React-CoreModules
    - React-cxxreact
    - React-jserrorhandler
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector
    - React-Mapbuffer
    - React-NativeModulesApple
    - React-RCTFabric
    - React-RuntimeCore
    - React-runtimeexecutor
    - React-RuntimeHermes
    - React-runtimescheduler
    - React-utils
  - React-RuntimeCore (0.76.7):
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.01.01.00)
    - React-cxxreact
    - React-featureflags
    - React-jserrorhandler
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector
    - React-performancetimeline
    - React-runtimeexecutor
    - React-runtimescheduler
    - React-utils
  - React-runtimeexecutor (0.76.7):
    - React-jsi (= 0.76.7)
  - React-RuntimeHermes (0.76.7):
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.01.01.00)
    - React-featureflags
    - React-hermes
    - React-jsi
    - React-jsinspector
    - React-jsitracing
    - React-nativeconfig
    - React-RuntimeCore
    - React-utils
  - React-runtimescheduler (0.76.7):
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.01.01.00)
    - React-callinvoker
    - React-cxxreact
    - React-debug
    - React-featureflags
    - React-jsi
    - React-performancetimeline
    - React-rendererconsistency
    - React-rendererdebug
    - React-runtimeexecutor
    - React-timing
    - React-utils
  - React-timing (0.76.7)
  - React-utils (0.76.7):
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.01.01.00)
    - React-debug
    - React-jsi (= 0.76.7)
  - ReactCodegen (0.76.7):
    - DoubleConversion
    - glog
    - hermes-engine
    - RCT-Folly
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-debug
    - React-Fabric
    - React-FabricImage
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-NativeModulesApple
    - React-rendererdebug
    - React-utils
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
  - ReactCommon (0.76.7):
    - ReactCommon/turbomodule (= 0.76.7)
  - ReactCommon/turbomodule (0.76.7):
    - DoubleConversion
    - fmt (= 9.1.0)
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.01.01.00)
    - React-callinvoker (= 0.76.7)
    - React-cxxreact (= 0.76.7)
    - React-jsi (= 0.76.7)
    - React-logger (= 0.76.7)
    - React-perflogger (= 0.76.7)
    - ReactCommon/turbomodule/bridging (= 0.76.7)
    - ReactCommon/turbomodule/core (= 0.76.7)
  - ReactCommon/turbomodule/bridging (0.76.7):
    - DoubleConversion
    - fmt (= 9.1.0)
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.01.01.00)
    - React-callinvoker (= 0.76.7)
    - React-cxxreact (= 0.76.7)
    - React-jsi (= 0.76.7)
    - React-logger (= 0.76.7)
    - React-perflogger (= 0.76.7)
  - ReactCommon/turbomodule/core (0.76.7):
    - DoubleConversion
    - fmt (= 9.1.0)
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.01.01.00)
    - React-callinvoker (= 0.76.7)
    - React-cxxreact (= 0.76.7)
    - React-debug (= 0.76.7)
    - React-featureflags (= 0.76.7)
    - React-jsi (= 0.76.7)
    - React-logger (= 0.76.7)
    - React-perflogger (= 0.76.7)
    - React-utils (= 0.76.7)
  - ReactNativeLocalization (2.3.2):
    - React-Core
  - rn-smile-id (0.0.50):
    - React-Core
    - Smile_Identity_SDK (= 2.1.41)
  - RNCAsyncStorage (1.23.1):
    - DoubleConversion
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.01.01.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-ImageManager
    - React-NativeModulesApple
    - React-RCTFabric
    - React-rendererdebug
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - Yoga
  - RNCClipboard (1.13.2):
    - DoubleConversion
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.01.01.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-ImageManager
    - React-NativeModulesApple
    - React-RCTFabric
    - React-rendererdebug
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - Yoga
  - RNDateTimePicker (8.3.0):
    - DoubleConversion
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.01.01.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-ImageManager
    - React-NativeModulesApple
    - React-RCTFabric
    - React-rendererdebug
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - Yoga
  - RNDeviceInfo (14.0.4):
    - React-Core
  - RNFastImage (8.6.5):
    - React-Core
    - SDWebImage (~> 5.12.1)
    - SDWebImageWebPCoder (~> 0.8.4)
  - RNFBApp (20.5.0):
    - Firebase/CoreOnly (= 10.29.0)
    - React-Core
  - RNFBMessaging (20.5.0):
    - Firebase/Messaging (= 10.29.0)
    - FirebaseCoreExtension
    - React-Core
    - RNFBApp
  - RNFBPerf (20.5.0):
    - Firebase/Performance (= 10.29.0)
    - React-Core
    - RNFBApp
  - RNFBRemoteConfig (20.5.0):
    - Firebase/RemoteConfig (= 10.29.0)
    - React-Core
    - RNFBApp
  - RNFBStorage (20.5.0):
    - Firebase/Storage (= 10.29.0)
    - React-Core
    - RNFBApp
  - RNFlashList (1.7.0):
    - DoubleConversion
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.01.01.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-ImageManager
    - React-NativeModulesApple
    - React-RCTFabric
    - React-rendererdebug
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - Yoga
  - RNFS (2.20.0):
    - React-Core
  - RNGestureHandler (2.24.0):
    - DoubleConversion
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.01.01.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-debug
    - React-Fabric
    - React-FabricComponents
    - React-featureflags
    - React-graphics
    - React-ImageManager
    - React-NativeModulesApple
    - React-RCTFabric
    - React-rendererdebug
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - Yoga
  - RNGoogleMobileAds (14.9.1):
    - DoubleConversion
    - glog
    - Google-Mobile-Ads-SDK (= 12.0.0)
    - GoogleUserMessagingPlatform (= 2.7.0)
    - hermes-engine
    - RCT-Folly (= 2024.01.01.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-ImageManager
    - React-NativeModulesApple
    - React-RCTFabric
    - React-rendererdebug
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - Yoga
  - RNImageCropPicker (0.34.1):
    - React-Core
    - React-RCTImage
    - RNImageCropPicker/QBImagePickerController (= 0.34.1)
    - TOCropViewController
  - RNImageCropPicker/QBImagePickerController (0.34.1):
    - React-Core
    - React-RCTImage
    - TOCropViewController
  - RNKeychain (10.0.0):
    - DoubleConversion
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.01.01.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-ImageManager
    - React-NativeModulesApple
    - React-RCTFabric
    - React-rendererdebug
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - Yoga
  - RNLocalize (3.0.2):
    - DoubleConversion
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.01.01.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-ImageManager
    - React-NativeModulesApple
    - React-RCTFabric
    - React-rendererdebug
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - Yoga
  - RNNotifee (7.8.0):
    - React-Core
    - RNNotifee/NotifeeCore (= 7.8.0)
  - RNNotifee/NotifeeCore (7.8.0):
    - React-Core
  - RNPermissions (5.2.6):
    - DoubleConversion
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.01.01.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-ImageManager
    - React-NativeModulesApple
    - React-RCTFabric
    - React-rendererdebug
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - Yoga
  - RNReactNativeHapticFeedback (1.11.0):
    - React-Core
  - RNReanimated (3.16.7):
    - DoubleConversion
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.01.01.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-ImageManager
    - React-NativeModulesApple
    - React-RCTFabric
    - React-rendererdebug
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - RNReanimated/reanimated (= 3.16.7)
    - RNReanimated/worklets (= 3.16.7)
    - Yoga
  - RNReanimated/reanimated (3.16.7):
    - DoubleConversion
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.01.01.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-ImageManager
    - React-NativeModulesApple
    - React-RCTFabric
    - React-rendererdebug
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - RNReanimated/reanimated/apple (= 3.16.7)
    - Yoga
  - RNReanimated/reanimated/apple (3.16.7):
    - DoubleConversion
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.01.01.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-ImageManager
    - React-NativeModulesApple
    - React-RCTFabric
    - React-rendererdebug
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - Yoga
  - RNReanimated/worklets (3.16.7):
    - DoubleConversion
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.01.01.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-ImageManager
    - React-NativeModulesApple
    - React-RCTFabric
    - React-rendererdebug
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - Yoga
  - RNScreens (4.9.0):
    - DoubleConversion
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.01.01.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-ImageManager
    - React-NativeModulesApple
    - React-RCTFabric
    - React-RCTImage
    - React-rendererdebug
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - RNScreens/common (= 4.9.0)
    - Yoga
  - RNScreens/common (4.9.0):
    - DoubleConversion
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.01.01.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-ImageManager
    - React-NativeModulesApple
    - React-RCTFabric
    - React-RCTImage
    - React-rendererdebug
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - Yoga
  - RNShare (12.0.9):
    - DoubleConversion
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.01.01.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-ImageManager
    - React-NativeModulesApple
    - React-RCTFabric
    - React-rendererdebug
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - Yoga
  - RNStaticSafeAreaInsets (2.2.0):
    - React-Core
  - RNSVG (15.11.1):
    - DoubleConversion
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.01.01.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-ImageManager
    - React-NativeModulesApple
    - React-RCTFabric
    - React-rendererdebug
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - RNSVG/common (= 15.11.1)
    - Yoga
  - RNSVG/common (15.11.1):
    - DoubleConversion
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.01.01.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-ImageManager
    - React-NativeModulesApple
    - React-RCTFabric
    - React-rendererdebug
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - Yoga
  - RNVectorIcons (10.2.0):
    - DoubleConversion
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.01.01.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-ImageManager
    - React-NativeModulesApple
    - React-RCTFabric
    - React-rendererdebug
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - Yoga
  - SDWebImage (5.12.6):
    - SDWebImage/Core (= 5.12.6)
  - SDWebImage/Core (5.12.6)
  - SDWebImageWebPCoder (0.8.5):
    - libwebp (~> 1.0)
    - SDWebImage/Core (~> 5.10)
  - Smile_Identity_SDK (2.1.41):
    - GoogleMLKit/FaceDetection
  - SocketRocket (0.7.1)
  - TOCropViewController (2.7.4)
  - UserLeapKit (4.12.1)
  - VisionCamera (4.6.4):
    - VisionCamera/Core (= 4.6.4)
    - VisionCamera/FrameProcessors (= 4.6.4)
    - VisionCamera/React (= 4.6.4)
  - VisionCamera/Core (4.6.4)
  - VisionCamera/FrameProcessors (4.6.4):
    - React
    - React-callinvoker
    - react-native-worklets-core
  - VisionCamera/React (4.6.4):
    - React-Core
    - VisionCamera/FrameProcessors
  - VisionCameraFaceDetector (1.8.1):
    - GoogleMLKit/FaceDetection
    - React-Core
    - VisionCamera
  - Yoga (0.0.0)

DEPENDENCIES:
  - "amplitude-react-native (from `../node_modules/@amplitude/analytics-react-native`)"
  - boost (from `../node_modules/react-native/third-party-podspecs/boost.podspec`)
  - "BugsnagReactNative (from `../node_modules/@bugsnag/react-native`)"
  - BVLinearGradient (from `../node_modules/react-native-linear-gradient`)
  - DoubleConversion (from `../node_modules/react-native/third-party-podspecs/DoubleConversion.podspec`)
  - "experiment-react-native-client (from `../node_modules/@amplitude/experiment-react-native-client`)"
  - FBLazyVector (from `../node_modules/react-native/Libraries/FBLazyVector`)
  - fmt (from `../node_modules/react-native/third-party-podspecs/fmt.podspec`)
  - glog (from `../node_modules/react-native/third-party-podspecs/glog.podspec`)
  - hermes-engine (from `../node_modules/react-native/sdks/hermes-engine/hermes-engine.podspec`)
  - "intercom-react-native (from `../node_modules/@intercom/intercom-react-native`)"
  - lottie-react-native (from `../node_modules/lottie-react-native`)
  - "onfido-react-native-sdk (from `../node_modules/@onfido/react-native-sdk`)"
  - RCT-Folly (from `../node_modules/react-native/third-party-podspecs/RCT-Folly.podspec`)
  - RCT-Folly/Fabric (from `../node_modules/react-native/third-party-podspecs/RCT-Folly.podspec`)
  - RCTDeprecation (from `../node_modules/react-native/ReactApple/Libraries/RCTFoundation/RCTDeprecation`)
  - RCTRequired (from `../node_modules/react-native/Libraries/Required`)
  - RCTTypeSafety (from `../node_modules/react-native/Libraries/TypeSafety`)
  - React (from `../node_modules/react-native/`)
  - React-callinvoker (from `../node_modules/react-native/ReactCommon/callinvoker`)
  - React-Core (from `../node_modules/react-native/`)
  - React-Core/RCTWebSocket (from `../node_modules/react-native/`)
  - React-CoreModules (from `../node_modules/react-native/React/CoreModules`)
  - React-cxxreact (from `../node_modules/react-native/ReactCommon/cxxreact`)
  - React-debug (from `../node_modules/react-native/ReactCommon/react/debug`)
  - React-defaultsnativemodule (from `../node_modules/react-native/ReactCommon/react/nativemodule/defaults`)
  - React-domnativemodule (from `../node_modules/react-native/ReactCommon/react/nativemodule/dom`)
  - React-Fabric (from `../node_modules/react-native/ReactCommon`)
  - React-FabricComponents (from `../node_modules/react-native/ReactCommon`)
  - React-FabricImage (from `../node_modules/react-native/ReactCommon`)
  - React-featureflags (from `../node_modules/react-native/ReactCommon/react/featureflags`)
  - React-featureflagsnativemodule (from `../node_modules/react-native/ReactCommon/react/nativemodule/featureflags`)
  - React-graphics (from `../node_modules/react-native/ReactCommon/react/renderer/graphics`)
  - React-hermes (from `../node_modules/react-native/ReactCommon/hermes`)
  - React-idlecallbacksnativemodule (from `../node_modules/react-native/ReactCommon/react/nativemodule/idlecallbacks`)
  - React-ImageManager (from `../node_modules/react-native/ReactCommon/react/renderer/imagemanager/platform/ios`)
  - React-jserrorhandler (from `../node_modules/react-native/ReactCommon/jserrorhandler`)
  - React-jsi (from `../node_modules/react-native/ReactCommon/jsi`)
  - React-jsiexecutor (from `../node_modules/react-native/ReactCommon/jsiexecutor`)
  - React-jsinspector (from `../node_modules/react-native/ReactCommon/jsinspector-modern`)
  - React-jsitracing (from `../node_modules/react-native/ReactCommon/hermes/executor/`)
  - React-logger (from `../node_modules/react-native/ReactCommon/logger`)
  - React-Mapbuffer (from `../node_modules/react-native/ReactCommon`)
  - React-microtasksnativemodule (from `../node_modules/react-native/ReactCommon/react/nativemodule/microtasks`)
  - react-native-ad-id (from `../node_modules/react-native-ad-id`)
  - react-native-app-auth (from `../node_modules/react-native-app-auth`)
  - react-native-background-timer (from `../node_modules/react-native-background-timer`)
  - react-native-blob-util (from `../node_modules/react-native-blob-util`)
  - "react-native-blur (from `../node_modules/@react-native-community/blur`)"
  - react-native-branch (from `../node_modules/react-native-branch`)
  - react-native-carrier-info (from `../node_modules/react-native-carrier-info`)
  - react-native-contacts (from `../node_modules/react-native-contacts`)
  - react-native-document-picker (from `../node_modules/react-native-document-picker`)
  - react-native-encrypted-storage (from `../node_modules/react-native-encrypted-storage`)
  - react-native-fbsdk (from `../node_modules/react-native-fbsdk`)
  - react-native-fingerprint-scanner (from `../node_modules/react-native-fingerprint-scanner`)
  - react-native-geolocation-service (from `../node_modules/react-native-geolocation-service`)
  - react-native-get-random-values (from `../node_modules/react-native-get-random-values`)
  - "react-native-image-resizer (from `../node_modules/@bam.tech/react-native-image-resizer`)"
  - react-native-mmkv (from `../node_modules/react-native-mmkv`)
  - "react-native-netinfo (from `../node_modules/@react-native-community/netinfo`)"
  - react-native-okhi (from `../node_modules/react-native-okhi`)
  - react-native-pager-view (from `../node_modules/react-native-pager-view`)
  - react-native-pdf (from `../node_modules/react-native-pdf`)
  - react-native-plaid-link-sdk (from `../node_modules/react-native-plaid-link-sdk`)
  - react-native-safe-area-context (from `../node_modules/react-native-safe-area-context`)
  - "react-native-segmented-control (from `../node_modules/@react-native-community/segmented-control`)"
  - "react-native-skia (from `../node_modules/@shopify/react-native-skia`)"
  - react-native-spinkit (from `../node_modules/react-native-spinkit`)
  - react-native-tracking-transparency (from `../node_modules/react-native-tracking-transparency`)
  - react-native-userleap (from `../node_modules/react-native-userleap`)
  - react-native-view-shot (from `../node_modules/react-native-view-shot`)
  - react-native-webview (from `../node_modules/react-native-webview`)
  - react-native-worklets-core (from `../node_modules/react-native-worklets-core`)
  - React-nativeconfig (from `../node_modules/react-native/ReactCommon`)
  - React-NativeModulesApple (from `../node_modules/react-native/ReactCommon/react/nativemodule/core/platform/ios`)
  - React-perflogger (from `../node_modules/react-native/ReactCommon/reactperflogger`)
  - React-performancetimeline (from `../node_modules/react-native/ReactCommon/react/performance/timeline`)
  - React-RCTActionSheet (from `../node_modules/react-native/Libraries/ActionSheetIOS`)
  - React-RCTAnimation (from `../node_modules/react-native/Libraries/NativeAnimation`)
  - React-RCTAppDelegate (from `../node_modules/react-native/Libraries/AppDelegate`)
  - React-RCTBlob (from `../node_modules/react-native/Libraries/Blob`)
  - React-RCTFabric (from `../node_modules/react-native/React`)
  - React-RCTImage (from `../node_modules/react-native/Libraries/Image`)
  - React-RCTLinking (from `../node_modules/react-native/Libraries/LinkingIOS`)
  - React-RCTNetwork (from `../node_modules/react-native/Libraries/Network`)
  - React-RCTSettings (from `../node_modules/react-native/Libraries/Settings`)
  - React-RCTText (from `../node_modules/react-native/Libraries/Text`)
  - React-RCTVibration (from `../node_modules/react-native/Libraries/Vibration`)
  - React-rendererconsistency (from `../node_modules/react-native/ReactCommon/react/renderer/consistency`)
  - React-rendererdebug (from `../node_modules/react-native/ReactCommon/react/renderer/debug`)
  - React-rncore (from `../node_modules/react-native/ReactCommon`)
  - React-RuntimeApple (from `../node_modules/react-native/ReactCommon/react/runtime/platform/ios`)
  - React-RuntimeCore (from `../node_modules/react-native/ReactCommon/react/runtime`)
  - React-runtimeexecutor (from `../node_modules/react-native/ReactCommon/runtimeexecutor`)
  - React-RuntimeHermes (from `../node_modules/react-native/ReactCommon/react/runtime`)
  - React-runtimescheduler (from `../node_modules/react-native/ReactCommon/react/renderer/runtimescheduler`)
  - React-timing (from `../node_modules/react-native/ReactCommon/react/timing`)
  - React-utils (from `../node_modules/react-native/ReactCommon/react/utils`)
  - ReactCodegen (from `build/generated/ios`)
  - ReactCommon/turbomodule/core (from `../node_modules/react-native/ReactCommon`)
  - ReactNativeLocalization (from `../node_modules/react-native-localization`)
  - rn-smile-id (from `../node_modules/rn-smile-id`)
  - "RNCAsyncStorage (from `../node_modules/@react-native-async-storage/async-storage`)"
  - "RNCClipboard (from `../node_modules/@react-native-clipboard/clipboard`)"
  - "RNDateTimePicker (from `../node_modules/@react-native-community/datetimepicker`)"
  - RNDeviceInfo (from `../node_modules/react-native-device-info`)
  - RNFastImage (from `../node_modules/react-native-fast-image`)
  - "RNFBApp (from `../node_modules/@react-native-firebase/app`)"
  - "RNFBMessaging (from `../node_modules/@react-native-firebase/messaging`)"
  - "RNFBPerf (from `../node_modules/@react-native-firebase/perf`)"
  - "RNFBRemoteConfig (from `../node_modules/@react-native-firebase/remote-config`)"
  - "RNFBStorage (from `../node_modules/@react-native-firebase/storage`)"
  - "RNFlashList (from `../node_modules/@shopify/flash-list`)"
  - RNFS (from `../node_modules/react-native-fs`)
  - RNGestureHandler (from `../node_modules/react-native-gesture-handler`)
  - RNGoogleMobileAds (from `../node_modules/react-native-google-mobile-ads`)
  - RNImageCropPicker (from `../node_modules/react-native-image-crop-picker`)
  - RNKeychain (from `../node_modules/react-native-keychain`)
  - RNLocalize (from `../node_modules/react-native-localize`)
  - "RNNotifee (from `../node_modules/@notifee/react-native`)"
  - RNPermissions (from `../node_modules/react-native-permissions`)
  - RNReactNativeHapticFeedback (from `../node_modules/react-native-haptic-feedback`)
  - RNReanimated (from `../node_modules/react-native-reanimated`)
  - RNScreens (from `../node_modules/react-native-screens`)
  - RNShare (from `../node_modules/react-native-share`)
  - RNStaticSafeAreaInsets (from `../node_modules/react-native-static-safe-area-insets`)
  - RNSVG (from `../node_modules/react-native-svg`)
  - RNVectorIcons (from `../node_modules/react-native-vector-icons`)
  - VisionCamera (from `../node_modules/react-native-vision-camera`)
  - VisionCameraFaceDetector (from `../node_modules/react-native-vision-camera-face-detector`)
  - Yoga (from `../node_modules/react-native/ReactCommon/yoga`)

SPEC REPOS:
  trunk:
    - AppAuth
    - BranchSDK
    - FBSDKCoreKit
    - FBSDKLoginKit
    - FBSDKShareKit
    - Firebase
    - FirebaseABTesting
    - FirebaseAppCheckInterop
    - FirebaseAuthInterop
    - FirebaseCore
    - FirebaseCoreExtension
    - FirebaseCoreInternal
    - FirebaseInstallations
    - FirebaseMessaging
    - FirebasePerformance
    - FirebaseRemoteConfig
    - FirebaseRemoteConfigInterop
    - FirebaseSessions
    - FirebaseSharedSwift
    - FirebaseStorage
    - Google-Mobile-Ads-SDK
    - GoogleDataTransport
    - GoogleMLKit
    - GoogleToolboxForMac
    - GoogleUserMessagingPlatform
    - GoogleUtilities
    - GoogleUtilitiesComponents
    - GTMSessionFetcher
    - Intercom
    - libwebp
    - lottie-ios
    - MLImage
    - MLKitCommon
    - MLKitFaceDetection
    - MLKitVision
    - nanopb
    - OkHi
    - Onfido
    - Plaid
    - PromisesObjC
    - PromisesSwift
    - React-Codegen
    - SDWebImage
    - SDWebImageWebPCoder
    - Smile_Identity_SDK
    - SocketRocket
    - TOCropViewController
    - UserLeapKit

EXTERNAL SOURCES:
  amplitude-react-native:
    :path: "../node_modules/@amplitude/analytics-react-native"
  boost:
    :podspec: "../node_modules/react-native/third-party-podspecs/boost.podspec"
  BugsnagReactNative:
    :path: "../node_modules/@bugsnag/react-native"
  BVLinearGradient:
    :path: "../node_modules/react-native-linear-gradient"
  DoubleConversion:
    :podspec: "../node_modules/react-native/third-party-podspecs/DoubleConversion.podspec"
  experiment-react-native-client:
    :path: "../node_modules/@amplitude/experiment-react-native-client"
  FBLazyVector:
    :path: "../node_modules/react-native/Libraries/FBLazyVector"
  fmt:
    :podspec: "../node_modules/react-native/third-party-podspecs/fmt.podspec"
  glog:
    :podspec: "../node_modules/react-native/third-party-podspecs/glog.podspec"
  hermes-engine:
    :podspec: "../node_modules/react-native/sdks/hermes-engine/hermes-engine.podspec"
    :tag: hermes-2024-11-12-RNv0.76.2-5b4aa20c719830dcf5684832b89a6edb95ac3d64
  intercom-react-native:
    :path: "../node_modules/@intercom/intercom-react-native"
  lottie-react-native:
    :path: "../node_modules/lottie-react-native"
  onfido-react-native-sdk:
    :path: "../node_modules/@onfido/react-native-sdk"
  RCT-Folly:
    :podspec: "../node_modules/react-native/third-party-podspecs/RCT-Folly.podspec"
  RCTDeprecation:
    :path: "../node_modules/react-native/ReactApple/Libraries/RCTFoundation/RCTDeprecation"
  RCTRequired:
    :path: "../node_modules/react-native/Libraries/Required"
  RCTTypeSafety:
    :path: "../node_modules/react-native/Libraries/TypeSafety"
  React:
    :path: "../node_modules/react-native/"
  React-callinvoker:
    :path: "../node_modules/react-native/ReactCommon/callinvoker"
  React-Core:
    :path: "../node_modules/react-native/"
  React-CoreModules:
    :path: "../node_modules/react-native/React/CoreModules"
  React-cxxreact:
    :path: "../node_modules/react-native/ReactCommon/cxxreact"
  React-debug:
    :path: "../node_modules/react-native/ReactCommon/react/debug"
  React-defaultsnativemodule:
    :path: "../node_modules/react-native/ReactCommon/react/nativemodule/defaults"
  React-domnativemodule:
    :path: "../node_modules/react-native/ReactCommon/react/nativemodule/dom"
  React-Fabric:
    :path: "../node_modules/react-native/ReactCommon"
  React-FabricComponents:
    :path: "../node_modules/react-native/ReactCommon"
  React-FabricImage:
    :path: "../node_modules/react-native/ReactCommon"
  React-featureflags:
    :path: "../node_modules/react-native/ReactCommon/react/featureflags"
  React-featureflagsnativemodule:
    :path: "../node_modules/react-native/ReactCommon/react/nativemodule/featureflags"
  React-graphics:
    :path: "../node_modules/react-native/ReactCommon/react/renderer/graphics"
  React-hermes:
    :path: "../node_modules/react-native/ReactCommon/hermes"
  React-idlecallbacksnativemodule:
    :path: "../node_modules/react-native/ReactCommon/react/nativemodule/idlecallbacks"
  React-ImageManager:
    :path: "../node_modules/react-native/ReactCommon/react/renderer/imagemanager/platform/ios"
  React-jserrorhandler:
    :path: "../node_modules/react-native/ReactCommon/jserrorhandler"
  React-jsi:
    :path: "../node_modules/react-native/ReactCommon/jsi"
  React-jsiexecutor:
    :path: "../node_modules/react-native/ReactCommon/jsiexecutor"
  React-jsinspector:
    :path: "../node_modules/react-native/ReactCommon/jsinspector-modern"
  React-jsitracing:
    :path: "../node_modules/react-native/ReactCommon/hermes/executor/"
  React-logger:
    :path: "../node_modules/react-native/ReactCommon/logger"
  React-Mapbuffer:
    :path: "../node_modules/react-native/ReactCommon"
  React-microtasksnativemodule:
    :path: "../node_modules/react-native/ReactCommon/react/nativemodule/microtasks"
  react-native-ad-id:
    :path: "../node_modules/react-native-ad-id"
  react-native-app-auth:
    :path: "../node_modules/react-native-app-auth"
  react-native-background-timer:
    :path: "../node_modules/react-native-background-timer"
  react-native-blob-util:
    :path: "../node_modules/react-native-blob-util"
  react-native-blur:
    :path: "../node_modules/@react-native-community/blur"
  react-native-branch:
    :path: "../node_modules/react-native-branch"
  react-native-carrier-info:
    :path: "../node_modules/react-native-carrier-info"
  react-native-contacts:
    :path: "../node_modules/react-native-contacts"
  react-native-document-picker:
    :path: "../node_modules/react-native-document-picker"
  react-native-encrypted-storage:
    :path: "../node_modules/react-native-encrypted-storage"
  react-native-fbsdk:
    :path: "../node_modules/react-native-fbsdk"
  react-native-fingerprint-scanner:
    :path: "../node_modules/react-native-fingerprint-scanner"
  react-native-geolocation-service:
    :path: "../node_modules/react-native-geolocation-service"
  react-native-get-random-values:
    :path: "../node_modules/react-native-get-random-values"
  react-native-image-resizer:
    :path: "../node_modules/@bam.tech/react-native-image-resizer"
  react-native-mmkv:
    :path: "../node_modules/react-native-mmkv"
  react-native-netinfo:
    :path: "../node_modules/@react-native-community/netinfo"
  react-native-okhi:
    :path: "../node_modules/react-native-okhi"
  react-native-pager-view:
    :path: "../node_modules/react-native-pager-view"
  react-native-pdf:
    :path: "../node_modules/react-native-pdf"
  react-native-plaid-link-sdk:
    :path: "../node_modules/react-native-plaid-link-sdk"
  react-native-safe-area-context:
    :path: "../node_modules/react-native-safe-area-context"
  react-native-segmented-control:
    :path: "../node_modules/@react-native-community/segmented-control"
  react-native-skia:
    :path: "../node_modules/@shopify/react-native-skia"
  react-native-spinkit:
    :path: "../node_modules/react-native-spinkit"
  react-native-tracking-transparency:
    :path: "../node_modules/react-native-tracking-transparency"
  react-native-userleap:
    :path: "../node_modules/react-native-userleap"
  react-native-view-shot:
    :path: "../node_modules/react-native-view-shot"
  react-native-webview:
    :path: "../node_modules/react-native-webview"
  react-native-worklets-core:
    :path: "../node_modules/react-native-worklets-core"
  React-nativeconfig:
    :path: "../node_modules/react-native/ReactCommon"
  React-NativeModulesApple:
    :path: "../node_modules/react-native/ReactCommon/react/nativemodule/core/platform/ios"
  React-perflogger:
    :path: "../node_modules/react-native/ReactCommon/reactperflogger"
  React-performancetimeline:
    :path: "../node_modules/react-native/ReactCommon/react/performance/timeline"
  React-RCTActionSheet:
    :path: "../node_modules/react-native/Libraries/ActionSheetIOS"
  React-RCTAnimation:
    :path: "../node_modules/react-native/Libraries/NativeAnimation"
  React-RCTAppDelegate:
    :path: "../node_modules/react-native/Libraries/AppDelegate"
  React-RCTBlob:
    :path: "../node_modules/react-native/Libraries/Blob"
  React-RCTFabric:
    :path: "../node_modules/react-native/React"
  React-RCTImage:
    :path: "../node_modules/react-native/Libraries/Image"
  React-RCTLinking:
    :path: "../node_modules/react-native/Libraries/LinkingIOS"
  React-RCTNetwork:
    :path: "../node_modules/react-native/Libraries/Network"
  React-RCTSettings:
    :path: "../node_modules/react-native/Libraries/Settings"
  React-RCTText:
    :path: "../node_modules/react-native/Libraries/Text"
  React-RCTVibration:
    :path: "../node_modules/react-native/Libraries/Vibration"
  React-rendererconsistency:
    :path: "../node_modules/react-native/ReactCommon/react/renderer/consistency"
  React-rendererdebug:
    :path: "../node_modules/react-native/ReactCommon/react/renderer/debug"
  React-rncore:
    :path: "../node_modules/react-native/ReactCommon"
  React-RuntimeApple:
    :path: "../node_modules/react-native/ReactCommon/react/runtime/platform/ios"
  React-RuntimeCore:
    :path: "../node_modules/react-native/ReactCommon/react/runtime"
  React-runtimeexecutor:
    :path: "../node_modules/react-native/ReactCommon/runtimeexecutor"
  React-RuntimeHermes:
    :path: "../node_modules/react-native/ReactCommon/react/runtime"
  React-runtimescheduler:
    :path: "../node_modules/react-native/ReactCommon/react/renderer/runtimescheduler"
  React-timing:
    :path: "../node_modules/react-native/ReactCommon/react/timing"
  React-utils:
    :path: "../node_modules/react-native/ReactCommon/react/utils"
  ReactCodegen:
    :path: build/generated/ios
  ReactCommon:
    :path: "../node_modules/react-native/ReactCommon"
  ReactNativeLocalization:
    :path: "../node_modules/react-native-localization"
  rn-smile-id:
    :path: "../node_modules/rn-smile-id"
  RNCAsyncStorage:
    :path: "../node_modules/@react-native-async-storage/async-storage"
  RNCClipboard:
    :path: "../node_modules/@react-native-clipboard/clipboard"
  RNDateTimePicker:
    :path: "../node_modules/@react-native-community/datetimepicker"
  RNDeviceInfo:
    :path: "../node_modules/react-native-device-info"
  RNFastImage:
    :path: "../node_modules/react-native-fast-image"
  RNFBApp:
    :path: "../node_modules/@react-native-firebase/app"
  RNFBMessaging:
    :path: "../node_modules/@react-native-firebase/messaging"
  RNFBPerf:
    :path: "../node_modules/@react-native-firebase/perf"
  RNFBRemoteConfig:
    :path: "../node_modules/@react-native-firebase/remote-config"
  RNFBStorage:
    :path: "../node_modules/@react-native-firebase/storage"
  RNFlashList:
    :path: "../node_modules/@shopify/flash-list"
  RNFS:
    :path: "../node_modules/react-native-fs"
  RNGestureHandler:
    :path: "../node_modules/react-native-gesture-handler"
  RNGoogleMobileAds:
    :path: "../node_modules/react-native-google-mobile-ads"
  RNImageCropPicker:
    :path: "../node_modules/react-native-image-crop-picker"
  RNKeychain:
    :path: "../node_modules/react-native-keychain"
  RNLocalize:
    :path: "../node_modules/react-native-localize"
  RNNotifee:
    :path: "../node_modules/@notifee/react-native"
  RNPermissions:
    :path: "../node_modules/react-native-permissions"
  RNReactNativeHapticFeedback:
    :path: "../node_modules/react-native-haptic-feedback"
  RNReanimated:
    :path: "../node_modules/react-native-reanimated"
  RNScreens:
    :path: "../node_modules/react-native-screens"
  RNShare:
    :path: "../node_modules/react-native-share"
  RNStaticSafeAreaInsets:
    :path: "../node_modules/react-native-static-safe-area-insets"
  RNSVG:
    :path: "../node_modules/react-native-svg"
  RNVectorIcons:
    :path: "../node_modules/react-native-vector-icons"
  VisionCamera:
    :path: "../node_modules/react-native-vision-camera"
  VisionCameraFaceDetector:
    :path: "../node_modules/react-native-vision-camera-face-detector"
  Yoga:
    :path: "../node_modules/react-native/ReactCommon/yoga"

SPEC CHECKSUMS:
  amplitude-react-native: 9d57e1bcc4175039e36283390aa3daeaea9441a5
  AppAuth: d4f13a8fe0baf391b2108511793e4b479691fb73
  boost: 1dca942403ed9342f98334bf4c3621f011aa7946
  BranchSDK: ce28650272c658fcdb66675769e670ef83845d17
  BugsnagReactNative: 0e67d7e6d7aab02bfde08ca4cf8b7f6e884a39db
  BVLinearGradient: 880f91a7854faff2df62518f0281afb1c60d49a3
  DoubleConversion: f16ae600a246532c4020132d54af21d0ddb2a385
  experiment-react-native-client: 5d55c92cdafba6ea657f7afa8123906b5c835be1
  FBLazyVector: ca8044c9df513671c85167838b4188791b6f37e1
  FBSDKCoreKit: 4afd6ff53d8133a433dbcda44451c9498f8c6ce4
  FBSDKLoginKit: 7181765f2524d7ebf82d9629066c8e6caafc99d0
  FBSDKShareKit: 0c0d51b3af47075a85ed9bea0e28b2fc70e3594b
  Firebase: cec914dab6fd7b1bd8ab56ea07ce4e03dd251c2d
  FirebaseABTesting: d87f56707159bae64e269757a6e963d490f2eebe
  FirebaseAppCheckInterop: 6a1757cfd4067d8e00fccd14fcc1b8fd78cfac07
  FirebaseAuthInterop: 17db81e9b198afb0f95ce48c133825727eed55d3
  FirebaseCore: 30e9c1cbe3d38f5f5e75f48bfcea87d7c358ec16
  FirebaseCoreExtension: 705ca5b14bf71d2564a0ddc677df1fc86ffa600f
  FirebaseCoreInternal: df84dd300b561c27d5571684f389bf60b0a5c934
  FirebaseInstallations: 913cf60d0400ebd5d6b63a28b290372ab44590dd
  FirebaseMessaging: 7b5d8033e183ab59eb5b852a53201559e976d366
  FirebasePerformance: d0ac4aa90f8c1aedeb8d0329a56e2d77d8d9e004
  FirebaseRemoteConfig: 48ef3f243742a8d72422ccfc9f986e19d7de53fd
  FirebaseRemoteConfigInterop: 6efda51fb5e2f15b16585197e26eaa09574e8a4d
  FirebaseSessions: dbd14adac65ce996228652c1fc3a3f576bdf3ecc
  FirebaseSharedSwift: 20530f495084b8d840f78a100d8c5ee613375f6e
  FirebaseStorage: 436c30aa46f2177ba152f268fe4452118b8a4856
  fmt: 10c6e61f4be25dc963c36bd73fc7b1705fe975be
  glog: 08b301085f15bcbb6ff8632a8ebaf239aae04e6a
  Google-Mobile-Ads-SDK: 14a8e52164a89a44178bd67d544f207969f8d69a
  GoogleDataTransport: 6c09b596d841063d76d4288cc2d2f42cc36e1e2a
  GoogleMLKit: 97ac7af399057e99182ee8edfa8249e3226a4065
  GoogleToolboxForMac: d1a2cbf009c453f4d6ded37c105e2f67a32206d8
  GoogleUserMessagingPlatform: a8b56893477f67212fbc8411c139e61d463349f5
  GoogleUtilities: ea963c370a38a8069cc5f7ba4ca849a60b6d7d15
  GoogleUtilitiesComponents: 679b2c881db3b615a2777504623df6122dd20afe
  GTMSessionFetcher: 5aea5ba6bd522a239e236100971f10cb71b96ab6
  hermes-engine: eb4a80f6bf578536c58a44198ec93a30f6e69218
  Intercom: baf5b7c5107289aa4e503130ca5fffca92d5a2ae
  intercom-react-native: c32823bcd02cf6b2e3d6d25aebefc5ccdfe15f0f
  libwebp: 02b23773aedb6ff1fd38cec7a77b81414c6842a8
  lottie-ios: a881093fab623c467d3bce374367755c272bdd59
  lottie-react-native: 83580847fed6eaf097b4b6b03f6632792809d147
  MLImage: 1824212150da33ef225fbd3dc49f184cf611046c
  MLKitCommon: afec63980417d29ffbb4790529a1b0a2291699e1
  MLKitFaceDetection: 7c0e8bf09ddd27105da32d088fca978a99fc30cc
  MLKitVision: e858c5f125ecc288e4a31127928301eaba9ae0c1
  nanopb: 438bc412db1928dac798aa6fd75726007be04262
  OkHi: 54dc547f189705fb55430697fba4b8b2cf1723e1
  Onfido: 371e0b651be0426d5c838db8a18841eb01d67007
  onfido-react-native-sdk: e9ad7bdd915cf41170810d39fd51327daf361983
  Plaid: 293e77af7a75c035b8dce0938910dc00a9e24910
  PromisesObjC: f5707f49cb48b9636751c5b2e7d227e43fba9f47
  PromisesSwift: 9d77319bbe72ebf6d872900551f7eeba9bce2851
  RCT-Folly: bf5c0376ffe4dd2cf438dcf86db385df9fdce648
  RCTDeprecation: 7691283dd69fed46f6653d376de6fa83aaad774c
  RCTRequired: eac044a04629288f272ee6706e31f81f3a2b4bfe
  RCTTypeSafety: cfe499e127eda6dd46e5080e12d80d0bfe667228
  React: 1f3737a983fdd26fb3d388ddbca41a26950fe929
  React-callinvoker: 5c15ac628eab5468fe0b4dc453495f4742761f00
  React-Codegen: 4b8b4817cea7a54b83851d4c1f91f79aa73de30a
  React-Core: 4b90a977a5b2777fd8f4a8db7325a83431ecd2d8
  React-CoreModules: 385bbacfa34ac9208aa24f239a5184fa7ab1cd28
  React-cxxreact: 3e09bcdf1f86b931b5e96bf5429d7c274a0ec168
  React-debug: 2669a88076750ff75f3ee720f5744bd6dca62505
  React-defaultsnativemodule: c723434eb3417662fbf7129ef7050fff79a8d6c5
  React-domnativemodule: 213202bbe24efe4a2196c79bc9a3c23266575ce3
  React-Fabric: 61dfae833d674bbfea1925e484d2de3c73443af5
  React-FabricComponents: f8b808ea0c5b7f8cb994b42cca4b2850a184ba39
  React-FabricImage: 6121ae656aa88beb74a4bcd63fda61ca40ca2d88
  React-featureflags: de929056b02f8e00b8ee0de4ad6f107c1f6beb82
  React-featureflagsnativemodule: 783185b42dec3865261e929c715b20d617768b95
  React-graphics: 085147f596e4cede9e8a2f1c0e61f7a1730e1371
  React-hermes: 24bfc254f1ba83182d4936641898fe963af343fb
  React-idlecallbacksnativemodule: 28fc3950a40c8a751dd58c05216ea3cbf26d3e3d
  React-ImageManager: 5465f84b7b58fb056dbec1a315bcdaee92e3a47f
  React-jserrorhandler: 5b07e25284405ddca6465cf4ef83542a04ffbce1
  React-jsi: ede7e8c96f997f8772871c82688cea53c1ffb148
  React-jsiexecutor: fc9b287189ce800a92d5ab4e7291508eacaab451
  React-jsinspector: 657e24b84532a572238ddb577885648c4b8c925e
  React-jsitracing: d5d719177e8ac8df8289992a841b4e08e9a68fd4
  React-logger: f9d104eace4ce03d7d5ab96802069d9905082225
  React-Mapbuffer: 33ee28be239923f0e1db4650f859380c5640562c
  React-microtasksnativemodule: bfacefa4aa4e3f9264d274012de38b314414bf11
  react-native-ad-id: e591afd89c9331e6d76915496b2606fb9e626ef3
  react-native-app-auth: 390986b663497d92de5c00d77b6e2c947d52995e
  react-native-background-timer: 17ea5e06803401a379ebf1f20505b793ac44d0fe
  react-native-blob-util: c3b0faecd2919db568e9d552084396f3e50b57c7
  react-native-blur: d81883c19b028d3d04b44126ea661ae9e7ff35b6
  react-native-branch: f50f269fa6d2e1b8025c7fc8ebfe4872678abe53
  react-native-carrier-info: f94c990c4e27017648738430afc01acf367e102f
  react-native-contacts: 5cfaafcc92c2c10cc3040113cb0f2b6851854dff
  react-native-document-picker: 4921045c5c6c78243010f784eae0a3f4b2af7799
  react-native-encrypted-storage: db300a3f2f0aba1e818417c1c0a6be549038deb7
  react-native-fbsdk: 8e92c7a334fd8d9c2b5d8b8fcd7ae2c314161099
  react-native-fingerprint-scanner: ac6656f18c8e45a7459302b84da41a44ad96dbbe
  react-native-geolocation-service: 608e1da71a1ac31b4de64d9ef2815f697978c55b
  react-native-get-random-values: a6ea6a8a65dc93e96e24a11105b1a9c8cfe1d72a
  react-native-image-resizer: a2c220950f240cfe60f1fb23399ec3ee0db438ae
  react-native-mmkv: 35992c4170a4a46be095fd1b7fe17d2fd1d42124
  react-native-netinfo: f0a9899081c185db1de5bb2fdc1c88c202a059ac
  react-native-okhi: 4d198343738e6c319d94b46ef862efe547e42876
  react-native-pager-view: 1e07c15ab08a6833b05923e5a2a8d5e6f8fabe4f
  react-native-pdf: 5d85253646c36b0fda050ec5226654f0f2f31a25
  react-native-plaid-link-sdk: ec01f6fb12a58bdaefc4fd165b1fc4e97fd01d8e
  react-native-safe-area-context: d2a27f4bd25ee263641970904107e069cd4d8cf8
  react-native-segmented-control: 65df6cd0619b780b3843d574a72d4c7cec396097
  react-native-skia: 3e676b80ae8e8fbda01d10fdec49f7dc2f982d01
  react-native-spinkit: da294fd828216ad211fe36a5c14c1e09f09e62db
  react-native-tracking-transparency: 25ff1ff866e338c137c818bdec20526bb05ffcc1
  react-native-userleap: 429907db2e229713e3ab8ddafb817c2732918720
  react-native-view-shot: 6b7ed61d77d88580fed10954d45fad0eb2d47688
  react-native-webview: 458c3029d00a1d02e1ec3c9389d5fa1065a7411f
  react-native-worklets-core: aeaf19098bcf6aa5e183eac9b42617e167ccd11b
  React-nativeconfig: c77447a7820ac780b97f121ae6248ac07248835f
  React-NativeModulesApple: 2d6558580b3eb08593091264bdfff3301e708d9d
  React-perflogger: f02ee21d98773121d77993b3c1a8be445840fae3
  React-performancetimeline: 348b294a18697f9fa0ed3229bb1eb0da448be231
  React-RCTActionSheet: ad84d5a0bd1ad1782f0b78b280c6f329ad79a53a
  React-RCTAnimation: 388460f7c124c76e337c6646738a83d6ea147095
  React-RCTAppDelegate: 4661e2a44f7ce1033bf6f373f7d5368b11f5a2be
  React-RCTBlob: 07cccbb74e22ce66745358799f6ab02a5bed2993
  React-RCTFabric: e4e698b6a07e8a18a1596cb9eef5e45f4f11afd4
  React-RCTImage: 8fbdae841ea1217c44f4c413bba2403134b83cd1
  React-RCTLinking: c59bf8286ba2cc327b01bb524fb9c16446dc18bc
  React-RCTNetwork: 2c137a0aaaed2cf4bb53aff82a2bb8c34f2fbeac
  React-RCTSettings: 9fcd32c5b38af6421a3dd20cdd9ebf09df0a9a6d
  React-RCTText: 5308618477fec454282809065bd121c2bd3dd5e1
  React-RCTVibration: 7b2a186756b5c8e586e3e7948eed4432a93299c0
  React-rendererconsistency: 764e4066c5ec9f80b8b18b4b117ec92c79ce4011
  React-rendererdebug: ac19be3c50e9c370538620c4f323f5b426f47ce2
  React-rncore: 75849d74ca735b2496f667635b9686594e2f6624
  React-RuntimeApple: e4da43c529498b23f024225138da6aa2ebf481aa
  React-RuntimeCore: 75386d1ac115277c83e63678bd200173d32ea138
  React-runtimeexecutor: 732038d7c356ba74132f1d16253a410621d3c2c1
  React-RuntimeHermes: 1cb35aa9baf0609e898deb0857aff72a886e2606
  React-runtimescheduler: 96279a5332c45a79999ff5afcdc828e970abb157
  React-timing: 30584b21b496cf80d332c70db3063c295f997fa3
  React-utils: 72cb4a2db18c087d09ec80079524e1fcb27b29a0
  ReactCodegen: 807cff999d1705af15b99363e2cc37ca9fae4761
  ReactCommon: 043f537fa4c5366bb960a760bc626fb50c4eb84f
  ReactNativeLocalization: fb171138cdc80d5d0d4f20243d2fc82c2b3cc48f
  rn-smile-id: f244fb6d111412d41d818af8102387e098fd1ec0
  RNCAsyncStorage: 722620dc87fb48ad321505fcfccd1782455c7448
  RNCClipboard: a13d9a31591b058e46c517f481935adf11271f1d
  RNDateTimePicker: dfea58203474b4a5b5d0550e2e9ed35026e7859f
  RNDeviceInfo: feea80a690d2bde1fe51461cf548039258bd03f2
  RNFastImage: 8a6447a64c4b98530ea8ab149aec0cae9f65f0aa
  RNFBApp: 5f87753a8d8b37d229adf85cd0ff37709ffdf008
  RNFBMessaging: 3fa1114c0868dd21f20dfe186adf42297ea316b1
  RNFBPerf: e5cc00c3fe9cf4f119b444216dd18c396dcbd378
  RNFBRemoteConfig: 2867b9d3484b7aa86e095dcde8c333c029004bd0
  RNFBStorage: cfaaa615c556212a56987e735b4e3787363e5024
  RNFlashList: 918380bebee5e234ee2e743fcc65a2ef9130a19b
  RNFS: 4ac0f0ea233904cb798630b3c077808c06931688
  RNGestureHandler: ab2d86c369f7450edf9ebe2c1b292983fe649acb
  RNGoogleMobileAds: c2906f12b91ac03b3d0b125b82f316543ed4d39f
  RNImageCropPicker: 39ec170dc53c227ea352b706c76f7960070309a8
  RNKeychain: 7798de32076b5f64dcfe51aabc7e3b571cd6cd9d
  RNLocalize: 6817308ef97c80d91ceffdad6110f5330a0d58b8
  RNNotifee: f3c01b391dd8e98e67f539f9a35a9cbcd3bae744
  RNPermissions: d37ef973c3efee260c1b628807e4c6fd21b44924
  RNReactNativeHapticFeedback: 653a8c126a0f5e88ce15ffe280b3ff37e1fbb285
  RNReanimated: ddc60075b382b872662cadaf9972b07d52a8fce7
  RNScreens: edb1a4fd3b5a251a4591d6475207809c5aee1edd
  RNShare: 2ff8d5040f2c1651173d810a7bb6c1b0cd2c6699
  RNStaticSafeAreaInsets: 055ddbf5e476321720457cdaeec0ff2ba40ec1b8
  RNSVG: 3cd6669b7ad658c8b482db630dccf364c3b59cfb
  RNVectorIcons: 8c7af262a73890caabbac2bb081ddbaa1b8a768c
  SDWebImage: a47aea9e3d8816015db4e523daff50cfd294499d
  SDWebImageWebPCoder: 908b83b6adda48effe7667cd2b7f78c897e5111d
  Smile_Identity_SDK: 5f045640d515bab9b9c675c450ccde350236c96a
  SocketRocket: d4aabe649be1e368d1318fdf28a022d714d65748
  TOCropViewController: 80b8985ad794298fb69d3341de183f33d1853654
  UserLeapKit: f4c104b74d9682a730a1b82c657c57e5edbe630a
  VisionCamera: 3cc9afb487bb11de5dca69581e976ca473bb39ea
  VisionCameraFaceDetector: 6b3b28b3e953cead2b3cd4b84874524e9667cf62
  Yoga: 069a51fa94a03880d83adc91995fe180e997ce58

PODFILE CHECKSUM: b5d15e6810f633f1b3381016abd06cd6aec9fd87

COCOAPODS: 1.15.2
