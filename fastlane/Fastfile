# This file contains the fastlane.tools configuration
# You can find the documentation at https://docs.fastlane.tools
#
# For a list of all available actions, check out
#
#     https://docs.fastlane.tools/actions
#
# For a list of all available plugins, check out
#
#     https://docs.fastlane.tools/plugins/available-plugins
#

# Uncomment the line if you want fastlane to automatically update itself
# update_fastlane

# DOCS
# https://www.notion.so/chippercash/Building-The-App-For-Distribution-WIP-15f6945e96de4867a2c75b5a11eb6b69

default_platform(:ios)

platform :ios do
  desc "Retrieves the API Key to connect"
  private_lane :api_key do |options|
    api_key = app_store_connect_api_key(
      key_id: options[:key_id],
      issuer_id: options[:issuer_id],
      key_filepath: options[:key_filepath],
    )

    api_key
  end

  lane :bump_version do |options|
    increment_version_number_in_xcodeproj(
      bump_type: "minor",
      xcodeproj: "ios/ChipperNative.xcodeproj",
      target: "ChipperNative",
    )

    increment_version_number_in_plist(
      bump_type: "minor",
      xcodeproj: "ios/ChipperNative.xcodeproj",
      target: "ChipperNativeTests",
    )

    increment_version_number_in_plist(
      bump_type: "minor",
      xcodeproj: "ios/ChipperNative.xcodeproj",
      target: "ChipperNative-tvOS",
    )

    increment_version_number_in_plist(
      bump_type: "minor",
      xcodeproj: "ios/ChipperNative.xcodeproj",
      target: "ChipperNative-tvOSTests",
    )
  end

  desc "Finds the latest build number"
  private_lane :build_number do |options|
    build_numbers = []

    build_numbers.append(get_build_number(
      xcodeproj: options[:xcodeproj],
    ).to_i)

    if options.key? "api_key"
      build_numbers.append(latest_testflight_build_number(
        api_key: options[:api_key],
      ).to_i)
    else
      build_numbers.append(latest_testflight_build_number.to_i)
    end

    build_numbers.max
  end

  desc "Sets the build number"
  private_lane :set_build_number do |options|
    increment_build_number(
      build_number: options[:build_number],
      xcodeproj: options[:xcodeproj],
    )
  end

  desc "Build"
  desc "Build & sign the IOS app"
  lane :build do |options|
    setup_semaphore

    if ENV["CI"]
      api_key = api_key(
        key_id: CredentialsManager::AppfileConfig.try_fetch_value(:key_id),
        issuer_id: CredentialsManager::AppfileConfig.try_fetch_value(:issuer_id),
        key_filepath: CredentialsManager::AppfileConfig.try_fetch_value(:key_filepath),
      )
      build_number = build_number(
        xcodeproj: "ios/ChipperNative.xcodeproj",
        target: "ChipperNative",
        api_key: api_key,
      )
    else
      build_number = build_number(
        xcodeproj: "ios/ChipperNative.xcodeproj",
        target: "ChipperNative",
      )
    end

    set_build_number(
      build_number: build_number + 1,
      xcodeproj: "ios/ChipperNative.xcodeproj",
    )

    match(type: "appstore")

    # Build the app using gym
    gym(
      workspace: "./ios/ChipperNative.xcworkspace",
      scheme: "ChipperNative",
      clean: true,
      output_directory: "./",  # Output to root directory to match artifact expectation
      export_method: "app-store",
      export_options: {
        provisioningProfiles: {
          CredentialsManager::AppfileConfig.try_fetch_value(:app_identifier) => "match AppStore #{CredentialsManager::AppfileConfig.try_fetch_value(:app_identifier)}",
        },
      },
      include_bitcode: false,
      include_symbols: true,
      buildlog_path: "./build/logs",  # Keep logs in build directory
      configuration: "Release",
      silent: false,
      disable_xcpretty: true,
      xcargs: "-allowProvisioningUpdates -verbose",
    )

    # Upload to Bugsnag
    upload_symbols_to_bugsnag
    send_build_to_bugsnag(
      config_file: "./ios/ChipperNative/Info.plist",
    )
  end

  desc "Alpha Release"
  desc "Publishes the local build to the alpha channels"
  lane :alphaRelease do |options|
    if ENV["CI"]
      api_key = api_key(
        key_id: CredentialsManager::AppfileConfig.try_fetch_value(:key_id),
        issuer_id: CredentialsManager::AppfileConfig.try_fetch_value(:issuer_id),
        key_filepath: CredentialsManager::AppfileConfig.try_fetch_value(:key_filepath),
      )
      upload_to_testflight(
        app_identifier: CredentialsManager::AppfileConfig.try_fetch_value(:app_identifier),
        api_key: api_key,
        distribute_external: true,
        notify_external_testers: true,
        groups: "Alpha Testers",
        skip_waiting_for_build_processing: false,
        ipa: "./ChipperNative.ipa",
        changelog: "Alpha Build. Thanks for being an Alpha tester! Please report any bugs you find in Slack.",
      )
    else
      upload_to_testflight(
        app_identifier: CredentialsManager::AppfileConfig.try_fetch_value(:app_identifier),
        distribute_external: true,
        groups: "Alpha Testers",
        ipa: "./ChipperNative.ipa",
        changelog: "Alpha Build. Thanks for being an Alpha tester! Please report any bugs you find in Slack.",
      )
    end
  end

  desc "Beta Release"
  desc "Publishes the local build to the beta channels"
  lane :betaRelease do |options|
    api_key = api_key(
      key_id: CredentialsManager::AppfileConfig.try_fetch_value(:key_id),
      issuer_id: CredentialsManager::AppfileConfig.try_fetch_value(:issuer_id),
      key_filepath: CredentialsManager::AppfileConfig.try_fetch_value(:key_filepath),
    )
    upload_to_testflight(
      app_identifier: CredentialsManager::AppfileConfig.try_fetch_value(:app_identifier),
      distribute_external: true,
      api_key: api_key,
      notify_external_testers: true,
      skip_waiting_for_build_processing: false,
      groups: "Beta Testers",
      ipa: "./ChipperNative.ipa",
      changelog: "Release Candidate. Thanks for being a Beta tester, we're testing this builds candidacy for Production. Please report any bugs through the chat or issues board.",
    )
  end
end

platform :android do
  desc "Find the latest version code"
  private_lane :version_code do |options|
    local_version_code = 0

    file = File.new(CredentialsManager::AppfileConfig.try_fetch_value(:properties_file))

    # This iterates through the Gradle Properties file for the Chipper Native app in order to find the current value of `versionCode`
    while (line = file.gets)
      next unless line.include? CredentialsManager::AppfileConfig.try_fetch_value(:version_code_identifier)

      version_components = line.strip.split("=")
      local_version_code = version_components[version_components.length - 1].to_i
      break
    end

    if local_version_code.zero?
      puts "Problem reading the local version code file"
      exit 1
    end

    # This will reach out to Google Play to find the latest version code for the alpha tack
    play_store_alpha_version_codes = google_play_track_version_codes(
      package_name: CredentialsManager::AppfileConfig.try_fetch_value(:app_identifier),
      track: "internal",
      json_key: CredentialsManager::AppfileConfig.try_fetch_value(:json_data),
    )

    # This will reach out to Google Play to find the latest version code for the beta tack
    play_store_beta_version_codes = google_play_track_version_codes(
      package_name: CredentialsManager::AppfileConfig.try_fetch_value(:app_identifier),
      track: "alpha",
      json_key: CredentialsManager::AppfileConfig.try_fetch_value(:json_data),
    )

    # We want to use the max number used in Beta, Alpha or
    # locally for the next version of the application
    version_codes = []
    version_codes.append(local_version_code)
    version_codes.append(*play_store_alpha_version_codes)
    version_codes.append(*play_store_beta_version_codes)

    max_version_code = version_codes.max

    UI.message "LOG: All Version Codes: #{version_codes}"

    UI.message "LOG: Max Version Code: #{max_version_code}"

    max_version_code
  end

  lane :bump_version do |options|
    increment_version_name_in_properties_file(
      key: "android.versionName",
      path: "android/app/gradle.properties",
      update_type: "minor",
    )
  end

  desc "Replaces the version code in the properties file"
  private_lane :set_version_code do |options|
    file_location = CredentialsManager::AppfileConfig.try_fetch_value(:properties_file)
    version_code_identifier = CredentialsManager::AppfileConfig.try_fetch_value(:version_code_identifier)

    # This will substitute the value in the properties file wiht the version to use for the subsequent build
    contents = File.read(file_location)
    modified = contents.gsub(
      /#{version_code_identifier}\=\d+/,
      "#{version_code_identifier}=#{options[:version_code]}"
    )
    File.open(file_location, "w") { |file| file.puts modified }
  end

  desc "Gets the current version name"
  private_lane :version_name do |options|
    version_name = "0"

    file = File.new(options[:properties_file])

    # This iterates through the Gradle Properties file for the Chipper Native app in order to find the current value of `versionName`
    while (line = file.gets)
      next unless line.include? options[:version_name_identifier]

      version_components = line.strip.split("=")
      version_name = version_components[version_components.length - 1]
      break
    end

    if version_name == "0"
      puts "Problem reading the version name file"
      exit 1
    end

    version_name
  end

  desc "Upload Bugsnag Sourcemaps"
  lane :upload_bugsnag_sourcemaps do |options|
    ENV["APP_VERSION"] = options[:app_version]
    ENV["VERSION_NUMBER"] = options[:version_number]
    sh("cd .. && npm run bugsnag-upload-android-source-maps")
  end

  desc "Build"
  desc "Build & sign the Android app"
  lane :build do |options|
    version_name = version_name(
      version_name_identifier: CredentialsManager::AppfileConfig.try_fetch_value(:version_name_identifier),
      properties_file: CredentialsManager::AppfileConfig.try_fetch_value(:properties_file),
    )

    current_version_code = version_code()
    new_version_code = current_version_code + 1

    # This is useful if you want to look up the build on the Play Store
    UI.message "LOG: New Version Code: #{new_version_code}"

    set_version_code(
      version_code: new_version_code,
    )

    gradle(task: "clean", project_dir: "android/")

    # Build the app
    gradle(task: "app:bundle", project_dir: "android/")

    # we generate 4 builds for each architecture in output.versionCodeOverride = versionCodes.get(abi) * 1 + defaultConfig.versionCode
    # so we have to bump the new version code by 4
    send_build_to_bugsnag(
      app_version: version_name,
      android_version_code: (new_version_code + 4).to_s,
    )

    upload_bugsnag_sourcemaps(
      app_version: version_name,
      version_number: (new_version_code + 4).to_s,
    )
  end

  desc "Build Simulator"
  desc "Builds a simulator for testing in a variety of devices"
  lane :buildSimulator do |options|
    # Use the same version code that is currently deployed so that any version code checks on the API work
    current_version_code = version_code()
    set_version_code(
      version_code: current_version_code,
    )

    gradle(task: "clean", project_dir: "android/")

    # Build the app
    gradle(task: "app:assembleDebug", project_dir: "android/")
  end

  desc "Alpha Release"
  desc "Publishes the local build to the alpha channels"
  lane :alphaRelease do |options|
    upload_to_play_store(
      package_name: CredentialsManager::AppfileConfig.try_fetch_value(:app_identifier),
      track: "internal",
      skip_upload_apk: true,
      aab: CredentialsManager::AppfileConfig.try_fetch_value(:build_outputs) + "/" + "app-release.aab",
      json_key: CredentialsManager::AppfileConfig.try_fetch_value(:json_data),
    )
  end

  desc "Beta Release"
  desc "Publishes the local build to the beta channels"
  lane :betaRelease do |options|
    upload_to_play_store(
      package_name: CredentialsManager::AppfileConfig.try_fetch_value(:app_identifier),
      track: "alpha",
      skip_upload_apk: true,
      aab: CredentialsManager::AppfileConfig.try_fetch_value(:build_outputs) + "/" + "app-release.aab",
      json_key: CredentialsManager::AppfileConfig.try_fetch_value(:json_data),
    )
  end
end
